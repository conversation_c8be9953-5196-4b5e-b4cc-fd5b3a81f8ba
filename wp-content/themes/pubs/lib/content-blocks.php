<?php
/**
 * Add 'Heartwood Blocks category
 */
add_filter( 'block_categories_all' , function( $categories ) {

    // Adding a new category.
    array_unshift( $categories, array(
        'slug'  => 'heartwood',
        'title' => 'Heartwood'
    ) );

	return $categories;
} );

/**
 * Register All content blocks
 */
function register_acf_block_types() {

    /**
     * Register content block: Heartwood: Featured content with  image
     */
    acf_register_block_type(array(
        'name'              => 'featured-content',
        'title'             => __('Heartwood: Featured content with image'),
        'description'       => __('Featured content with image, content, CTA buttons'),
        'render_template'   => 'blocks/featured-content.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'featured', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Call To Action
     */
    acf_register_block_type(array(
        'name'              => 'cta',
        'title'             => __('Heartwood: Call To Action'),
        'description'       => __('Call To Action'),
        'render_template'   => 'blocks/cta.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'cta', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Testimonials
     */
    acf_register_block_type(array(
        'name'              => 'testimonials',
        'title'             => __('Heartwood: Testimonials'),
        'description'       => __('Testimonials carousel'),
        'render_template'   => 'blocks/testimonials.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'testimonials', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

     /**
     * Register content block: Heartwood: Case Studies
     */
    acf_register_block_type(array(
        'name'              => 'studies',
        'title'             => __('Heartwood: Case Studies'),
        'description'       => __('Case Studies feed'),
        'render_template'   => 'blocks/case-studies.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'case studies', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Responsive Map
     */
    acf_register_block_type(array(
        'name'              => 'map',
        'title'             => __('Heartwood: Responsive Map'),
        'description'       => __('Responsive Map'),
        'render_template'   => 'blocks/map.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'map', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Accordions
     */
    acf_register_block_type(array(
        'name'              => 'accordions',
        'title'             => __('Heartwood: Accordions'),
        'description'       => __('Accordions'),
        'render_template'   => 'blocks/accordions.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'accordions', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: FAQ accordions by Tag
     */
    acf_register_block_type(array(
        'name'              => 'faqs',
        'title'             => __('Heartwood: FAQ accordions by Tag'),
        'description'       => __('FAQ accordions by Tag'),
        'render_template'   => 'blocks/faqs.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'faq', 'accordions', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Socials
     */
    acf_register_block_type(array(
        'name'              => 'socials',
        'title'             => __('Heartwood: Socials'),
        'description'       => __('Social links'),
        'render_template'   => 'blocks/socials.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'socials', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'edit',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Sitemap
     */
    acf_register_block_type(array(
        'name'              => 'sitemap',
        'title'             => __('Heartwood: Sitemap'),
        'description'       => __('Website Sitemap'),
        'render_template'   => 'blocks/sitemap.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'sitemap', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Location Menus
     */
    acf_register_block_type(array(
        'name'              => 'menus',
        'title'             => __('Heartwood: Location Menus'),
        'description'       => __('Location Menus'),
        'render_template'   => 'blocks/menus.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'menus', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Latest from Blog
     */
    acf_register_block_type(array(
        'name'              => 'latest',
        'title'             => __('Heartwood: Latest from Blog'),
        'description'       => __('Latest from Blog'),
        'render_template'   => 'blocks/latest-blog.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'lates', 'blog', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Latest from Blog
     */
    acf_register_block_type(array(
        'name'              => 'our-rooms',
        'title'             => __('Heartwood: Our Rooms'),
        'description'       => __('Our Rooms'),
        'render_template'   => 'blocks/our-rooms.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'rooms', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Newsletter
     */
    acf_register_block_type(array(
        'name'              => 'newsletter',
        'title'             => __('Heartwood: Newsletter signup'),
        'description'       => __('Newsletter signup'),
        'render_template'   => 'blocks/newsletter.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'newsletter', 'signup', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Contact details
     */
    acf_register_block_type(array(
        'name'              => 'contact',
        'title'             => __('Heartwood: Contact details'),
        'description'       => __('Contact details'),
        'render_template'   => 'blocks/contact.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'contact', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Opening times
     */
    acf_register_block_type(array(
        'name'              => 'times',
        'title'             => __('Heartwood: Opening times'),
        'description'       => __('Opening times'),
        'render_template'   => 'blocks/opening-times.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'opening times', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Booking widget
     */
    acf_register_block_type(array(
        'name'              => 'booking',
        'title'             => __('Heartwood: Booking widget'),
        'description'       => __('Booking widget'),
        'render_template'   => 'blocks/booking-widget.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'Booking widget', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Locations listing
     */
    acf_register_block_type(array(
        'name'              => 'locations',
        'title'             => __('Heartwood: Locations listing'),
        'description'       => __('Locations listing'),
        'render_template'   => 'blocks/locations-listing.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'Locations listing', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Room Features (taxonomy)
     */
    acf_register_block_type(array(
        'name'              => 'room-features',
        'title'             => __('Room Features (taxonomy)'),
        'description'       => __('Room Features (taxonomy)'),
        'render_template'   => 'blocks/room-features.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'Room Features', 'taxonomy', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Rooms: Guestline booking button
     */
    acf_register_block_type(array(
        'name'              => 'room-booking-btn',
        'title'             => __('Rooms: Guestline booking button'),
        'render_template'   => 'blocks/room-booking-btn.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'Room Booking', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Terms & Conditions
    */
    acf_register_block_type(array(
        'name'              => 'terms',
        'title'             => __('Heartwood: Terms & Conditions'),
        'description'       => __('Display all Terms&Conditions field from all posts (offers, events) in form of accordions'),
        'render_template'   => 'blocks/terms.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'terms', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Transparent Video
    */
    acf_register_block_type(array(
        'name'              => 'video',
        'title'             => __('Heartwood: Transparent Video'),
        'description'       => __('Embed a video from your media library or upload a new one. Allows for dual video source to keep Safari browser happy.'),
        'render_template'   => 'blocks/video.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'video', 'transparent', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Accordions
     */
    acf_register_block_type(array(
        'name'              => 'awards',
        'title'             => __('Heartwood: Awards'),
        'description'       => __('Awards feed for current site'),
        'render_template'   => 'blocks/awards.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'awards', 'feed', 'Heartwood Collection' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Features Carousel
     */
    acf_register_block_type(array(
        'name'              => 'features-carousel',
        'title'             => __('Heartwood: Features Carousel'),
        'description'       => __('Features Carousel for current site'),
        'render_template'   => 'blocks/features-carousel.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'features', 'carousel', 'Heartwood Collection' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Features (location)
     */
    acf_register_block_type(array(
        'name'              => 'features-location',
        'title'             => __('Heartwood: Features (location)'),
        'description'       => __('Display listing from: Theme Settings -> Pub Features -> Features (location)'),
        'render_template'   => 'blocks/features-location.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'features', 'location', 'Heartwood Collection' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Swing Sign
     */
    acf_register_block_type(array(
        'name'              => 'swingsign',
        'title'             => __('Heartwood: Swing sign'),
        'description'       => __('Display Swing sign from: Theme Settings, with fallback to video'),
        'render_template'   => 'blocks/swingsign.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'swing', 'sign', 'Heartwood Collection' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Wordmark
     */
    acf_register_block_type(array(
        'name'              => 'wordmark',
        'title'             => __('Heartwood: Wordmark'),
        'description'       => __('Display Wordmark from: Theme Settings'),
        'render_template'   => 'blocks/wordmark.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'wordmark', 'Heartwood Collection' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

}

/**
 * Check if function exists and hook into setup.
 */
if( function_exists('acf_register_block_type') ) {
    add_action('acf/init', 'register_acf_block_types');
}

/**
 * Register ACF blocks (v2)
 */
add_action('init', 'saint_register_block_types');
function saint_register_block_types() {
    register_block_type( get_stylesheet_directory() . '/blocks/responsive-image');
    register_block_type( get_stylesheet_directory() . '/blocks/featured-content');
    register_block_type( get_stylesheet_directory() . '/blocks/carousel');
    register_block_type( get_stylesheet_directory() . '/blocks/carousel-slide');
    register_block_type( get_stylesheet_directory() . '/blocks/bookable-area');
    register_block_type( get_stylesheet_directory() . '/blocks/responsive-embed');
    register_block_type( get_stylesheet_directory() . '/blocks/bookable-area-carousel');
    register_block_type( get_stylesheet_directory() . '/blocks/get-togethers/footer');
}

/**
 * Remove InnerBlocks wrapper for certain blocks
 */
add_filter( 'acf/blocks/wrap_frontend_innerblocks', 'acf_should_wrap_innerblocks', 10, 2 );
function acf_should_wrap_innerblocks( $wrap, $name ) {
    if ( in_array($name, array('acf/carousel', 'acf/carousel-slide') ) ) {
        return false;
    }
    return true;
}