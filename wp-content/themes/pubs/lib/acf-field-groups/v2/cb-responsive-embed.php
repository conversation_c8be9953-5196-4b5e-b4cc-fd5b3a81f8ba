<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_679a01753697e',
	'title' => 'Content Block v2 - Responsive Embed',
	'fields' => array(
		array(
			'key' => 'field_679a01751e9ba',
			'label' => 'Embed URL',
			'name' => 'embed',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'allow_in_bindings' => 0,
			'rows' => 8,
			'placeholder' => '',
			'new_lines' => '',
		),
		array(
			'key' => 'field_679a01ae1e9bb',
			'label' => 'Aspect Ratio',
			'name' => 'ratio',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'9x13' => 'Portrait 9x13',
				'9x16' => 'Portrait 9x16',
				'1x1' => 'Square 1x1',
				'4x3' => 'Landscape 4x3',
				'16x9' => 'Landscape 16x9',
				'21x9' => 'Lanscape 21x9',
			),
			'default_value' => '16x9',
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 0,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'block',
				'operator' => '==',
				'value' => 'acf/responsive-embed',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 0,
	'graphql_field_name' => 'contentBlockV2ResponsiveEmbed',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );