<?php
/**
 * Carousel slide - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'carousel-slide-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-slide'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$inner_blocks_template = array(
    array(
        'acf/responsive-image',
        array(
            'name' => 'acf/responsive-image',
            'data' => array(
                'field_67879d3bebe05' => '',
                'field_67879d68ebe06' => ''
            ),
            'mode' => 'preview'
        ),
        array()
    ),
    array(
        'core/heading',
        array(
            'level' => 3,
            'className' => 'mt-0',
            'content' => 'Heading placeholder'
        ),
        array()
    ),
    array(
        'core/paragraph',
        array(
            'content' => 'Ipsum consectetur purus in lobortis scelerisque a mi posuere in velit suspendisse quisque ultrices scelerisque parturient consectetur a habitasse suspendisse sociosqu magna facilisi porta euismod.'
        ),
        array()
    ),

);

?>
<?php if ( ! $is_preview ) { ?>
    <div data-carousel-slide="true" data-hash="<?= esc_attr($block['anchor']) ?>"
        <?php
        echo wp_kses_data(
            get_block_wrapper_attributes(
                array(
                    'id'    => esc_attr($id),
                    'class' => esc_attr( $class_name ),
                )
            )
        );
        ?>
    >
<?php } ?>

    <InnerBlocks
        template="<?php echo esc_attr( wp_json_encode( $inner_blocks_template ) ); ?>"
    />

<?php if ( ! $is_preview ) { ?>
    </div><!-- .block-slide -->
<?php } ?>