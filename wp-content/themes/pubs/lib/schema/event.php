<?php
if (
    !is_plugin_active( 'wordpress-seo/wp-seo.php' )
    && !is_plugin_active( 'wordpress-seo-premium/wp-seo-premium.php' )
) return false;
/**
 * Class Recipe
 */
class SaintEvent {

    /**
     * A value object with context variables.
     *
     * @var WPSEO_Schema_Context
     */
    public $context;

    /**
     * Recipe constructor.
     *
     * @param WPSEO_Schema_Context $context Value object with context variables.
     */
    public function __construct( WPSEO_Schema_Context $context ) {
        $this->context = $context;
    }

    /**
     * Determines whether or not a piece should be added to the graph.
     *
     * @return bool Whether or not a piece should be added.
     */
    public function is_needed() {

        // We only ever want to output this on 'recipe' category post.
        if ( is_singular( 'post' ) && has_category( 'events' ) ) {
            return true;
        }

        return false;
    }

    /**
     * Adds our Recipe piece of the graph.
     *
     * @return array Recipe Schema markup.
     */
    public function generate() {

        $canonical = $this->context->canonical;
        $post = $this->context->post;
        $post_id = $post->ID;
        $name = get_field('schema_name', $post_id) ?: the_title_attribute( array( 'echo' => false ) );
        $date = get_field( 'schema_start', $post_id ) ? new DateTime(get_field( 'schema_start', $post_id )) : false;
        $start_date = $date ? $date->format(DateTime::ATOM) : false;
        $date = get_field( 'schema_end', $post_id ) ? new DateTime(get_field( 'schema_end', $post_id )) : false;
        $end_date = $date ? $date->format(DateTime::ATOM) : false;
        $desc = get_field('schema_desc', $post_id) ?: false;
        $event_status = get_field('schema_status', $post_id) ?: false;
        $price = get_field('schema_price', $post_id) ?: false;
        $image = get_the_post_thumbnail_url( $post_id, 'medium_large' ) ?: false;

        $pub_name = get_bloginfo( "name" );
        $address = get_field('schema_address', $post_id) ?: get_field('opt_address', 'option');
        $town = get_field('schema_town', $post_id) ?: get_field('opt_town', 'option');
        $county = get_field('schema_county', $post_id) ?: get_field('opt_county', 'option');

        // Set the type.
        $data['@type'] = 'Event';

        // Give it a unique ID, based on the URL and the Post ID.
        $data['@id'] = esc_url($canonical . '#/schema/Event/' . $post_id);

        // Give it a name. (required)
        $data['name'] = $name;

        // Make it the main entity of the webpage we're on.
        $data['mainEntityOfPage'] = [ '@id' => $canonical ];

        // Start Date (required)
        if( $start_date ) $data['startDate'] = $start_date;
        if( $end_date ) $data['endDate'] = $end_date;

        // Description
        if( $desc ) $data['description'] = strip_tags($desc);

        // Status
        if( $event_status ) $data['eventStatus'] = "https://schema.org/" . $event_status;

        // Price
        if( $price ) $data['offers'] = [
            '@type' => 'Offer',
            'price' => $price,
            'priceCurrency' => 'GBP'
        ];

        // Image
        if( $image ) {
            $data['image'] = [
                esc_url( $image )
            ];
        }

        // Location
        $data['location'] = [
            '@type' => 'Place',
            'name' => $pub_name,
            'address' => [
                '@type' => 'PostalAddress',
                'streetAddress' => $address,
                'addressLocality' => $town,
                'addressRegion' => $county,
                'addressCountry' => 'UK'
            ]
        ];

        $data['organizer'] = [
            '@type' => 'Organization',
            'name' => $pub_name
        ];

        return $data;
    }
}