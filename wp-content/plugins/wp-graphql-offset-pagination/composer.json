{"name": "valu/wp-graphql-offset-pagination", "description": "Add offset pagination to wp-graphql", "type": "wordpress-plugin", "license": "GPL-2.0-or-later", "authors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "developer"}], "require-dev": {"valu/wp-testing-tools": "^0.4.0", "lucatume/wp-browser": "^2.2", "phpunit/phpunit": "^8.0", "codeception/module-asserts": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-rest": "^1.0", "codeception/module-cli": "^1.0", "codeception/util-universalframework": "^1.0"}, "autoload": {"psr-4": {"WPGraphQL\\Extensions\\OffsetPagination\\": "src/"}}, "scripts": {"wp-install": "wp-install --full --env-file .env --wp-composer-file composer.wp-install.json", "wpunit": "codecept run wpunit", "functional": "codecept run functional", "test": ["@wpunit", "@functional"]}, "config": {"optimize-autoloader": true}, "support": {"issues": "https://github.com/valu-digital/wp-graphql-offset-pagination/issues", "source": "https://github.com/valu-digital/wp-graphql-offset-pagination"}}