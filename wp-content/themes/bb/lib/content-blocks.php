<?php
/**
 * Add 'BB Blocks category
 */
add_filter( 'block_categories_all' , function( $categories ) {

    // Adding a new category.
    array_unshift( $categories, array(
        'slug'  => 'bb',
        'title' => 'Brasserie Blanc'
    ) );

	return $categories;
} );

/**
 * Register All content blocks
 */
function register_acf_block_types() {

    /**
     * Register content block: BB: Featured content with  image
     */
    acf_register_block_type(array(
        'name'              => 'featured-content',
        'title'             => __('BB: Featured content with image'),
        'description'       => __('Featured content with image, content, CTA buttons'),
        'render_template'   => 'blocks/featured-content.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'featured', 'content', 'BB' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Call To Action
     */
    acf_register_block_type(array(
        'name'              => 'cta',
        'title'             => __('BB: Call To Action'),
        'description'       => __('Call To Action'),
        'render_template'   => 'blocks/cta.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'cta', 'content', 'BB' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Testimonials
     */
    acf_register_block_type(array(
        'name'              => 'testimonials',
        'title'             => __('BB: Testimonials'),
        'description'       => __('Testimonials carousel'),
        'render_template'   => 'blocks/testimonials.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'testimonials', 'content', 'BB' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

     /**
     * Register content block: BB: Case Studies
     */
    acf_register_block_type(array(
        'name'              => 'studies',
        'title'             => __('BB: Case Studies'),
        'description'       => __('Case Studies feed'),
        'render_template'   => 'blocks/case-studies.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'case studies', 'content', 'BB' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Responsive Map
     */
    acf_register_block_type(array(
        'name'              => 'map',
        'title'             => __('BB: Responsive Map'),
        'description'       => __('Responsive Map'),
        'render_template'   => 'blocks/map.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'map', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Accordions
     */
    acf_register_block_type(array(
        'name'              => 'accordions',
        'title'             => __('BB: Accordions'),
        'description'       => __('Accordions'),
        'render_template'   => 'blocks/accordions.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'accordions', 'content', 'BB' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: FAQ accordions by Tag
     */
    acf_register_block_type(array(
        'name'              => 'faqs',
        'title'             => __('BB: FAQ accordions by Tag'),
        'description'       => __('FAQ accordions by Tag'),
        'render_template'   => 'blocks/faqs.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'faq', 'accordions', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Socials
     */
    acf_register_block_type(array(
        'name'              => 'socials',
        'title'             => __('BB: Socials'),
        'description'       => __('Social links'),
        'render_template'   => 'blocks/socials.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'socials', 'content', 'BB' ),
        'mode' => 'edit',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Sitemap
     */
    acf_register_block_type(array(
        'name'              => 'sitemap',
        'title'             => __('BB: Sitemap'),
        'description'       => __('Website Sitemap'),
        'render_template'   => 'blocks/sitemap.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'sitemap', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Location Menus
     */
    acf_register_block_type(array(
        'name'              => 'menus',
        'title'             => __('BB: Location Menus'),
        'description'       => __('Location Menus'),
        'render_template'   => 'blocks/menus.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'menus', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Latest from Blog
     */
    acf_register_block_type(array(
        'name'              => 'latest',
        'title'             => __('BB: Latest from Blog'),
        'description'       => __('Latest from Blog'),
        'render_template'   => 'blocks/latest-blog.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'lates', 'blog', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Latest from Blog
     */
    acf_register_block_type(array(
        'name'              => 'our-rooms',
        'title'             => __('BB: Our Rooms'),
        'description'       => __('Our Rooms'),
        'render_template'   => 'blocks/our-rooms.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'rooms', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Newsletter
     */
    acf_register_block_type(array(
        'name'              => 'newsletter',
        'title'             => __('BB: Newsletter signup'),
        'description'       => __('Newsletter signup'),
        'render_template'   => 'blocks/newsletter.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'newsletter', 'signup', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Contact details
     */
    acf_register_block_type(array(
        'name'              => 'contact',
        'title'             => __('BB: Contact details'),
        'description'       => __('Contact details'),
        'render_template'   => 'blocks/contact.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'contact', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Opening times
     */
    acf_register_block_type(array(
        'name'              => 'times',
        'title'             => __('BB: Opening times'),
        'description'       => __('Opening times'),
        'render_template'   => 'blocks/opening-times.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'opening times', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Booking widget
     */
    acf_register_block_type(array(
        'name'              => 'booking',
        'title'             => __('BB: Booking widget'),
        'description'       => __('Booking widget'),
        'render_template'   => 'blocks/booking-widget.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'Booking widget', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Locations listing
     */
    acf_register_block_type(array(
        'name'              => 'locations',
        'title'             => __('BB: Locations listing'),
        'description'       => __('Locations listing'),
        'render_template'   => 'blocks/locations-listing.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'Locations listing', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Room Features (taxonomy)
     */
    acf_register_block_type(array(
        'name'              => 'room-features',
        'title'             => __('Room Features (taxonomy)'),
        'description'       => __('Room Features (taxonomy)'),
        'render_template'   => 'blocks/room-features.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'Room Features', 'taxonomy', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Rooms: Guestline booking button
     */
    acf_register_block_type(array(
        'name'              => 'room-booking-btn',
        'title'             => __('Rooms: Guestline booking button'),
        'render_template'   => 'blocks/room-booking-btn.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'Room Booking', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Terms & Conditions
    */
    acf_register_block_type(array(
        'name'              => 'terms',
        'title'             => __('BB: Terms & Conditions'),
        'description'       => __('Display all Terms&Conditions field from all posts (offers, events) in form of accordions'),
        'render_template'   => 'blocks/terms.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'terms', 'content', 'BB' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Transparent Video
    */
    acf_register_block_type(array(
        'name'              => 'video',
        'title'             => __('BB: Transparent Video'),
        'description'       => __('Embed a video from your media library or upload a new one. Allows for dual video source to keep Safari browser happy.'),
        'render_template'   => 'blocks/video.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'video', 'transparent', 'content', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Accordions
     */
    acf_register_block_type(array(
        'name'              => 'awards',
        'title'             => __('BB: Awards'),
        'description'       => __('Awards feed for current site'),
        'render_template'   => 'blocks/awards.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'awards', 'feed', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Features Carousel
     */
    acf_register_block_type(array(
        'name'              => 'features-carousel',
        'title'             => __('BB: Features Carousel'),
        'description'       => __('Features Carousel for current site'),
        'render_template'   => 'blocks/features-carousel.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'features', 'carousel', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Features (location)
     */
    acf_register_block_type(array(
        'name'              => 'features-location',
        'title'             => __('BB: Features (location)'),
        'description'       => __('Display listing from: Theme Settings -> Pub Features -> Features (location)'),
        'render_template'   => 'blocks/features-location.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'features', 'location', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Swing Sign
     */
    acf_register_block_type(array(
        'name'              => 'swingsign',
        'title'             => __('BB: Swing sign'),
        'description'       => __('Display Swing sign from: Theme Settings, with fallback to video'),
        'render_template'   => 'blocks/swingsign.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'swing', 'sign', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: BB: Wordmark
     */
    acf_register_block_type(array(
        'name'              => 'wordmark',
        'title'             => __('BB: Wordmark'),
        'description'       => __('Display Wordmark from: Theme Settings'),
        'render_template'   => 'blocks/wordmark.php',
        'category'          => 'bb',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'wordmark', 'BB' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

}

/**
 * Check if function exists and hook into setup.
 */
if( function_exists('acf_register_block_type') ) {
    add_action('acf/init', 'register_acf_block_types');
}

/**
 * Register ACF blocks (v2)
 */
add_action('init', 'saint_register_block_types');
function saint_register_block_types() {
    register_block_type( get_stylesheet_directory() . '/blocks/responsive-image');
    register_block_type( get_stylesheet_directory() . '/blocks/featured-content');
    register_block_type( get_stylesheet_directory() . '/blocks/carousel');
    register_block_type( get_stylesheet_directory() . '/blocks/carousel-slide');
    register_block_type( get_stylesheet_directory() . '/blocks/bookable-area');
    register_block_type( get_stylesheet_directory() . '/blocks/responsive-embed');
    register_block_type( get_stylesheet_directory() . '/blocks/bookable-area-carousel');
    register_block_type( get_stylesheet_directory() . '/blocks/get-togethers/footer');
}

/**
 * Remove InnerBlocks wrapper for certain blocks
 */
add_filter( 'acf/blocks/wrap_frontend_innerblocks', 'acf_should_wrap_innerblocks', 10, 2 );
function acf_should_wrap_innerblocks( $wrap, $name ) {
    if ( in_array($name, array('acf/carousel', 'acf/carousel-slide') ) ) {
        return false;
    }
    return true;
}