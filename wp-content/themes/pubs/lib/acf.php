<?php
/**
 * Register Site Options page
 */
add_action( 'acf/init', function(){
	if( function_exists('acf_add_options_page') ) {

		acf_add_options_page(array(
			'page_title' 	=> 'Heartwood Settings',
			'menu_title'	=> 'Heartwood Settings',
			'menu_slug' 	=> 'heartwood-settings',
			'capability'	=> 'edit_theme_settings',
			'redirect'		=> false,
			'show_in_graphql' => true,
		));

	}
} );

add_action( 'acf/init', function() {
	acf_add_options_page( array(
		'page_title' => 'Heartwood SEO',
		'menu_slug' => 'heartwood-seo',
		'parent_slug' => 'wpseo_dashboard',
		'position' => '',
		'redirect' => false,
		'capability' => 'wpseo_manage_options',
	) );
} );



/** ==================================
 * ACF export
 * taken from Heartwood Inns DEV site, here:
 * https://hwc-cms.wearetesting.co.uk/pubs/wp-admin/edit.php?post_type=acf-field-group
 *  ================================== */

// === Gutenberg Content Block
require_once __DIR__.'/acf-field-groups/cb-accordions.php';
require_once __DIR__.'/acf-field-groups/cb-booking-widget.php';
require_once __DIR__.'/acf-field-groups/cb-case-studies.php';
require_once __DIR__.'/acf-field-groups/cb-contact.php';
require_once __DIR__.'/acf-field-groups/cb-cta.php';
require_once __DIR__.'/acf-field-groups/cb-featured-content.php';
require_once __DIR__.'/acf-field-groups/cb-socials.php';
require_once __DIR__.'/acf-field-groups/cb-testimonials.php';
require_once __DIR__.'/acf-field-groups/cb-room-booking-cta.php';
require_once __DIR__.'/acf-field-groups/cb-latest-blog.php';
require_once __DIR__.'/acf-field-groups/cb-our-rooms.php';
require_once __DIR__.'/acf-field-groups/cb-location-listing.php';
require_once __DIR__.'/acf-field-groups/cb-video.php';
require_once __DIR__.'/acf-field-groups/cb-awards.php';
require_once __DIR__.'/acf-field-groups/cb-newsletter.php';
require_once __DIR__.'/acf-field-groups/cb-opening-times.php';

// === Gutenberg Content Block v2
require_once __DIR__.'/acf-field-groups/v2/cb-featured-content.php';
require_once __DIR__.'/acf-field-groups/v2/cb-responsive-image.php';
require_once __DIR__.'/acf-field-groups/v2/cb-carousel.php';
require_once __DIR__.'/acf-field-groups/v2/cb-bookable-area.php';
require_once __DIR__.'/acf-field-groups/v2/cb-responsive-embed.php';
require_once __DIR__.'/acf-field-groups/v2/cb-gettogethers-footer.php';



// === General
require_once __DIR__.'/acf-field-groups/page-options.php';
require_once __DIR__.'/acf-field-groups/hero-content.php';
require_once __DIR__.'/acf-field-groups/promo-popup.php';
require_once __DIR__.'/acf-field-groups/recipe.php';

// === CPT related
require_once __DIR__.'/acf-field-groups/single-post.php';
require_once __DIR__.'/acf-field-groups/single-menu.php';
require_once __DIR__.'/acf-field-groups/single-room.php';
require_once __DIR__.'/acf-field-groups/single-faq.php';
require_once __DIR__.'/acf-field-groups/single-area.php';
require_once __DIR__.'/acf-field-groups/testimonial.php';
require_once __DIR__.'/acf-field-groups/event-schema.php';

// === Theme Settigns related
require_once __DIR__.'/acf-field-groups/opt-general.php';
require_once __DIR__.'/acf-field-groups/opt-booking.php';
require_once __DIR__.'/acf-field-groups/opt-rooms.php';

// === SEO: Robots
require_once __DIR__.'/acf-field-groups/seo-robots.php';

/**
 * Pre-fill some of the ACF fields
 * WP admin only
 */
if( is_admin() ) {
	add_filter('acf/load_value/name=room-btn-rooms', function ( $value, $post_id, $field ) {
		global $post;
		$post_id = $post->ID;
		$default_val = get_field('guestline_room_id', $post_id);
		if( empty($value) && $default_val ) $value = $default_val;
		return $value;
	}, 10, 3);
}

add_action('after_setup_theme', function(){

});

if( !is_admin() ) {
	add_filter( 'acf/load_value/key=field_67b872c2ab925',function ($value, $post_id, $field) {
		$source_html = $value ? ' origin="'.$value.'"' : '';
		return do_shortcode('[newsletter_form type="standalone"'.$source_html.' layout="short" disable_booking]');
	}, 10, 3 );
}