<?php
namespace Saint;

/**
 * Add <body> classes
 */
function body_class($classes) {
  // Add page slug if it doesn't exist
  if (is_single() || is_page() && !is_front_page()) {
    if (!in_array(basename(get_permalink()), $classes)) {
      $classes[] = basename(get_permalink());
    }
  }

  return $classes;
}
add_filter('body_class', __NAMESPACE__ . '\\body_class');

/**
 * Clean up the_excerpt()
 */
function excerpt_more() {
  // return ' &hellip; <a href="' . get_permalink() . '">' . __('Continued', 'sage') . '</a>';
}
add_filter('excerpt_more', __NAMESPACE__ . '\\excerpt_more');

// allow to upload SVG files
function custom_mime_types( $mimes ){
  $mimes['svg'] = 'image/svg+xml';
  return $mimes;
}
add_filter('upload_mimes', __NAMESPACE__ . '\\custom_mime_types');

function fix_svg_thumb_display() {
  echo '<style type="text/css">
    table.media .media-icon img[src$=".svg"], img[src$=".svg"].attachment-post-thumbnail {
      width: 100% !important;
      height: auto !important;
    }
  </style>';
}
add_action('admin_head', __NAMESPACE__ . '\\fix_svg_thumb_display');

/**
 * To allow users with the roles editor and administrator who can edit pages (in single and also multisite instances)
 * to edit and delete the privacy policy page
 */
add_filter('map_meta_cap', function ($caps, $cap, $user_id, $args)
{
  if (!is_user_logged_in()) return $caps;

  $user_meta = get_userdata($user_id);
  if ($user_meta->roles && array_intersect(['editor', 'administrator'], $user_meta->roles)) {
    if ('manage_privacy_options' === $cap) {
      $manage_name = is_multisite() ? 'manage_network' : 'manage_options';
      $caps = array_diff($caps, [ $manage_name ]);
    }
  }
  return $caps;
}, 1, 4);

/**
 * WPGraphQL: register broadcastedUrl
 */
add_action( 'graphql_register_types', function() {
  if( function_exists('ThreeWP_Broadcast') ) {
    $post_types = ['Post', 'Page'];
    foreach( $post_types as $type ) {
      register_graphql_field( $type, 'broadcastedUrl', [
        'type' => 'String',
        'description' => __( 'Broadcasted URL', 'wp-graphql' ),
        'resolve' => function( $post ) {
          $api = ThreeWP_Broadcast()->api();
          $linking = $api->linking( get_current_blog_id(), $post->ID );
          $parent = $linking->parent();
          if( isset( $parent['post_id'] ) && !empty( $parent['post_id'] ) ) {
            $id = $parent['post_id'];
            switch_to_blog( $parent['blog_id'] );
            $url = get_permalink( $parent['post_id'] );
            restore_current_blog();
          }
          return ! empty( $url ) ? $url : null;
        }
     ] );
    }
  }
} );

add_action( 'init', function(){
  /**
   * WPGraphQL: register guestlineButton
   */
  add_action( 'graphql_register_types', function() {
    register_graphql_field( 'RootQuery', 'guestlineButtonData', [
      'type' => 'String',
      'description' => __( 'Guestline button', 'wp-graphql' ),
      'resolve' => function() {
        return GuestlineButtonData();
      }
  ] );
  } );

  /**
   * WPGraphQL: register siteID
   */
  add_action( 'graphql_register_types', function() {
    register_graphql_field( 'RootQuery', 'siteID', [
      'type' => 'String',
      'description' => __( 'Site ID', 'wp-graphql' ),
      'resolve' => function() {
        return get_current_blog_id();
      }
  ] );
  } );

  /**
  * WPGraphQL: register postPassword
  */
  add_action( 'graphql_register_types', function() {
    $post_type = ['Page', 'Post', 'Room'];
    foreach($post_type as $type) {
      register_graphql_field( $type, 'postPassword', [
        'type' => 'String',
        'description' => __( $type.' Password', 'sage' ),
        'resolve' => function( $post, $args, $context, $info ) {
          $page = get_post($post->ID);
          if(!$page) return null;
          $pass = $page->post_password;
          return $pass ?: null;
        }
    ]);
    }
  });
}, 20 );

/* Remove Images From Yoast Sitemap */
add_filter( 'wpseo_xml_sitemap_img', '__return_false' );
/** Disable SearchAction output from Yoast Schema */
add_filter( 'disable_wpseo_json_ld_search', '__return_true' );

/**
 * Display backend notice for DEV cms only
 */
add_action( 'admin_notices', function() {
  if(SAINT_ENV == "DEV"):
  ?>
  <div class="notice notice-alt notice-warning"><p>Hello folks! You're in DEVELOPMENT cms. If you want to edit PRODUCTION content then please follow this <a href="https://cms.heartwoodcollection.com/wp-admin/" target="_blank">link</a>.</p></div>
  <?php
  endif;
} );

// Add the custom columns to the book post type:
add_filter( 'manage_faq_posts_columns', function($columns) {
  unset( $columns['author'] );
  $columns['faq_hide_master'] = __( 'Hide on Master Inns site', 'sage' );

  return $columns;
} );

// Add the data to the custom columns for the book post type:
add_action( 'manage_faq_posts_custom_column' , function( $column, $post_id ) {
  switch ( $column ) {

      case 'faq_hide_master' :
          echo get_post_meta( $post_id , 'faq_hide_master' , true ) ? 'yes' : '';
          break;

  }
}, 10, 2 );

/**
 * Add Recipe to Yoast Schema output, ha
 */
if (
  is_plugin_active( 'wordpress-seo/wp-seo.php' )
  || is_plugin_active( 'wordpress-seo-premium/wp-seo-premium.php' )
) {
  add_filter( 'wpseo_schema_graph_pieces', function ( $pieces, $context ) {
    $pieces[] = new \SaintRecipe( $context );
    return $pieces;
  }, 11, 2 );
  add_filter( 'wpseo_schema_graph_pieces', function ( $pieces, $context ) {
    $pieces[] = new \SaintFaqs( $context );
    return $pieces;
  }, 11, 2 );
  add_filter( 'wpseo_schema_graph_pieces', function ( $pieces, $context ) {
    $pieces[] = new \SaintEvent( $context );
    return $pieces;
  }, 11, 2 );
}

/**
 * Add Query for blog posts
 */
add_action( 'graphql_register_types', function() {

	// This registers a connection to the Schema at the root of the Graph
	// The connection field name is "archivePosts"
	register_graphql_connection( [
		'fromType'           => 'RootQuery',
		'toType'             => 'Post',
		'fromFieldName'      => 'archivePosts', // This is the field name that will be exposed in the Schema to query this connection by
		'connectionTypeName' => 'RootQueryToArchivePostsConnection',
		'connectionArgs'     => \WPGraphQL\Connection\PostObjects::get_connection_args(), // This adds Post connection args to the connection
		'resolve'            => function( $root, $args, \WPGraphQL\AppContext $context, $info ) {

			$resolver = new \WPGraphQL\Data\Connection\PostObjectConnectionResolver( $root, $args, $context, $info );

			// Note, these args will override anything the user passes in as { where: { ... } } args in the GraphQL Query
      $resolver->set_query_arg('post_type', 'post');
      if( isset($args['first']) && !empty($args['first']) ) $resolver->set_query_arg('posts_per_page', $args['first']);
      if( isset($args['after']) && !empty($args['after']) ) $resolver->set_query_arg('offset', $args['after']);
      $resolver->set_query_arg('meta_query', array(
        array(
          'relation' => 'AND',
          array(
            'key' => 'expiry_date',
            'value' => date("Y-m-d"),
            'compare' => '<',
            'type' => 'DATETIME',
          ),
          array(
            'key' => 'expiry_date',
            'value' => null,
            'compare' => '!='
          )
        )
      ));

			return $resolver->get_connection();
		}
	] );

  // This registers a connection to the Schema at the root of the Graph
	// The connection field name is "filteredPosts"
	register_graphql_connection( [
		'fromType'           => 'RootQuery',
		'toType'             => 'Post',
		'fromFieldName'      => 'filteredPosts', // This is the field name that will be exposed in the Schema to query this connection by
		'connectionTypeName' => 'RootQueryToFilteredPostsConnection',
		'connectionArgs'     => \WPGraphQL\Connection\PostObjects::get_connection_args(), // This adds Post connection args to the connection
		'resolve'            => function( $root, $args, \WPGraphQL\AppContext $context, $info ) {

			$resolver = new \WPGraphQL\Data\Connection\PostObjectConnectionResolver( $root, $args, $context, $info );

			// Note, these args will override anything the user passes in as { where: { ... } } args in the GraphQL Query
      $resolver->set_query_arg('post_type', 'post');
      if( isset($args['first']) && !empty($args['first']) ) $resolver->set_query_arg('posts_per_page', $args['first']);
      if( isset($args['after']) && !empty($args['after']) ) $resolver->set_query_arg('offset', $args['after']);
      if( isset($args['before']) && !empty($args['before']) ) $resolver->set_query_arg('category_name', $args['before']); // proxy for category name
      $resolver->set_query_arg('meta_query', array(
        array(
          'relation' => 'OR',
          array(
            'key' => 'expiry_date',
            'value' => date("Y-m-d"),
            'compare' => '>',
            'type' => 'DATETIME',
          ),
          array(
            'key' => 'expiry_date',
            'compare' => 'NOT EXISTS'
          ),
          array(
            'key' => 'expiry_date',
            'value' => null,
            'compare' => '='
          )
        )
      ));

			return $resolver->get_connection();
		}
	] );

},99 );

// Remove noindex pages (Yoast setting) from get_pages() function
add_filter( 'get_pages', function ( $pages, $args ) {
	if( array_key_exists( 'walker', $args ) ) {
		foreach ( $pages as $key => $item ) {
			$_yoast_wpseo_meta_robots_noindex = get_post_meta( $item->ID, '_yoast_wpseo_meta-robots-noindex', true );
			if( $_yoast_wpseo_meta_robots_noindex == 1 ) unset( $pages[$key] );
		}
	}
	return $pages;
}, 10, 2 );

// Remove boradcaste posts from XML Sitemap
add_filter( 'wpseo_sitemap_entry', function($url, $type, $post){
  if( function_exists('ThreeWP_Broadcast') ) {
    $api = ThreeWP_Broadcast()->api();
    $linking = $api->linking( get_current_blog_id(), $post->ID );
    $parent = $linking->parent();
    if( isset( $parent['post_id'] ) && !empty( $parent['post_id'] ) ) return false;
  }
  return $url;
}, 1, 3 );


// Allow custom shortcodes in CF7 HTML form
add_filter( 'wpcf7_form_elements', function ( $form ) {
  $form = do_shortcode( $form );
  return $form;
});

add_action( 'wpcf7_before_send_mail', function( $WPCF7_ContactForm ) {
    $submission = \WPCF7_Submission::get_instance();
    if ( $submission ){
      $posted_data = $submission->get_posted_data();
      if ( empty( $posted_data ) ){ return; }
      // --- Adjust only specific forms
      if( isset($posted_data['recipient-location']) && !empty($posted_data['recipient-location']) ) {
        // get mail property
        $mail = $WPCF7_ContactForm->prop( 'mail' ); // returns array
        $form_subject = $_POST['your-subject'];
        $form_subject__val = $posted_data['your-subject'][0];
        $recipient_location = explode('|',$posted_data['recipient-location']);
        $site_id = $recipient_location[0];
        $site_name = $recipient_location[1];
        $site_email = isset($recipient_location[2]) && !empty($recipient_location[2]) ? $recipient_location[2] : false;
        $mail['body'] = str_replace('[recipient-location]',"$site_name\n", $mail['body']);
        if( $site_id != 3 && $site_email && in_array($form_subject, array('contact', 'other')) )  {
          // $mail['body'] .= "\n Location email: $site_email";
          $mail['recipient'] = $site_email;
        }
        // foreach ($_POST as $key => $value) {
        //   $mail['body'] .= "Key: $key; Value: $value, \n";
        // }
        // $mail['body'] .= "\n";
        // foreach ($posted_data as $key => $value) {
        //   $mail['body'] .= "Key: $key; Value: ".print_r($value, true).", \n";
        // }
        $WPCF7_ContactForm->set_properties( array( 'mail' => $mail ) );
        return $WPCF7_ContactForm;
      }
    }
}, 10);

/**
 * Replace tags with CMS driven data
 */
function tags_replacement($content) {
  $pub_name = get_bloginfo( 'name' );
  if( $pub_name ) $content = str_replace('{{pubname}}', $pub_name, $content);
  $location = get_field('opt_town', 'option') ?: false;
  if( $location ) $content = str_replace('{{publocation}}', $location, $content);
  $phone = get_field('opt_phone', 'option') ?: false;
  if( $phone ) $content = str_replace('{{phone}}', $phone, $content);
  $email = get_field('opt_email', 'option') ?: false;
  if( $email ) $content = str_replace('{{email}}', $email, $content);
  $address = get_field('opt_address', 'option') ?: false;
  if( $address ) $content = str_replace('{{address_single}}', $address, $content);
  $address_multi = get_field('opt_address_multiline', 'option') ?: false;
  if( $address_multi ) $content = str_replace('{{address_multi}}', $address_multi, $content);
  $directions  = get_field('opt_directions', 'option') ?: false;
  if( $directions ) {
    $parts = parse_url($directions);
    $cleanUrl = $parts['host'] . ($parts['path'] ?? '');
    $content = str_replace('{{directions}}', $cleanUrl, $content);
  }
  $hotel_id = get_field('opt_hotel_id', 'option') ?: false;
  if( $hotel_id ) $content = str_replace('{{hotel_id}}', $hotel_id, $content);
  $add_info = get_field('opt_additional_info', 'option') ?: false;
  if( $add_info ) $content = str_replace('{{additional_information}}', $add_info, $content);
  $parking = get_field('opt_parking', 'option') ?: false;
  if( $parking ) $content = str_replace('{{parking_info}}', $parking, $content);
  $transport = get_field('opt_transport', 'option') ?: false;
  if( $transport ) $content = str_replace('{{public_transport_info}}', $transport, $content);
  // --- Social ahndles from Theme Settings -> "social channels"
  $socials = get_field('opt_socials', 'option') ?: false;
  if( $socials ) {
    foreach ($socials as $social) {
      $handle = basename($social['link']);
      if( $handle ) {
        $provider = $social['provider'];
        $content = str_replace("{{socials-$provider}}", $handle, $content);
      }
    }
  }
  return $content;
}

// Replace tags in post title
add_filter('the_title', __NAMESPACE__ . '\\tags_replacement');
// Replace tags in post content
add_filter('the_content', __NAMESPACE__ . '\\tags_replacement');
// Replace tags in post excerpt
add_filter('the_excerpt', __NAMESPACE__ . '\\tags_replacement');
// Replace tags in Yoast meta title
add_filter('wpseo_title', __NAMESPACE__ . '\\tags_replacement');
add_filter('wpseo_opengraph_title', __NAMESPACE__ . '\\tags_replacement');
// Replace tags in Yoast meta description
add_filter('wpseo_metadesc', __NAMESPACE__ . '\\tags_replacement');
add_filter('wpseo_opengraph_desc', __NAMESPACE__ . '\\tags_replacement');
// Replace tags in Yoast Schema for WebPage
add_filter( 'wpseo_schema_webpage', function( $data ) {
  foreach ( $data as $key => $value ) {
      if( in_array($key, array('description', 'name')) ) {
        $data[$key] = tags_replacement($value);
      }
  }
  return $data;
}, 10, 2 );

// Replace tags in Nav menu items
add_filter('wp_setup_nav_menu_item', function($item) {
  $item->title = tags_replacement($item->title);
  return $item;
});

// Replace tags in Hero fields
if( !is_admin() ){
  add_filter('acf/load_value/name=hp_title', __NAMESPACE__ . '\\tags_replacement', 10, 3);
  add_filter('acf/load_value/name=hp_content', __NAMESPACE__ . '\\tags_replacement', 10, 3);
  add_filter('acf/load_value/name=menu_custom_title', __NAMESPACE__ . '\\tags_replacement', 10, 3);
}