<?php
namespace Saint;

function create_post_type() {

    $labels = array(
		'name'                  => _x( 'Case Studies', 'Post Type General Name', 'sage' ),
		'singular_name'         => _x( 'Case Study', 'Post Type Singular Name', 'sage' ),
		'menu_name'             => __( 'Case Studies', 'sage' ),
		'name_admin_bar'        => __( 'Case Study', 'sage' ),
	);
	$args = array(
		'label'                 => __( 'Case Study', 'sage' ),
		'description'           => __( 'Case Study', 'sage' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'editor', 'thumbnail', 'revisions' ),
		'taxonomies'            => array( 'category' ),
		'hierarchical'          => false,
		'public'                => true,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-welcome-view-site',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => false,
		'exclude_from_search'   => true,
		'publicly_queryable'    => false,
		'capability_type'       => 'post',
	);
	register_post_type( 'case', $args );

    $labels = array(
		'name'                  => _x( 'Testimonials', 'Post Type General Name', 'sage' ),
		'singular_name'         => _x( 'Testimonial', 'Post Type Singular Name', 'sage' ),
		'menu_name'             => __( 'Testimonials', 'sage' ),
		'name_admin_bar'        => __( 'Testimonial', 'sage' ),
	);
	$args = array(
		'label'                 => __( 'Testimonial', 'sage' ),
		'description'           => __( 'Testimonial', 'sage' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'thumbnail' ),
		'hierarchical'          => false,
		'public'                => true,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-megaphone',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => false,
		'exclude_from_search'   => true,
		'publicly_queryable'    => false,
		'capability_type'       => 'post',
	);
	register_post_type( 'testimonial', $args );

}
add_action('init', __NAMESPACE__ . '\\create_post_type');
