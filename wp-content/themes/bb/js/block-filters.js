/**
 * Boostrap blocks \ Button \ styles
 */
function myButtonStyleOptions( styleOptions ) {
	styleOptions[0].bgColor = '#7c8552';
	styleOptions[0].textColor = '#fff7f1';
	styleOptions[1].bgColor = '#E5CCBD';
	styleOptions[1].textColor = '#1d1d1b';

	styleOptions.push( {
		label: 'Outline primary',
		value: 'outline-primary',
		bgColor: 'transparent',
		textColor: '#7c8552',
		borderColor: '#7c8552',
		borderWidth: '1px',
		borderStyle: 'solid'
	} );
	styleOptions.push( {
		label: 'Outline dusk',
		value: 'outline-dusk',
		bgColor: 'transparent',
		textColor: '#1d1d1b',
	} );
	styleOptions.push( {
		label: 'Red mid',
		value: 'redmid',
		bgColor: '#992B43',
		textColor: '#E5CCBD',
	} );
	styleOptions.push( {
		label: 'Red light',
		value: 'redlight',
		bgColor: '#BB5A6A',
		textColor: '#E5CCBD',
	} );
	console.log(styleOptions);
	return styleOptions;
}
wp.hooks.addFilter(
	'wpBootstrapBlocks.button.styleOptions',
	'heartwood/wp-bootstrap-blocks/button/styleOptions',
	myButtonStyleOptions
);