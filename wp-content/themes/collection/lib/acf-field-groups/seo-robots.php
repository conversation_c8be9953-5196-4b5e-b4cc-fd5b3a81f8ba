<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_660d1f43132b8',
	'title' => 'SEO - Robots',
	'fields' => array(
		array(
			'key' => 'field_660d1f43b7cc1',
			'label' => 'Robots.txt',
			'name' => 'opt_robots',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'rows' => '',
			'placeholder' => '',
			'new_lines' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'options_page',
				'operator' => '==',
				'value' => 'heartwood-seo',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 1,
	'graphql_field_name' => 'seoRobots',
	'map_graphql_types_from_location_rules' => 1,
	'graphql_types' => array(
		0 => 'HeartwoodSettings',
	),
) );
} );
