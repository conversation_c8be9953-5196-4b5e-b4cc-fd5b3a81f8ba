<?php
/**
 * Get Inns master site (heartwoodcollection.com)
 */
function is_inns_master_site() {
    return get_current_blog_id() == 3 ? true : false;
}

/**
 * Reaplce elements in non-associative array
 */
function SaintMoveKeyBefore($arr, $find, $move) {
    if (!isset($arr[$find], $arr[$move])) {
        return $arr;
    }

    $elem = [$arr[$move]];  // cache the element to be moved
    unset($arr[$move]);
    $start = array_splice($arr, 0, $find);
    unset($start[$move]);  // only important if $move is in $start
    return array_merge($start, $elem, $arr);
}

/**
 * Populate select dropdown with years
 */
function populate_year_dropdown( $earliest_year = 1900, $latest_year ) {
    if(!$latest_year) $latest_year = date('Y');
    $html = '';
    foreach ( range( $latest_year, $earliest_year ) as $i ) {
      $html .= '<option value="'.$i.'">'.$i.'</option>';
    }
    return $html;
  }

/**
 * Get All Pubs sorted
 */
function get_all_pubs_sorted() {
    $sites = get_sites([
        'site__not_in' => [1,2,3]
    ]);

    // Order by ->blogname
    usort( $sites, function( $a, $b ) {
        return strcmp( $a->blogname, $b->blogname );
    });

    // --- Replace black Horse pubs positions so that Reigate is first
    $thame_key = $reigate_key = null;
    foreach( $sites as $key => $site ) {
        if( strstr($site->domain, 'blackhorsethame') !== false ) $thame_key = $key;
        if( strstr($site->domain, 'theblackhorsereigate') !== false ) $reigate_key = $key;
    }
    if( $thame_key && $reigate_key && $reigate_key > $thame_key ) {
        // var_dump($thame_key, $reigate_key);
        $sites = SaintMoveKeyBefore($sites, $thame_key, $reigate_key);
    }

    return $sites;
}

/**
 * Get All Pubs (published) sorted
 */
function get_published_pubs_sorted() {
    $sites = get_sites([
        'site__not_in' => [1,2,3],
        'public' => 1,
    ]);

    // Order by ->blogname
    usort( $sites, function( $a, $b ) {
        return strcmp( $a->blogname, $b->blogname );
    });

    // --- Replace black Horse pubs positions so that Reigate is first
    $thame_key = $reigate_key = null;
    foreach( $sites as $key => $site ) {
        if( strstr($site->domain, 'blackhorsethame') !== false ) $thame_key = $key;
        if( strstr($site->domain, 'theblackhorsereigate') !== false ) $reigate_key = $key;
    }
    if( $thame_key && $reigate_key && $reigate_key > $thame_key ) {
        // var_dump($thame_key, $reigate_key);
        $sites = SaintMoveKeyBefore($sites, $thame_key, $reigate_key);
    }

    return $sites;
}

/**
 * Find Page ID by assigned template
 */
function getPageByTemplate($pageTemplate){
    $pages = get_pages(array(
        'meta_key' => '_wp_page_template',
        'meta_value' => $pageTemplate
    ));
    return $pages[0]->ID;
}

/**
 * Get ACF option in network
 *
 * @param   int $blog
 * @param   string $key
 * @return  string
 */

 function multisite_acf_option( $blog, $key )
 {
     switch_to_blog( $blog );
     $meta_value = get_field($key,'option');
     restore_current_blog();
     return $meta_value;
 }

/**
 * Get Driving distnace between postcodes
 */
function GetDrivingDistance( $origin, $destination ) {

    // $api_key = 'AIzaSyA1sPp7yX2mR4cc6EDKe3Wr8BzoqeUvN_w'; // Testing project
    $api_key = 'AIzaSyDuXtPZUTjTyYvq9SN5-rthAtJ73Cva0Mg'; // CN api key
    $data = file_get_contents( "https://maps.googleapis.com/maps/api/distancematrix/json?units=imperial&origins=". urlencode($origin) ."&destinations=". urlencode($destination) ."&key=".$api_key );

    if( $data ) {
        $response = json_decode($data, true);
        $error = ( $response['status'] !== 'OK' || $response['rows'][0]['elements'][0]['status'] !== 'OK' );
        if( $error ) return $response;
        $dist = $response['rows'][0]['elements'][0]['distance']['text'];
        $dist_val = $response['rows'][0]['elements'][0]['distance']['value'];
    //    $time = $response['rows'][0]['elements'][0]['duration']['text'];
        return array('distance' => $dist, 'value' => $dist_val, 'origin' => $response['origin_addresses'][0], 'response' => $response);
    }else{
        return false;
    }

}

// --- Get Locations data
function getLocationPreviews($layout='default') {
    $locations = get_all_pubs_sorted();
    $markers= null;
    $regions = [];
    $features = [];
    $room_features = [];
    foreach ( $locations as $site ) :
        switch_to_blog( $site->blog_id );
        $comingsoon = get_field('opt_coming_soon','option') ?: false;

        $brand = get_field('opt_brand', 'option');
        $name = get_bloginfo( "name" );
        $address = get_field('opt_address', 'option');
        $address_multi = get_field('opt_address_multiline', 'option');
        $phone = get_field( 'opt_phone', 'option' );
        $email = get_field('opt_email', 'option');
        $directions = get_field('opt_directions', 'option');
        $wordmark = get_field('opt_logo_wordmark', 'option');

        $disable_rooms = get_field('disable_rooms', 'option') ?: false;
        $opt_hotel_id = get_field('opt_hotel_id', 'option') ?: false;
        $opt_cutom_button_label = get_field('opt_cutom_button_label', 'option') ?: false;
        $opt_cutom_button_link = get_field('opt_cutom_button_link', 'option') ?: false;
        $opt_cutom_button_newtab = get_field('opt_cutom_button_newtab', 'option') ?: false;
        $region = get_field('opt_county', 'option') ?: false;
        $feature = get_field('opt_features', 'option') ?: false;
        $feature_obj = get_field_object('opt_features', 'option') ?: false;
        $room_feature = get_terms(array(
            'taxonomy' => 'feature'
        ));
        $headless_config = get_option('headless_config');
        $geo_lat = get_field('opt_geo_lat', 'option') ?: 0;
        $geo_lng = get_field('opt_geo_lng', 'option') ?: 0;
        $image = get_field('opt_logo_sign', 'option') ?: false;
        restore_current_blog();

        // --- If Site is not Public and not in ComingSoon mode, then continue
        if( !$site->public && !$comingsoon ) continue;
        // --- If layout 'rooms' then check if Site has rooms enabled
        if( $layout=='rooms' && ($disable_rooms || !$opt_hotel_id) ) continue;
        $image_array = null;

        // if( $brand == 'wbc' ) {
        if( $image ) {
            // $image_array = $image;
            $image_array = [
                $image['sizes']['medium_large'] ?: $image['url'],
                $image['sizes']['medium_large-width'] ?: $image['width'],
                $image['sizes']['medium_large-height'] ?: $image['height']
            ];
        }else { // Image placeholder
            $image_array = [
                '/images/coming-soon.png',
                350,
                350
            ];
        }
        // --- Inn: Regions
        if( $region ) {
            $region_parts = explode(",", $region);
            foreach( $region_parts as $item ) {
                if( $item && !in_array(trim($item), $regions)) $regions[] = trim($item);
            }
        }
        // --- Inn: Features
        if( $feature ) {
            foreach($feature as $item) {
                $arr_key = array_search($item['value'], array_keys($feature_obj['choices']));
                if(!in_array($item['label'], $features)) $features[$arr_key] = $item['label'];
            }
            ksort($features);
        }
        // --- Inn: Room Features
        if( $room_feature ) {
            foreach($room_feature as $item) {
                if(!in_array($item->name, $room_features)) $room_features[] = $item->name;
            }
        }

        $markers[] = array(
            "brand" => $brand,
            "name" => $name,
            "address" => $address_multi,
            "address_short" => $address,
            "tel" => $phone,
            "email" => $email,
            "directions" => $directions,
            "website" => $headless_config['frontend_url'],
            "bookTable" => $headless_config['frontend_url']."book-a-table/",
            "bookStay" => $headless_config['frontend_url']."rooms/",
            "image" => $image_array,
            "wordmark" => $wordmark,
            'region' => $region,
            'feature' => $feature,
            'room_feature' => array_map(function ($item){ return ['value'=>$item->slug, 'label'=>$item->name]; }, $room_feature),
            "position" => [$geo_lat, $geo_lng],
            "comingsoon" => $comingsoon,
            "rooms_enabled" => !$disable_rooms && $opt_hotel_id ? true : false,
            "custom_button" => [
                'label'=>$opt_cutom_button_label,
                'link'=>$opt_cutom_button_link,
                'newtab'=> $opt_cutom_button_newtab
            ]
        );
        // }

    endforeach;
    restore_current_blog();

    return array(
        array(
            "centerPos" => [52.412951,-0.8642876],
            "zoom" => 7,
            "showFilters" => false,
            "showPopups" => true,
            'regions' => $regions,
            'features' => array_values($features),
            'room_features' => $room_features,
            'layout' => $layout
        ),
        $markers
    );
}

/**
 * Get Bookable Areas listing data
 */
function getBookableAreaPreviews($area_type='all') {
    {
        $locations = get_all_pubs_sorted();
        $markers= null;
        $regions = [];
        $features = [];
        $pd_features = [];
        $pd_types = [];
        $room_features = [];
        $layout = 'areas';
        foreach ( $locations as $site ) :
            switch_to_blog( $site->blog_id );
            $comingsoon = get_field('opt_coming_soon','option') ?: false;

            $brand = get_field('opt_brand', 'option');
            $name = get_bloginfo( "name" );
            $address = get_field('opt_address', 'option');
            $address_multi = get_field('opt_address_multiline', 'option');
            $phone = get_field( 'opt_phone', 'option' );
            $email = get_field('opt_email', 'option');
            $directions = get_field('opt_directions', 'option');
            $wordmark = get_field('opt_logo_wordmark', 'option');

            $disable_rooms = get_field('disable_rooms', 'option') ?: false;
            $opt_hotel_id = get_field('opt_hotel_id', 'option') ?: false;
            $opt_cutom_button_label = get_field('opt_cutom_button_label', 'option') ?: false;
            $opt_cutom_button_link = get_field('opt_cutom_button_link', 'option') ?: false;
            $opt_cutom_button_newtab = get_field('opt_cutom_button_newtab', 'option') ?: false;
            $region = get_field('opt_county', 'option') ?: false;
            $feature = get_field('opt_features', 'option') ?: false;
            $feature_obj = get_field_object('opt_features', 'option') ?: false;
            $room_feature = get_terms(array(
                'taxonomy' => 'feature'
            ));
            $headless_config = get_option('headless_config');
            $geo_lat = get_field('opt_geo_lat', 'option') ?: 0;
            $geo_lng = get_field('opt_geo_lng', 'option') ?: 0;
            $image = get_field('opt_logo_sign', 'option') ?: false;

            $bookable_areas = null;
            $areas = get_posts(array(
                'post_type' => 'area',
                'posts_per_page' => -1,
                'orderby' => 'post_title',
                'order' => 'ASC'
            ));
            if( $areas ) {
                foreach ($areas as $area) {
                    $seated = get_field('capacity_seated',$area) ?: false;
                    $standing= get_field('capacity_standing', $area) ?: false;
                    $pd_feature = get_field('features', $area) ?: false;
                    $pd_feature_obj = get_field_object('features', $area) ?: false;
                    $pd_type = get_field('type', $area) ? [get_field('type', $area)] : false;
                    $pd_type_obj = get_field_object('type', $area) ?: false;
                    $area_image = get_field('image', $area) ?: false;
                    $area_image_array = null;
                    if($area_image) {
                        $area_image_array = [
                            $area_image['sizes']['medium_large'] ?: $area_image['url'],
                            $area_image['sizes']['medium_large-width'] ?: $area_image['width'],
                            $area_image['sizes']['medium_large-height'] ?: $area_image['height']
                        ];
                    }
                    $bookable_areas[] = array(
                        'title' => $area->post_title,
                        'slug' => $area->post_name,
                        'seated' => $seated ? [(int)$seated['from'], (int)$seated['to']] : [0,0],
                        'standing' => $standing ? [(int)$standing['from'], (int)$standing['to']] : [0,0],
                        'features' => $pd_feature,
                        'type'  => $pd_type,
                        'image' => $area_image_array
                    );
                    // --- Area: Features
                    if( $pd_feature ) {
                        foreach($pd_feature as $item) {
                            $arr_key = array_search($item['value'], array_keys($pd_feature_obj['choices']));
                            if(!in_array($item['label'], $pd_features)) $pd_features[$arr_key] = $item['label'];
                        }
                        ksort($pd_features);
                    }
                    // --- Area: Type
                    if( $pd_type ) {
                        foreach($pd_type as $item) {
                            $arr_key = array_search($item['value'], array_keys($pd_type_obj['choices']));
                            if(!in_array($item['label'], $pd_types)) $pd_types[$arr_key] = $item['label'];
                        }
                        ksort($pd_types);
                    }
                }
            }

            restore_current_blog();

            // --- If Site is not Public and not in ComingSoon mode, then continue
            if( !$site->public && !$comingsoon ) continue;
            // --- If layout 'rooms' then check if Site has rooms enabled
            if( $layout=='rooms' && ($disable_rooms || !$opt_hotel_id) ) continue;
            $image_array = null;

            // if( $brand == 'wbc' ) {
            if( $image ) {
                // $image_array = $image;
                $image_array = [
                    $image['sizes']['medium_large'] ?: $image['url'],
                    $image['sizes']['medium_large-width'] ?: $image['width'],
                    $image['sizes']['medium_large-height'] ?: $image['height']
                ];
            }else { // Image placeholder
                $image_array = [
                    '/images/coming-soon.png',
                    350,
                    350
                ];
            }
            // --- Inn: Regions
            if( $region ) {
                $region_parts = explode(",", $region);
                foreach( $region_parts as $item ) {
                    if( $item && !in_array(trim($item), $regions)) $regions[] = trim($item);
                }
            }
            // --- Inn: Features
            if( $feature ) {
                foreach($feature as $item) {
                    $arr_key = array_search($item['value'], array_keys($feature_obj['choices']));
                    if(!in_array($item['label'], $features)) $features[$arr_key] = $item['label'];
                }
                ksort($features);
            }
            // --- Area: Features
            if( $pd_feature ) {
                foreach($pd_feature as $item) {
                    $arr_key = array_search($item['value'], array_keys($pd_feature_obj['choices']));
                    if(!in_array($item['label'], $pd_features)) $pd_features[$arr_key] = $item['label'];
                }
                ksort($pd_features);
            }
            // --- Area: Types
            if( $pd_type ) {
                foreach($pd_type as $item) {
                    $arr_key = array_search($item['value'], array_keys($pd_type_obj['choices']));
                    if(!in_array($item['label'], $pd_types)) $pd_types[$arr_key] = $item['label'];
                }
                ksort($pd_types);
            }
            // --- Inn: Room Features
            if( $room_feature ) {
                foreach($room_feature as $item) {
                    if(!in_array($item->name, $room_features)) $room_features[] = $item->name;
                }
            }

            $markers[] = array(
                "brand" => $brand,
                "name" => $name,
                "address" => $address_multi,
                "address_short" => $address,
                "tel" => $phone,
                "email" => $email,
                "directions" => $directions,
                "website" => $headless_config['frontend_url'],
                "bookTable" => $headless_config['frontend_url']."book-a-table/",
                "bookStay" => $headless_config['frontend_url']."rooms/",
                "image" => $image_array,
                "wordmark" => $wordmark,
                'region' => $region,
                'feature' => $feature,
                'room_feature' => array_map(function ($item){ return ['value'=>$item->slug, 'label'=>$item->name]; }, $room_feature),
                "position" => [$geo_lat, $geo_lng],
                "comingsoon" => $comingsoon,
                "rooms_enabled" => !$disable_rooms && $opt_hotel_id ? true : false,
                "custom_button" => [
                    'label'=>$opt_cutom_button_label,
                    'link'=>$opt_cutom_button_link,
                    'newtab'=> $opt_cutom_button_newtab
                ],
                'bookable_areas' => $bookable_areas
            );
            // }

        endforeach;
        restore_current_blog();

        // --- Filter out features array
        $features = array_filter($features, function($value) {
            return in_array(strtolower($value), ['accommodation available']);
        });

        // --- Filter out pd_features array
        $pd_features = array_filter($pd_features, function($value) {
            return !in_array(strtolower($value), ['wheelchair accessible']);
        });

        return array(
            array(
                "centerPos" => [52.412951,-0.8642876],
                "zoom" => 7,
                "showFilters" => false,
                "showPopups" => true,
                'regions' => $regions,
                'features' => array_values($features),
                'pd_features' => array_values($pd_features),
                'pd_types' => array_values($pd_types),
                'room_features' => $room_features,
                'layout' => $layout,
                'area_type' => $area_type
            ),
            $markers
        );
    }
}

/**
 * Guestline: get Button data
 */
function GuestlineButtonData() {
    $data = [];
    // return json_encode($data);
    if( is_inns_master_site() ){
        $data['type'] = 'multi';
        $sites = get_all_pubs_sorted();
        foreach($sites as $site){
            switch_to_blog( $site->blog_id );
            $city = ', ' . get_field( 'opt_town', 'option' );
            $location_name = get_bloginfo( "name" ) . $city;
            $opt_disable_rooms = get_field('disable_rooms', 'option') ?: false;
            $opt_hotel_id = get_field('opt_hotel_id', 'option') ?: false;
            $comingsoon = get_field('opt_coming_soon','option') ?: false;
            $rooms_available = [];
            $rooms = get_posts(array(
                'post_type' => 'room',
                'fields' => 'ids',
                'posts_per_page' => -1
            ));
            $headless_config = get_option('headless_config');
            if( $rooms ) {
                foreach($rooms as $id) {
                    $room_id = get_field('guestline_room_id', $id) ?: false;
                    if( $room_id ) $rooms_available[] = $room_id;
                }
                array_unique($rooms_available);
            }
            restore_current_blog();
            if( !($opt_disable_rooms || !$opt_hotel_id || $comingsoon) ) {
                $data['hotels'][] = [
                    'id' => $opt_hotel_id,
                    'name' => $location_name,
                    'rooms' => $rooms_available,
                    'url' => $headless_config['frontend_url']."rooms/"
                ];
            }

        }
        restore_current_blog();
    }else {
        $default_hotel = get_field('opt_hotel_id', 'option') ?: false;
        $data['type'] = 'single';
        $data['hotels'] = $default_hotel;
    }
    return json_encode($data);
}