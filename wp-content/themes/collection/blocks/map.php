<?php
/**
 * Responsive Map embed - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'map-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-map my-6'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$options = json_encode(array(
    "centerPos" => [51.4277735,-0.328],
    "zoom" => 10,
    "showFilters" => false,
    "showPopups" => false
));

$markers = array(
    [
        "brand" => "hc",
        "name" => "King's Head Teddington",
        "address" => "123 High Street, <br>Teddington, TW11 8HG",
        "tel" => "020 3166 2900",
        "email" => "<EMAIL>",
        "directions" => "https://www.google.com/maps/place/The+King's+Head/@51.4274997,-0.3310558,17z/data=!3m1!4b1!4m6!3m5!1s0x48760b81d637ba61:0x5490da65211ead18!8m2!3d51.4274997!4d-0.3284809!16s%2Fg%2F1tfbmt6q",
        "website" => "https://kingsheadteddington.com/",
        "bookTable" => "https://kingsheadteddington.com/book-now/",
        "image" => "",
        "position" => [51.4277735,-0.328]
    ]);

$markers_json = json_encode($markers);

?>
<?php if( is_admin() ): ?>
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Responsive Map</h4>
        <hr>
        <p>This is just the placeholder here.</p>
    </div>
<?php else: ?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?> <?= $image_align ?>">
    <div data-animated="true" class="ratio">
        <div data-locations-map="true"
        data-options="<?= htmlspecialchars($options, ENT_QUOTES, 'UTF-8') ?>"
        data-markers="<?= htmlspecialchars($markers_json, ENT_QUOTES, 'UTF-8') ?>">&nbsp;</div>
    </div>
</div><!-- .block-map -->
<?php endif; ?>