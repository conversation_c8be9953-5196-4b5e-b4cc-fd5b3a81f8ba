{"key": "group_6409afb8d9e28", "title": "Testimonial - Details", "fields": [{"key": "field_6409afb9c4709", "label": "Cite", "name": "cite", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_6409b564c470a", "label": "Content", "name": "content", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "tabs": "all", "toolbar": "basic", "media_upload": 0, "delay": 0}], "location": [[{"param": "post_type", "operator": "==", "value": "testimonial"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "show_in_graphql": 1, "graphql_field_name": "testiDetails", "map_graphql_types_from_location_rules": 0, "graphql_types": "", "modified": 1744195983}