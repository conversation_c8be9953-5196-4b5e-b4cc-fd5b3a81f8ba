<?php
namespace Saint;

add_action('init', __NAMESPACE__ . '\\add_hw_editor_role', 15);
function add_hw_editor_role() {
    // remove_role( 'hw_editor' );

    // --- Create new role, based on Editor role plus extra capability "edit_theme_settings'
    $hw_role = get_role( 'hw_editor' );
    if( !$hw_role ) {
        $caps = get_role( 'editor' )->capabilities;
        $caps['edit_theme_settings'] = true;
        add_role( 'hw_editor', 'HW Editor', $caps);
    }

    // --- add 'edit_theme_settings' capability to Administrators
    $admin_role = get_role( 'administrator' );
    if($admin_role) $admin_role->add_cap( 'edit_theme_settings', true );
}