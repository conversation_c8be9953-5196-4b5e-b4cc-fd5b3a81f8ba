{"key": "group_67618826af7d1", "title": "Content Block v2: Featured content side image", "fields": [{"key": "field_67618826b7211", "label": "Layout", "name": "featured_layout", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"default": "<PERSON><PERSON><PERSON>", "grid": "<PERSON><PERSON>"}, "default_value": "default", "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 1, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_67618826b724b", "label": "Skin", "name": "featured_skin", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"default": "<PERSON><PERSON><PERSON>", "pine": "Pine", "sap": "<PERSON>p", "plaster": "<PERSON><PERSON><PERSON>"}, "default_value": "default", "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 1, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_67618826b719d", "label": "Title", "name": "featured_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "Placeholder title", "maxlength": "", "allow_in_bindings": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_67618826b71d7", "label": "Subtitle", "name": "featured_subtitle", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67618826b7211", "operator": "==", "value": "grid"}]], "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_67618826b7284", "label": "Add animation", "name": "featured_anim", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67618826b7211", "operator": "==", "value": "default"}]], "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "allow_in_bindings": 1, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_67618826b72be", "label": "Animation type", "name": "featured_anim_type", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67618826b7284", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"bird": "<PERSON>", "dog": "Talking Dog", "video-dog": "Sleeping Dog"}, "default_value": "bird", "return_format": "value", "multiple": 0, "allow_null": 0, "allow_in_bindings": 1, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_67618826b72f7", "label": "Animation Text", "name": "featured_anim_text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_67618826b7284", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "A lovely statement goes here", "maxlength": "", "allow_in_bindings": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_67618826b73a3", "label": "Side image", "name": "featured_image", "aria-label": "", "type": "image", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "70", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 1, "preview_size": "medium"}], "location": [[{"param": "block", "operator": "==", "value": "acf/featured-content-2"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "show_in_graphql": 0, "graphql_field_name": "contentBlock:FeaturedContentSideImage", "map_graphql_types_from_location_rules": 0, "graphql_types": "", "modified": 1744195875}