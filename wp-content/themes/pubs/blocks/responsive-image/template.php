<?php
/**
 * Responsive Image - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'responsive-image-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-image'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$image = get_field('image') ?: false;
$image_url = $image['sizes']['portfolio-full'];
$image_width = $image['sizes']['portfolio-full-width'];
$image_height = $image['sizes']['portfolio-full-height'];
$image_alt = $image['alt'] ?: $image['title'];

$image_mobile = get_field('image_mobile') ?: false;
$image_mobile_url = $image_mobile['sizes']['portfolio-thumb'];
$image_mobile_width = $image_mobile['sizes']['portfolio-thumb-width'];
$image_mobile_height = $image_mobile['sizes']['portfolio-thumb-height'];
$image_mobile_alt = $image_mobile['alt'] ?: $image_mobile['title'];

// Placeholder image - admin only
if( is_admin() && !$image ) {
    $image = true;
    $image_url = 'https://placehold.co/600x400?text=Image';
    $image_width = 600;
    $image_height = 400;
    $image_alt = 'Placeholder image';
}

?>
<?php if ( ! $is_preview ) { ?>
    <div
        <?php
        echo wp_kses_data(
            get_block_wrapper_attributes(
                array(
                    'id'    => esc_attr($id),
                    'class' => esc_attr( $className ),
                )
            )
        );
        ?>
    >
<?php } ?>

    <?php if( is_admin() ): ?>

        <div class="row">
            <?php if( $image ): ?>
                <!-- Tablet/Desktop image -->
                <div class="col-md-<?= $image_mobile ? '8' : '12' ?>" style="position: relative;">
                    <span class="badge">Desktop image</span>
                    <img src="<?= $image_url ?>" width="<?= $image_width ?>" height="<?= $image_height ?>" alt="<?= $image_alt ?>"
                    class="img-fluid" />
                </div>
            <?php endif; ?>
            <?php if( $image_mobile ): ?>
                <!-- Mobile image (optional) -->
                <div class="col-md-4">
                    <span class="badge">Mobile image</span>
                    <img src="<?= $image_mobile_url ?>" width="<?= $image_mobile_width ?>" height="<?= $image_mobile_height ?>" alt="<?= $image_mobile_alt ?>"
                    class="img-fluid" />
                </div>
            <?php endif; ?>
        </div>

    <?php else: ?>

        <?php if( $image_mobile ): ?>
            <!-- Mobile image (optional) -->
             <div class="d-md-none <?= $block['data']['image_mobile_class'] ?>">
                 <img src="<?= $image_mobile_url ?>" width="<?= $image_mobile_width ?>" height="<?= $image_mobile_height ?>" alt="<?= $image_mobile_alt ?>" class="img-fluid" />
             </div>
        <?php endif; ?>

        <?php if( $image ): ?>
            <!-- Tablet/Desktop image -->
             <div class="<?= $image_mobile ? 'd-none d-md-block' : '' ?> <?= $block['data']['image_class'] ?>">
                 <img src="<?= $image_url ?>" width="<?= $image_width ?>" height="<?= $image_height ?>" alt="<?= $image_alt ?>" class="img-fluid" />
             </div>
        <?php endif; ?>

    <?php endif; ?>

<?php if ( ! $is_preview ) { ?>
    </div><!-- .block-image -->
<?php } ?>