<?php
/**
 * FAQ's - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'faqs-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-faqs block-accordions'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$accordions = null;
// === Get FAQ's
$tags = get_terms(array(
    'taxonomy' => 'post_tag',
    'hide_empty' => true
));
$i=0;
if( $tags ):
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
    <?php foreach($tags as $tag):
        $faqs = get_posts(array(
            'post_type' => 'faq',
            'tag'=> $tag->slug
        ));
        if( $faqs ): ?>
            <h2 class="text-capitalize<?= $i==0 ? ' mt-0' : '' ?>"><?= $tag->name; ?></h2>
            <div class="accordion" data-bs-accordion="true">
                <?php
                $i=1;
                foreach( $faqs as $faq ):
                    if( is_inns_master_site() && get_field('faq_hide_master', $faq->ID) ) continue;
                ?>
                    <div class="card" data-bs-accordion-item="true" data-key="<?= $i ?>">
                        <div class="card-header mb-2 p-0" data-bs-accordion-header="true">
                            <?= $faq->post_title ?>
                        </div>

                        <div class="collapse" aria-labelledby="heading-<?= $i ?>" data-bs-accordion-body="true">
                            <div class="card-body pt-0 pb-5">
                                <?= $faq->post_content ?>
                            </div>
                        </div>
                    </div>
                <?php
                $i++;
                endforeach; ?>
            </div>
        <?php endif;
    ?>
    <?php endforeach; ?>

     <!-- Display warning at backend if no posts available -->
     <?php if(  is_admin() && !$i): ?>
        <p class="alert alert-info text-center">Please add some Faq's first.</p>
    <?php endif; ?>

</div><!-- .block-accordions -->
<?php
endif;
?>