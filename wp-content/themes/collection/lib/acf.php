<?php
/**
 * Register Site Options page
 */
add_action( 'acf/init', function(){
	if( function_exists('acf_add_options_page') ) {

		acf_add_options_page(array(
			'page_title' 	=> 'Heartwood Settings',
			'menu_title'	=> 'Heartwood Settings',
			'menu_slug' 	=> 'heartwood-settings',
			'capability'	=> 'edit_theme_settings',
			'redirect'		=> false,
			'show_in_graphql' => true,
		));

	}
} );

add_action( 'acf/init', function() {
	acf_add_options_page( array(
	'page_title' => 'Heartwood SEO',
	'menu_slug' => 'heartwood-seo',
	'parent_slug' => 'wpseo_dashboard',
	'position' => '',
	'redirect' => false,
	'capability' => 'wpseo_manage_options',
) );
} );

/**
 * Pre-fill some of the ACF fields
 */
if( is_admin() ) {
	add_filter('acf/load_field/key=field_663e0c9247bc5', function ( $field ) {
		// Reset choices
		$field['choices'] = array();
		// get sites
		$sites = get_sites([
			'public' => 1,
		]);
		// Loop through the array and add to field 'choices'
		if( is_array($sites) ) {
			foreach( $sites as $site ) {
				$label = get_blog_details($site->blog_id)->blogname;
				$field['choices'][ $site->blog_id ] = $label;
			}
		}
		// Return the field
		return $field;
	}, 10);
}


/** ==================================
 * ACF export
 * taken from Heartwood Inns DEV site, here:
 * https://hwc-cms.wearetesting.co.uk/wp-admin/edit.php?post_type=acf-field-group
 *  ================================== */

 // === Gutenberg Content Block
require_once __DIR__.'/acf-field-groups/cb-cta.php';
require_once __DIR__.'/acf-field-groups/cb-socials.php';
require_once __DIR__.'/acf-field-groups/cb-accordions.php';
require_once __DIR__.'/acf-field-groups/cb-featured.php';
require_once __DIR__.'/acf-field-groups/cb-case-studies.php';
require_once __DIR__.'/acf-field-groups/cb-testimonials.php';
require_once __DIR__.'/acf-field-groups/cb-video.php';
require_once __DIR__.'/acf-field-groups/cb-awards.php';

// === General
require_once __DIR__.'/acf-field-groups/hero.php';
require_once __DIR__.'/acf-field-groups/page-options.php';
require_once __DIR__.'/acf-field-groups/promo-popup.php';

// === CPT related
require_once __DIR__.'/acf-field-groups/single-testimonial.php';

// === Theme Settigns related
require_once __DIR__.'/acf-field-groups/opt-general.php';

// === SEO: robots
require_once __DIR__.'/acf-field-groups/seo-robots.php';
