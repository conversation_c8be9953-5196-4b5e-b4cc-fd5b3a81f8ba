<?php

/**
 * Aloha-API-PHP : Simple PHP wrapper for the Aloha API
 *
 * PHP version 7.x
 *
 * @category Awesomeness
 * @package  Atreemo-API-PHP
 * <AUTHOR> <<EMAIL>>
 * @license  http://opensource.org/licenses/gpl-license.php GNU Public License
 * @link     https://saintdesign.co.uk
 */
class AtreemoAPI {

    private $instance = 'https://atreemo.news.brasserieblanc.com';
    // private $instance = 'http://10.255.255.1'; // Debug only: fake 503 timeout error adress
    Private $username = '<EMAIL>';
    private $password = 'Q1zc6K*!l$ArpKur';

    private $token = null;
    private $data = null;
    public $locations = null;

    /**
     * Create the API access object.
     */
    public function __construct()
    {
        if (!in_array('curl', get_loaded_extensions()))
        {
            throw new Exception('You need to install cURL, see: http://curl.haxx.se/docs/install.html');
        }
    }

    /**
     * Check service
     * POST /token
     * The login and the password are used in the previous endpoint, in exchange for a token that will be appended to every future request.
     * Atreemo uses token based authentication through OAuth2. This means that you use a token on every request to authorize yourself.
     * Obtaining an access token is done by sending a HTTP POST request to the above endpoint.
     * @param username string Username.
     * @param password strong Password
     */
    public function getToken() {

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->instance . "/token");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, "grant_type=password&username=". $this->username ."&password=". $this->password ."");

        // === receive server response ...
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3); // The number of seconds to wait while trying to connect. Use 0 to wait indefinitely.
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // The maximum number of seconds to allow cURL functions to execute.
        $response = json_decode( curl_exec($ch), true );
        return $response;
    }

    /**
     * Get list of locations
     * GET api/Location/Get
     */
    // Sample:
    // {
    // "Locations": [
    //     {
    //     "LocationID": "sample string 1",
    //     "LocationName": "sample string 2",
    //     "Suspended": true,
    //     "OriginLocationID": "sample string 4"
    //     }
    // ],
    // "Response": true,
    // "Error": [
    //     "sample string 1"
    // ]
    // }
    public function getLocations() {
        $key = 'wbc-atreemo-locations';
        if ( false === ( $query = get_transient($key) ) ) {
            $query = $this->sendData( '/api/Location/Get', 'GET' );
            set_transient($key,$query,3600);
        }
        return $query;
    }

    public function getLocationsFormated() {
        // --- use cached data if available
        if( $this->locations ) return $this->locations;
        // --- get new data from API
        $response = $this->getLocations();
        if( !$response || !$response['Response'] ) return false;
        $locations_formated = null;
        $locations = $response['Locations'];
        foreach( $locations as $item ) {
            $locations_formated[ $item['LocationID'] ] = array(
                'LocationName'  => $item['LocationName'],
                'Suspended'     => $item['Suspended'] ? 1 : 0
            );
        }
        $this->locations = $locations_formated;
        return $this->locations;
    }

    public function isSuspended( $atreemo_id ) {
        $locs = $this->getLocationsFormated();
        return isset( $locs[ $atreemo_id ] ) && $locs[ $atreemo_id ]['Suspended'] ? true : false;
    }

    /**
     * Get customer ID(CtcID) and MD5 by Email
     * GET api/Contact/GetContactIDsByEmail?Email={Email}
     */
    public function getContactByID( $id ) {
        return $this->sendData( '/api/Contact/Get/' . $id, 'GET' );
    }

    /**
     * Get customer ID(CtcID) and MD5 by Email
     * GET api/Contact/GetContactIDsByEmail?Email={Email}
     */
    public function getContactIDByEmail( $contact_email ) {
        $query = http_build_query([
            'Email' => $contact_email
        ]);
        return $this->sendData( '/api/Contact/GetContactIDsByEmail?' . $query, 'GET' );
    }

    /**
     * Add Contact
     * POST [API Instance]/api/Contact/PostContact
    */
    public function addContact( $contact_data ) {
        $this->data = $contact_data;
        return $this->sendData('/api/Contact/PostContact');
    }

    /**
     * Update Contact
     * PUT api/Contact/PutContact
    */
    public function updateContact( $contact_data ) {
        $this->data = $contact_data;
        return $this->sendData('/api/Contact/PutContact', 'PUT');
    }

    /**
     * Post preferences of know contact
     * POST api/CommunicationPreference/PostEmailOptin
    */
    public function updatePreferences( $pref_data ) {
        $this->data = $pref_data;
        return $this->sendData('/api/CommunicationPreference/Post');
    }

    // ====== START: NEW Customer calls 1.04.2025
    /**
     * Add Customer
     * POST [API Instance]/api/Customer/Post
    */
    public function addCustomer( $contact_data ) {
        $this->data = $contact_data;
        return $this->sendData('/api/Customer/Post');
    }
    // ====== END: NEW Customer calls 1.04.2025

    /**
     * Send call to API endpoint
     * @param endpoint string API endpoint
     * @param methos string HTTP request method
     */
    public function sendData( $endpoint = '', $method = 'POST' ){

        $json = json_encode($this->data);
        $this->token = $this->getToken();


        if( !$this->token ) return array('status' => 'no token');

        // return $this->token['access_token'];

        $headers = array(
          'Content-Type: application/json',
          'Authorization: Bearer '. $this->token['access_token']
        );

        if( $method == 'GET' ) {
            $defaults = array(
                CURLOPT_URL => $this->instance . $endpoint,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FRESH_CONNECT => 1,
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_FORBID_REUSE => 1,
                CURLOPT_CUSTOMREQUEST => $method
            );
        }else{
            $defaults = array(
                CURLOPT_URL => $this->instance . $endpoint,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FRESH_CONNECT => 1,
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_FORBID_REUSE => 1,
                CURLOPT_POSTFIELDS => $json ?: false,
                CURLOPT_POST => 1,
                CURLOPT_CUSTOMREQUEST => $method
            );
        }

		try{
			$ch = curl_init();
			curl_setopt_array($ch, $defaults);

			$result = json_decode(curl_exec($ch), true);

			if(FALSE === $result){
				throw new Exception(curl_error($ch), curl_errno($ch));
			}
			curl_close($ch);
		}catch(Exception $e){
          return 'Caught exception:' . $e->getMessage();
		}
		return $result;
	}

}