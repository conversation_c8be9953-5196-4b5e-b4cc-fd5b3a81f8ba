<?php
/**
 * Bookable Area - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'area-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-area'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$area = $block['data']['area'] ?: get_field('bookable_area');

if( is_admin() && !$area ) { ?>
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Bookable Area</h4>
        <hr>
        <p>Nothing to display yet. Please use block settings in sidebar.</p>
    </div>
    <?php
    return;
}elseif( !$area ) {
    return;
}

$title = get_the_title($area);
$image = get_field('image', $area);
$image_mobile = get_field('image_mobile', $area);
$bookable_days = get_field('bookable_days', $area);
$booking_fee = get_field('booking_fee', $area);
$type = get_field('type', $area);
$features = get_field('features', $area);
$capacity_seated = get_field('capacity_seated', $area);
$capacity_standing = get_field('capacity_standing', $area);
$desc = get_field('description', $area);

?>
<?php if ( ! $is_preview ) { ?>
<div
    <?php
    echo wp_kses_data(
        get_block_wrapper_attributes(
            array(
                'id'    => esc_attr($id),
                'class' => esc_attr( $class_name ),
            )
        )
    );
    ?>
>
<?php } ?>

    <div class="align wp-block-acf-responsive-image" id="responsive-image-block_3cb8f4f692c046c5df9b0e6554face36">

        <?php Saint\render_acf_block('acf/responsive-image', ['image' => $image, 'image_mobile' => $image_mobile, 'image_class'=>'ratio ratio-2x1', 'image_mobile_class'=>'ratio ratio-1x1']); ?>

    </div>
    <div class="container">
        <div class="wp-bootstrap-blocks-row row my-50">

                <div class="col-12 col-md-6 col-lg-4 offset-lg-1 text-center text-md-start">
                    <div class="ps-md-50 ps-lg-0">
                        <h3 class="wp-block-heading mt-0"><?= $title ?></h3>
                        <?php
                        if( $desc ): echo $desc; endif;
                        ?>
                    </div>
                </div>

                <div class="col-12 col-md-6 col-lg-5 offset-lg-1 text-center text-md-start">
                    <div class="pe-md-50 pe-lg-0">
                        <ul class="wp-block-list text-center text-md-start">
                            <li>Capacity seated: <?= $capacity_seated["from"] ?: 'n/a' ?><?= $capacity_seated["from"] && $capacity_seated["to"] ? ' to '.$capacity_seated["to"] : '' ?></li>
                            <li>Capacity standing: <?= $capacity_standing["from"] ?: 'n/a' ?><?= $capacity_standing["from"] && $capacity_standing["to"] ? ' to '.$capacity_standing["to"] : '' ?></li>
                            <?php if( $bookable_days ): ?>
                                <li>Bookable days:
                                    <?php $i=0; foreach( $bookable_days as $day ): echo ($i>0 ? ', ' : '') . $day["label"]; $i++; endforeach; ?>
                                </li>
                            <?php endif; ?>
                            <!-- <li>Room charge: <?= $booking_fee ?></li> -->
                            <li><?= $type["label"] ?> area</li>
                            <?php if( $features ): ?>
                                <?php foreach( $features as $feature ): ?>
                                    <li><?= $feature["label"] ?></li>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>

        </div>
    </div>

<?php if ( ! $is_preview ) { ?>
</div><!-- .block-slide -->
<?php } ?>