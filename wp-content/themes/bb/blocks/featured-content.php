<?php
/**
 * Featured content with image - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'featured-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-featured'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
$title = get_field('featured_title') ?: false;
$subtitle = get_field('featured_subtitle') ?: false;
$layout = get_field('featured_layout') ?: 'default';
$className .= ' ' . $layout;
$skin = get_field('featured_skin') ?: false;
if( $skin ) {
    $className .= ' bg-lg-'.$skin;
    $contentClass  = ' bg-'.$skin;
}
$content = get_field('featured_content');
$image = get_field('featured_image') ?: false;
$img_url = $image['sizes']['large'];
$img_width = $image['sizes']['large-width'];
$img_height = $image['sizes']['large-height'];
$image_alt = $image['alt'] ?: $image['title'];
if( $layout == 'grid' ) {
    $img_col_class = 'pr-0';
}
$buttons = get_field('featured_buttons') ?: false;
// === Animated animal with text on path
$featured_anim = get_field('featured_anim') ?: false; // [true, false]
$featured_anim_type = get_field('featured_anim_type'); // [bird, dog]
$featured_anim_text = get_field('featured_anim_text'); // free text
if( $featured_anim ) $className .= ' has-anim has-'.$featured_anim_type;
?>
<div id="<?= esc_attr($id); ?>" class="position-relative <?= esc_attr($className); ?>">

    <?php
    switch( $layout ):

        case 'grid': // Grid
        ?>
            <?php if( $image ): ?>
                <div data-animated="true" class="featured-image-wrap ratio ratio-1x1 <?= $img_col_class ?>">
                    <img src="<?= $img_url ?>" width="<?= $img_width ?>" height="<?= $img_height ?>" alt="<?= $image_alt ?>" class="img-fluid" />
                </div>
            <?php endif; ?>

            <div data-animated="true" class="featured-content-col">
                <div class="featured-content full-width-boxed d-flex flex-column justify-content-center text-center<?= $contentClass?>">

                    <?php if( $title ): ?>
                        <h2 class="fs-3 featured-title mt-30 mb-0 text-center"><?= $title ?></h2>
                    <?php endif; ?>
                    <?php if( $subtitle ): ?>
                        <h3 class="fs-4 featured-subtitle m-0 text-center"><?= $subtitle ?></h3>
                    <?php endif; ?>


                    <div class="featured-text mb-50 px-md-30 mt-30">
                        <?= $content ?>
                    </div>
                    <?php if($buttons): ?>
                        <div class="featured-buttons d-flex flex-column align-items-center">
                            <?php foreach( $buttons as $btn ):
                                $parsed = parse_url($btn['url']);
                                $is_internal = empty($parsed['scheme']) && !str_starts_with( $btn['url'], '#' ) ? 'data-app-link="true"' : '';
                                $btn_style = $btn['style'] ?: 'outline-dusk';
                                ?>
                                <a <?= $is_internal ?> href="<?= $btn['url'] ?>" class="btn btn-<?= $btn_style ?> mb-30" <?= $btn['target'] ? 'target="_blank"' : '' ?>><?= $btn['label'] ?></a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                </div>
            </div>


        <?php
            break;

        default: // Default
        ?>
        <div class="d-flex flex-column flex-lg-unset d-lg-grid mb-lg-135 ">

                <?php if( $title ): ?>
                    <h2 data-animated="true" data-animation="fadeInRight" class="featured-title text-center text-lg-start mb-0"><?= $title ?></h2>
                    <?php endif; ?>
                <?php if( $image ): ?>
                <div class="featured-image-wrap p-0 mt-30 mt-lg-0 <?= $img_col_class ?>">
                    <figure class="overflow-hidden bg-<?= $skin ?>">
                        <div data-animated="true" data-animation="scaleDown">
                            <div class="ratio ratio-1x1">
                                <img src="<?= $img_url ?>" width="<?= $img_width ?>" height="<?= $img_height ?>" alt="<?= $image_alt ?>" class="img-fluid" />
                                <?php if( !is_admin() ): ?><div data-animated="true" data-animation="fullWidth,0,1" class="image-cover bg-<?= $skin ?>">&nbsp;</div><?php endif; ?>
                            </div>
                        </div>
                    </figure>
                </div>
                <?php endif; ?>
                <div data-animated="true" data-animation="fadeInRight,0.3" class="featured-content<?= $contentClass ?>">
                    <div  class="featured-text text-center text-lg-start mt-30">
                        <?= $content ?>
                    </div>
                    <?php if($buttons): ?>
                        <p class="featured-buttons d-flex flex-column align-items-center align-items-lg-start mx-lg-n7 <?= count($buttons)>3 ? "columns-2" : '' ?>">
                            <?php foreach( $buttons as $btn ):
                                $parsed = parse_url($btn['url']);
                                $is_internal = empty($parsed['scheme']) && !str_starts_with( $btn['url'], '#' ) ? 'data-app-link="true"' : '';
                                $btn_style = $btn['style'] ?: 'outline-dusk';
                                ?>
                                <a <?= $is_internal ?> href="<?= $btn['url'] ?>" class="btn btn-<?= $btn_style ?> mb-15 mx-lg-7" <?= $btn['target'] ? 'target="_blank"' : '' ?>><?= $btn['label'] ?></a>
                            <?php endforeach; ?>
                        </p>
                    <?php endif; ?>

                </div>

                <div data-animated="true" data-animation="fadeInRight" class="align-self-end w-100" >
                    <?php
                    if($featured_anim && $featured_anim_type != 'video-dog'):
                        echo do_shortcode('[animated_animal type="'. $featured_anim_type.'" text="'.$featured_anim_text.'" class="half-width mb-15"]');
                    endif; ?>
                    <?php
                    if($featured_anim && $featured_anim_type == 'video-dog'):
                        echo do_shortcode('[animated_animal type="'. $featured_anim_type.'" text="'.$featured_anim_text.'" class="half-width mb-15"]');
                    endif; ?>
                    <hr class="my-50 my-lg-0" />
                </div>
        </div>
        <?php
            break;

    endswitch;
    ?>



</div><!-- .block-featured -->