<?php
/**
 * Wordmark - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'wordmark-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-wordmark'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?> <?= $image_align ?>">

    <div data-wordmark="true" class="alert alert-info text-center">
        <h4 class="alert-heading">Wordmark</h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>

</div><!-- .block-wordmark -->