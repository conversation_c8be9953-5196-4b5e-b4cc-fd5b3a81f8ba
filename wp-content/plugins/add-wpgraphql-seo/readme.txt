=== WPGraphQL Yoast SEO Addon ===
Contributors: ash_hitch
Tags: SEO, Yoast, WPGraphQL, GraphQL, Headless WordPress, Decoupled WordPress, JAMStack
Requires at least: 5.0
Tested up to: 6.1.1
Requires PHP: 7.1
Stable tag: 4.22.1
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

This plugin enables Yoast SEO Support for WPGraphQL.

== Description ==

This plugin enables Yoast SEO Support for WPGraphQL

This is an extension to the WPGraphQL plugin (https://github.com/wp-graphql/wp-graphql) that returns Yoast SEO data.

**Currently returning SEO data for:**

- Pages
- Posts
- Custom post types
- Products (WooCommerce)
- Categories
- Custom taxonomies
- WooCommerce Products
- Yoast Configuration
  - Webmaster verification
  - Social profiles
  - Schemas
  - Breadcrumbs

  > Please Note: Yoast and WPGraphQL and their logos are copyright to their respective owners.

== Installation ==

1. Install & activate [WPGraphQL](https://www.wpgraphql.com/)
2. Install & activate [Yoast SEO](https://wordpress.org/plugins/wordpress-seo/)
2. Upload plugin to the `/wp-content/plugins/` directory


 [See GitHub Repo for example queries](https://github.com/ashhitch/wp-graphql-yoast-seo)

== Upgrade Notice ==
Please note version 14 of the Yoast Plugin is a major update so is now required to run this plugin
