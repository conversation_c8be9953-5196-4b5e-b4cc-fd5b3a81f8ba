<?php
/**
 * Features (location) - Block Template.
 * Display listing from: Theme Settings -> Pub Features -> Features (location)
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'features-location-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-features-location'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
$features = get_field('opt_features', 'option');
if( $features ):
// var_dump($features);
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>" data-timestamp="<?= $assets_timestamp; ?>">
    <ul class="list list-unstyled list-inline">
        <?php foreach( $features as $feature ): ?>
            <li class="item list-inline-item"><?= $feature['label'] ?></li>
        <?php endforeach; ?>
    </ul>
</div><!-- .block-features-location -->
<?php elseif( is_admin() ): ?>
    <p class="alert alert-info text-center">Please add some Features (location) first, <a href="<?= admin_url( 'admin.php?page=heartwood-settings' ) ?>" target="_blank">here</a>.</p>
<?php endif; ?>