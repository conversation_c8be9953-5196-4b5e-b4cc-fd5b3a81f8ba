<?php
/**
 * Menus - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'menus-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-menus'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}


$menus_json = null;
$menus = get_posts(array(
    'post_type' => 'menu',
    'posts_per_page' => -1,
    'orderby' => 'menu_order',
    'order' => 'ASC'
));

if(!$menus) return;

foreach($menus as $menu) {
    $title = get_the_title( $menu );
    $slug = $menu->post_name;
    $featuredImage = wp_get_attachment_image_src( get_post_thumbnail_id( $menu ), "medium_large" );
    $menuType = get_field('menu_type', $menu);
    $menuHide = get_field('menu_hide', $menu);
    $customTitle = get_field('menu_custom_title', $menu) ?: false;
    $menus_json[] = array(
        'title' => $title,
        'customTitle' => $customTitle,
        'slug' => $slug,
        'featuredImage' => $featuredImage,
        'menuType' => $menuType,
        'menuHide' => $menuHide
    );
}

$menus_json = json_encode($menus_json);

if( !is_admin() ):
?>
<div data-animated="true">
    <div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
        <div data-location-menus="true" data-menus="<?= htmlspecialchars($menus_json, ENT_QUOTES, 'UTF-8') ?>">&nbsp;</div>
    </div>
</div><!-- .block-menus -->

<?php
else: ?>
    <!-- Backend placeholder -->
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Location Menus</h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>
<?php endif; ?>