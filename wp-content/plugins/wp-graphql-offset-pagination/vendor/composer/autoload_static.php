<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInite59a3a397b95a76817a0e6c0a56a9eec
{
    public static $prefixLengthsPsr4 = array (
        'W' => 
        array (
            'WPGraphQL\\Extensions\\OffsetPagination\\' => 38,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'WPGraphQL\\Extensions\\OffsetPagination\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'WPGraphQL\\Extensions\\OffsetPagination\\Loader' => __DIR__ . '/../..' . '/src/Loader.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInite59a3a397b95a76817a0e6c0a56a9eec::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInite59a3a397b95a76817a0e6c0a56a9eec::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInite59a3a397b95a76817a0e6c0a56a9eec::$classMap;

        }, null, ClassLoader::class);
    }
}
