<?php
/**
 * Opening times - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'opening-times-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-times'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$section_ids = get_field('section_ids') ? ' ids="'.get_field('section_ids').'"' : '';
$heading_style = get_field('heading_style') ? ' heading_style="'.get_field('heading_style').'"' : '';

?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
    <?php echo do_shortcode( '[opening_times'.$section_ids.$heading_style.']' )?>
    <?php if( is_admin() ): ?>
        <div class="alert alert-info text-center">
            <h4 class="alert-heading">Opening Times</h4>
            <hr>
            <p>This is just a placeholder.</p>
        </div>
    <?php endif; ?>
</div><!-- .block-contact -->