# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

stages:          # List of stages for jobs, and their order of execution
  - deploy

# image: samueldebruyn/debian-git

before_script:
  - apt-get update
  - apt-get -qq install git-ftp

deploy_staging:
  stage: deploy
  script:
    - echo "Deploy to staging server"
    # - git ftp init --user $FTP_USERNAME --passwd $FTP_PASSWORD --insecure sftp://dedi3763.your-server.de:22/public_html/heartwood
    - git ftp push --user $FTP_USERNAME --passwd $FTP_PASSWORD --insecure sftp://dedi3763.your-server.de:22/public_html/heartwood
  environment:
    name: staging
    url: https://hwc-cms.wearetesting.co.uk
  only:
  - staging

deploy_production:
  stage: deploy
  script:
    - echo "Deploy to production server"
    # - git ftp init --user $FTP_USERNAME --passwd $FTP_PASSWORD --insecure sftp://dedi3763.your-server.de:22/public_html/cms
    - git ftp push --user $FTP_USERNAME --passwd $FTP_PASSWORD --insecure sftp://dedi3763.your-server.de:22/public_html/cms
  environment:
    name: production
    url: https://cms.heartwoodcollection.com
  only:
  - master
