<?php
/**
 * Socials with icons - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'socials-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-socials st-socials'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// === Get Layout
$socials_layout = get_field('socials_layout') ?: 'links';
$className .= ' layout-'.$socials_layout;
// === Get Socials
$socials = get_field('socials');
if( $socials ):
    // var_dump($socials['opt_socials']);
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
    <ul class="st-socials-list">
        <?php foreach( $socials['opt_socials'] as $item): ?>
            <li class="st-socials-item">
                <a class="st-socials-link" href="<?=  $item['link']?>" target="_blank" title="Follow on <?= $item['provider'] ?>">
                    <?php if( $socials_layout == 'icons' ): ?>
                        <svg class="icon-<?= $item['provider'] ?>" fill="currentColor">
                            <use href="<?= '/images/icon.svg#' . $item['provider']?>" />
                        </svg>
                    <?php else: ?>
                        <?= $item['provider'] ?>
                    <?php endif; ?>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
</div><!-- .block-socials -->
<?php
else:
    echo '<p class="alert alert-info text-center">Please add some content</p>';
endif;
?>