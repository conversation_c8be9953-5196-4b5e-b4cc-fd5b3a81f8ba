<?php
/**
 * Transparent Video - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'video-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-video'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$mp4 = get_field('video_mp4') ? 'mp4="'.get_field('video_mp4').'" ' : false;
$webm = get_field('video_webm') ? 'webm="'.get_field('video_webm').'" ' : false;
$autoplay = get_field('video_autoplay') ? 'autoplay ' : '';
$loop = get_field('video_loop') ? 'loop ' : '';
$muted = get_field('video_muted') ? 'muted ' : '';
$playsinline = get_field('video_playsinline') ? 'playsinline ' : '';
$controls = get_field('video_controls') ? 'controls ' : '';
$no_lazy = get_field('video_lazyload') ? 'nolazy ' : '';
if( is_admin() ){
  $no_lazy = 'nolazy ';
}
if( !$mp4 && !$webm ) return;
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>" data-timestamp="<?= $assets_timestamp; ?>">
    <?php echo do_shortcode( '[video_transparent '.$mp4.$webm.$autoplay.$loop.$muted.$playsinline.$controls.$no_lazy.']' ); ?>
</div><!-- .block-video -->