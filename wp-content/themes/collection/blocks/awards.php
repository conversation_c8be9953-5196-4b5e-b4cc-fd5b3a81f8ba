<?php
/**
 * Transparent Video - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'awards-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-awards'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
$count = get_field('awards_count') ?: false;
$cols = get_field('awards_cols') ?: false;

$count_arg = $count ? ' count="'.$count.'"' : '';
$cols_arg = $cols ? ' cols="'.$cols.'"' : '';
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>" data-timestamp="<?= $assets_timestamp; ?>">
    <?php echo do_shortcode( '[awards_feed '.$count_arg.$cols_arg.']' ); ?>
</div><!-- .block-video -->