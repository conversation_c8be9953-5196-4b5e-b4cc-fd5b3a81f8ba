<?php
/**
 * Locations listing - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'locations-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-locations my-6'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
$layout = get_field('locations_layout') ?: array('label'=>'Default', 'value'=>'default');
$area_type = get_field('locations_area_type') ?: 'all';
?>
<?php if( is_admin() ): ?>
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Locations listing - <?= $layout["label"] ?></h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>
<?php else:
    list($options, $markers) = $layout["value"] == 'areas' ? getBookableAreaPreviews($area_type) : getLocationPreviews($layout["value"]);
    ?>
    <div data-locations-listing="true"
        data-options="<?= htmlspecialchars(json_encode($options), ENT_QUOTES, 'UTF-8') ?>"
        data-markers="<?= htmlspecialchars(json_encode($markers), ENT_QUOTES, 'UTF-8') ?>"
        id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
    </div>
</div><!-- .block-map -->
<?php endif; ?>