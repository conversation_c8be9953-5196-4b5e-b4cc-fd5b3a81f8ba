<?php
// if( is_admin() ){
//   error_reporting(E_ALL);
//   ini_set('display_errors', '1');
// }

if( !defined('SAINT_ENV') ) {
  if( strstr( $_SERVER['HTTP_HOST'], 'wearetesting.co.uk' ) !== false ) {
    define('SAINT_ENV', 'DEV');
  }else{
    define('SAINT_ENV', 'LIVE');
  }
}

if( !defined('ASSETS_TIMESTAMP') ) {
  define('ASSETS_TIMESTAMP', '20250220-01');
}

$sage_includes = [
  'lib/users.php', // Users related
  'lib/helpers.php',   // Helper functions
  'lib/acf.php',        // ACF related
  'lib/post-types.php', // CPT's
  'lib/schema/recipe.php', // Schema: Recipe
  'lib/schema/faqs.php', // Schema: FAQ
  'lib/schema/event.php', // Schema: Event
  'lib/setup.php',      // Theme setup
  'lib/gutenberg.php', // Extend Gutenberg blocks
  'lib/extras.php',     // Custom functions
  'lib/shortcodes.php', // Shortcodes
  'lib/content-blocks.php', // ACF Gutenberg blocks
  'atreemo/atreemo-api-class.php', // Atreemo API class
  'zonal-events/zonal-events-api.class.php', // Zonal Events REST API
  'lib/rest-api.php', // Custom REST API routes
];

foreach ($sage_includes as $file) {
  if (!$filepath = locate_template($file)) {
    trigger_error(sprintf(__('Error locating %s for inclusion', 'sage'), $file), E_USER_ERROR);
  }

  require_once $filepath;
}
unset($file, $filepath);