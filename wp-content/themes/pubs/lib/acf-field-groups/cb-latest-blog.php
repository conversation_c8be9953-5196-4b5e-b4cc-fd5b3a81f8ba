<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_6548a6b2ec746',
	'title' => 'Content Block - Latest from Blog',
	'fields' => array(
		array(
			'key' => 'field_6548a9d22e593',
			'label' => 'Type',
			'name' => 'latest_type',
			'aria-label' => '',
			'type' => 'radio',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'all' => 'All',
				'category' => 'Category',
			),
			'default_value' => 'all',
			'return_format' => 'value',
			'allow_null' => 0,
			'other_choice' => 0,
			'layout' => 'horizontal',
			'save_other_choice' => 0,
		),
		array(
			'key' => 'field_6548a6b32e591',
			'label' => 'Category',
			'name' => 'latest_cat',
			'aria-label' => '',
			'type' => 'taxonomy',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_6548a9d22e593',
						'operator' => '==',
						'value' => 'category',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'taxonomy' => 'category',
			'add_term' => 0,
			'save_terms' => 0,
			'load_terms' => 0,
			'return_format' => 'object',
			'field_type' => 'select',
			'allow_null' => 1,
			'bidirectional' => 0,
			'multiple' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_6548a9592e592',
			'label' => 'No. of posts',
			'name' => 'latest_qty',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_6548a9d22e593',
						'operator' => '==',
						'value' => 'category',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => 5,
			'min' => 1,
			'max' => '',
			'placeholder' => '',
			'step' => 1,
			'prepend' => '',
			'append' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'block',
				'operator' => '==',
				'value' => 'acf/latest',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 0,
	'graphql_field_name' => 'contentBlockLatestFromBlog',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );
