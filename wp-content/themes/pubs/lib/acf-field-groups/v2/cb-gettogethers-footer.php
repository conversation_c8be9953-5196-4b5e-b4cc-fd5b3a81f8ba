<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_683ff5235fc33',
	'title' => 'Content Block - Get Togethers (Features & Enquiry))',
	'fields' => array(
		array(
			'key' => 'field_683ff523e4171',
			'label' => 'Enquiry form',
			'name' => 'enquiry_form',
			'aria-label' => '',
			'type' => 'post_object',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'post_type' => array(
				0 => 'wpcf7_contact_form',
			),
			'post_status' => array(
				0 => 'publish',
			),
			'taxonomy' => '',
			'return_format' => 'object',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 1,
			'bidirectional' => 0,
			'ui' => 1,
			'bidirectional_target' => array(
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'block',
				'operator' => '==',
				'value' => 'acf/get-togethers-footer',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 0,
	'graphql_field_name' => 'contentBlockGetTogethers(Features&Enquiry))',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );