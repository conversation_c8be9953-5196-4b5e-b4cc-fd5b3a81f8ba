<?php
namespace Saint;
/**
 * Render an ACF block with your chosen attrs in place
 * Allows you to use the block within PHP templates
 * Note: Block template may need to be adjusted to read this data
 */
function render_acf_block( $block_name, $attrs = [] ) {
	$block = acf_get_block_type( $block_name );
	$content = '';
	$is_preview = false;

	foreach( $attrs as $attr => $val) {
		$block['data'][$attr] = $val;
	}

	echo acf_rendered_block( $block, $content, $is_preview );
}

/**
 * Extend Guttenberg blocks
 */

 /**
  * Core/button
  */
function register_button_block_styles() {
    register_block_style(
        'core/button', // name of your block
        array(
            'name'  => 'outline-dusk', // part of the class that gets added to the block.
            'label' => __( ' Outline dusk', 'style-outline-dusk' ),
        )
    );
    register_block_style(
        'core/button', // name of your block
        array(
            'name'  => 'redmid', // part of the class that gets added to the block.
            'label' => __( ' Red mid', 'style-redmid' ),
        )
    );
    register_block_style(
        'core/button', // name of your block
        array(
            'name'  => 'redlight', // part of the class that gets added to the block.
            'label' => __( ' Red light', 'style-redlight' ),
        )
    );
}
add_action( 'init', __NAMESPACE__ . '\\register_button_block_styles' );

/**
 * Enqueue custom gallery block script
 * Goal: add extra toggle to backend editor
 * It was commented out, since the gutenberg team fixes the 'lightbox for all images" thing
 */
// function saint_gallery_block_script() {
//     wp_enqueue_script(
//       'saint-gallery-block',
//       get_template_directory_uri() . '/gutenberg/gallery-block/build/block.js',
//       array('wp-blocks', 'wp-element', 'wp-editor'),
//       filemtime(get_template_directory() . '/gutenberg/gallery-block/build/block.js')
//     );
// }
// add_action('enqueue_block_editor_assets', __NAMESPACE__ . '\\saint_gallery_block_script');