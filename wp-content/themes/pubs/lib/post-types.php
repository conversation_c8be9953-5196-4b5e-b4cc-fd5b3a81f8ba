<?php
namespace Saint;

function create_post_type() {

    $labels = array(
		'name'                  => _x( 'Case Studies', 'Post Type General Name', 'sage' ),
		'singular_name'         => _x( 'Case Study', 'Post Type Singular Name', 'sage' ),
		'menu_name'             => __( 'Case Studies', 'sage' ),
		'name_admin_bar'        => __( 'Case Study', 'sage' ),
	);
	$args = array(
		'label'                 => __( 'Case Study', 'sage' ),
		'description'           => __( 'Case Study', 'sage' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'editor', 'thumbnail', 'revisions' ),
		'taxonomies'            => array( 'category' ),
		'hierarchical'          => false,
		'public'                => true,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-welcome-view-site',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => false,
		'exclude_from_search'   => true,
		'publicly_queryable'    => false,
		'capability_type'       => 'post',
	);
	register_post_type( 'case', $args );

    $labels = array(
		'name'                  => _x( 'Testimonials', 'Post Type General Name', 'sage' ),
		'singular_name'         => _x( 'Testimonial', 'Post Type Singular Name', 'sage' ),
		'menu_name'             => __( 'Testimonials', 'sage' ),
		'name_admin_bar'        => __( 'Testimonial', 'sage' ),
	);
	$args = array(
		'label'                 => __( 'Testimonial', 'sage' ),
		'description'           => __( 'Testimonial', 'sage' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'thumbnail' ),
		'hierarchical'          => false,
		'public'                => true,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-megaphone',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => false,
		'exclude_from_search'   => true,
		'publicly_queryable'    => false,
		'capability_type'       => 'post',
	);
	register_post_type( 'testimonial', $args );

	$labels = array(
		'name'                  => _x( 'FAQ', 'Post Type General Name', 'sage' ),
		'singular_name'         => _x( 'FAQ', 'Post Type Singular Name', 'sage' ),
		'menu_name'             => __( 'FAQ', 'sage' ),
		'name_admin_bar'        => __( 'FAQ', 'sage' ),
	);
	$args = array(
		'label'                 => __( 'FAQ', 'sage' ),
		'description'           => __( 'FAQ', 'sage' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'editor' ),
		'hierarchical'          => false,
		'public'                => true,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-lightbulb',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => false,
		'exclude_from_search'   => true,
		'publicly_queryable'    => false,
		'capability_type'       => 'post',
		'taxonomies' => array('post_tag'),
		'show_in_graphql' 		=> true,
		'graphql_single_name'	=> 'faq',
		'graphql_plural_name'	=> 'faqs',
		'rewrite' => array(
            'with_front' => false,
            'slug'       => 'faq'
        )
	);
	register_post_type( 'faq', $args );

	$labels = array(
		'name'                  => _x( 'Menus', 'Post Type General Name', 'sage' ),
		'singular_name'         => _x( 'Menu', 'Post Type Singular Name', 'sage' ),
		'menu_name'             => __( 'Menus', 'sage' ),
		'name_admin_bar'        => __( 'Menu', 'sage' ),
	);
	$args = array(
		'label'                 => __( 'Menu', 'sage' ),
		'description'           => __( 'Menu', 'sage' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'editor', 'thumbnail', 'revisions' ),
		'hierarchical'          => false,
		'public'                => true,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-food',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => true,
		'exclude_from_search'   => false,
		'publicly_queryable'    => true,
		'capability_type'       => 'post',
		'show_in_graphql' 		=> true,
		'graphql_single_name'	=> 'tenKitesMenu',
		'graphql_plural_name'	=> 'tenKitesMenus',
		'rewrite' => array(
            'with_front' => false,
            'slug'       => 'menu'
        )
	);
	register_post_type( 'menu', $args );

	$labels = array(
		'name'                  => _x( 'Rooms', 'Post Type General Name', 'sage' ),
		'singular_name'         => _x( 'Room', 'Post Type Singular Name', 'sage' ),
		'menu_name'             => __( 'Rooms', 'sage' ),
		'name_admin_bar'        => __( 'Room', 'sage' ),
	);
	$args = array(
		'label'                 => __( 'Room', 'sage' ),
		'description'           => __( 'Room', 'sage' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'editor', 'excerpt', 'thumbnail', 'revisions' ),
		'hierarchical'          => false,
		'public'                => true,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-store',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => true,
		'exclude_from_search'   => false,
		'publicly_queryable'    => true,
		'capability_type'       => 'post',
		'show_in_rest' => true,
		'show_in_graphql' 		=> true,
		'graphql_single_name'	=> 'room',
		'graphql_plural_name'	=> 'rooms',
		'rewrite' => array(
            'with_front' => false,
            'slug'       => 'room-styles'
        )
	);
	register_post_type( 'room', $args );

	$labels = array(
		'name'                  => _x( 'Areas', 'Post Type General Name', 'sage' ),
		'singular_name'         => _x( 'Area', 'Post Type Singular Name', 'sage' ),
		'menu_name'             => __( 'Bookable Areas', 'sage' ),
		'name_admin_bar'        => __( 'Bookable Areas', 'sage' ),
	);
	$args = array(
		'label'                 => __( 'Area', 'sage' ),
		'description'           => __( 'Area', 'sage' ),
		'labels'                => $labels,
		'supports'              => array( 'title', 'page-attributes' ),
		'hierarchical'          => false,
		'public'                => true,
		'show_ui'               => true,
		'show_in_menu'          => true,
		'menu_position'         => 5,
		'menu_icon'             => 'dashicons-grid-view',
		'show_in_admin_bar'     => true,
		'show_in_nav_menus'     => true,
		'can_export'            => true,
		'has_archive'           => false,
		'exclude_from_search'   => true,
		'publicly_queryable'    => false,
		'capability_type'       => 'post',
		'show_in_graphql' 		=> true,
		'graphql_single_name'	=> 'area',
		'graphql_plural_name'	=> 'areas',
		'rewrite' => array(
            'with_front' => false,
            'slug'       => 'area'
        )
	);
	register_post_type( 'area', $args );

}
add_action('init', __NAMESPACE__ . '\\create_post_type');

function create_taxonomies() {
	register_taxonomy( 'feature', array(
		0 => 'room',
	), array(
		'labels' => array(
			'name' => 'Features',
			'singular_name' => 'Feature',
			'menu_name' => 'Features',
			'all_items' => 'All Features',
			'edit_item' => 'Edit Feature',
			'view_item' => 'View Feature',
			'update_item' => 'Update Feature',
			'add_new_item' => 'Add New Feature',
			'new_item_name' => 'New Feature Name',
			'search_items' => 'Search Features',
			'popular_items' => 'Popular Features',
			'separate_items_with_commas' => 'Separate features with commas',
			'add_or_remove_items' => 'Add or remove features',
			'choose_from_most_used' => 'Choose from the most used features',
			'not_found' => 'No features found',
			'no_terms' => 'No features',
			'items_list_navigation' => 'Features list navigation',
			'items_list' => 'Features list',
			'back_to_items' => '← Go to features',
			'item_link' => 'Feature Link',
			'item_link_description' => 'A link to a feature',
		),
		'public' => true,
		'publicly_queryable' => false,
		'show_in_menu' => true,
		'show_in_nav_menus' => false,
		'show_in_rest' => false,
		'show_tagcloud' => false,
		'show_admin_column' => true,
		'sort' => true,
		'show_in_graphql' => true,
		'show_in_rest' => true,
		'graphql_single_name'	=> 'feature',
		'graphql_plural_name'	=> 'features',
	) );
}
add_action('init', __NAMESPACE__.'\\create_taxonomies');

/* Exclude Multiple Content Types From Yoast SEO Sitemap */
// add_filter( 'wpseo_sitemap_exclude_post_type', function ( $value, $post_type ) {
// 	$post_type_to_exclude = array('post_type_slug1','post_type_slug2', 'post_type_slug3');
// 	if( in_array( $post_type, $post_type_to_exclude ) ) return true;
// }, 10, 2 );

/* Exclude Multiple Taxonomies From Yoast SEO Sitemap */
add_filter( 'wpseo_sitemap_exclude_taxonomy', function ( $value, $taxonomy ) {
	$taxonomy_to_exclude = array('feature', 'post_tag', 'category');
	if( in_array( $taxonomy, $taxonomy_to_exclude ) ) return true;
}, 10, 2 );