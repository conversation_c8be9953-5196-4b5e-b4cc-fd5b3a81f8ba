{"name": "saint/nextjs-wordpress-starter", "description": "Next.js WordPress Starter", "type": "project", "license": "GPL-2.0-or-later", "authors": [{"name": "SaintDesign", "email": "<EMAIL>"}], "repositories": {"wppackagist": {"type": "composer", "url": "https://wpackagist.org/"}}, "extra": {"installer-paths": {"plugins/{$name}/": ["type:wordpress-plugin"], "mu-plugins/{$name}/": ["type:wordpress-muplugin"]}}, "config": {"platform": {"php": "7.4"}, "allow-plugins": {"composer/installers": true}}, "require": {"wpackagist-plugin/wp-graphql": "^1.29.3", "wpackagist-plugin/add-wpgraphql-seo": "^4.22.5", "wp-graphql/wp-graphql-jwt-authentication": "^0.6", "wp-graphql/wp-graphql-acf": "^0.6.1", "valu/wp-graphql-offset-pagination": "^0.2"}}