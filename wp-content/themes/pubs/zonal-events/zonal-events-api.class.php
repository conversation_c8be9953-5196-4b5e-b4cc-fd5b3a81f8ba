<?php

/**
 * ZonalEvents-API-PHP : Simple PHP wrapper for the ZonalEvents API
 *
 * PHP version 7.x
 *
 * @category Awesomeness
 * @package  ZonalEvents-API-PHP
 * <AUTHOR> <<EMAIL>>
 * @license  http://opensource.org/licenses/gpl-license.php GNU Public License
 * @link     https://saintdesign.co.uk
 */
class ZonaleEventsAPI {

    private $instance;
    private $api_key;
    private $api_password;

    private $token = null;
    private $data = null;

    private $assets_timestamp = '20221215-01';

    /**
     * Create the API access object.
     */
    public function __construct()
    {
        if (!in_array('curl', get_loaded_extensions()))
        {
            throw new Exception('You need to install cURL, see: http://curl.haxx.se/docs/install.html');
        }

        // TODO: turn this to 'false' for default DEV/LIVE functionality based on SAINT_ENV, see below
        $live_testing_mode = true;

        // === Set Environment
        if( strtolower(SAINT_ENV) == 'dev' && !$live_testing_mode ) { // STAGING
            $this->instance     = 'https://api-staging.liveres.co.uk/events/v1';
            $this->api_key      = '033232fc-c1c3-4b89-961d-7b87d18bac60';
            $this->api_password = 'qe@X*!wd0^A8';
        }else { // LIVE
            $this->instance     = 'https://api.liveres.co.uk/events/v1';
            $this->api_key      = '6398dd0e-2106-4989-8074-ca572c399a0f';
            $this->api_password = 'bJ9FwKi#%2o5';
        }

        if( !is_admin() ) {

            add_action( 'rest_api_init', function () {
                /**
                 * Get Occasiosn
                 * zonal/v1/occasions
                 */
                register_rest_route( 'zonal/v1', '/occasions', array(
                    'methods'          => 'POST',
                    'callback'         => array($this, 'getRestaurantOccasions'),
                    'permission_callback' => '__return_true',
                    ) );
                /**
                 * Get Restrictions (min, max party size) / restrictions
                 * zonal/v1/occasions
                 */
                register_rest_route( 'zonal/v1', '/restrictions', array(
                    'methods'          => 'POST',
                    'callback'         => array($this, 'getRestrictions'),
                    'permission_callback' => '__return_true',
                    ) );
                /**
                 * Get Menus /menus
                 * zonal/v1/menus
                 */
                register_rest_route( 'zonal/v1', '/menus', array(
                    'methods'          => 'POST',
                    'callback'         => array($this, 'getRestaurantMenus'),
                    'permission_callback' => '__return_true',
                    ) );
                /**
                 * Get Areas /areas
                 * zonal/v1/areas
                 */
                register_rest_route( 'zonal/v1', '/areas', array(
                    'methods'          => 'POST',
                    'callback'         => array($this, 'getRestaurantAreas'),
                    'permission_callback' => '__return_true',
                    ) );
                /**
                 * Get Upsells /upsells
                 * zonal/v1/upsells
                 */
                register_rest_route( 'zonal/v1', '/upsells', array(
                    'methods'          => 'POST',
                    'callback'         => array($this, 'getRestaurantUpsells'),
                    'permission_callback' => '__return_true',
                    ) );
                /**
                 * Get Availabilty /slots
                 * zonal/v1/slots
                 */
                register_rest_route( 'zonal/v1', '/slots', array(
                    'methods'          => 'POST',
                    'callback'         => array($this, 'getRestaurantSlots'),
                    'permission_callback' => '__return_true',
                    ) );
                /**
                 * Create Booking /booking
                 * zonal/v1/booking
                 */
                register_rest_route( 'zonal/v1', '/booking', array(
                    'methods'          => 'POST',
                    'callback'         => array($this, 'createEvent'),
                    'permission_callback' => '__return_true',
                    ) );
                /**
                 * Create Enquiry /enquiry
                 * zonal/v1/enquiry
                 */
                register_rest_route( 'zonal/v1', '/enquiry', array(
                    'methods'          => 'POST',
                    'callback'         => array($this, 'createEnquiry'),
                    'permission_callback' => '__return_true',
                    ) );
            });

        }

    }

    /**
     * Restrictions
     * Detail any restrictions for selected items
     * /restrictions
     * param $siteId
     * param $occasionId
     * return [minCovers, maxCovers]
     */
    public function getRestrictions(WP_REST_Request $request) {
        $data = $request->get_body_params();
        // --- Auth connection
        if( !isset($data['api_secret']) || $data['api_secret'] != PREVIEW_SECRET_TOKEN ) {
            return rest_ensure_response( array(
            'code' => 401,
            'message' => 'Authorization needed.'
            ) );
        }

        $params = array(
            'siteId' => isset($data['siteId']) && !empty($data['siteId']) ? esc_html( $data['siteId'] ) : false,
            'occasionId' => isset($data['occasionId']) && !empty($data['occasionId']) ? esc_html( $data['occasionId'] ) : false,
        );

        if( $params['siteId'] && $params['occasionId'] ) {
            $endpoint = '/restrictions?'.http_build_query($params);
            $response = $this->sendData($endpoint, 'GET');
            return rest_ensure_response($response);
        }else {
            return rest_ensure_response(false);
        }

    }

    /**
     * List occasions
     * List occasions and their availability
     * GET /occasions
     */
    public function getRestaurantOccasions(WP_REST_Request $request) {
        $data = $request->get_body_params();
        // --- Auth connection
        if( !isset($data['api_secret']) || $data['api_secret'] != PREVIEW_SECRET_TOKEN ) {
            return rest_ensure_response( array(
            'code' => 401,
            'message' => 'Authorization needed.'
            ) );
        }

        $date = date_create( esc_html( $data['booking_date'] ) );
        $formated_date = date_format( $date, 'Y-m-d' );
        $date_mode = isset($data['date_mode']) && !empty($data['date_mode']) ? esc_html( $data['date_mode'] ) : 'single';
        $from = isset($data['from']) && !empty($data['from']) ? esc_html( $data['from'] ) : false;
        $until = isset($data['until']) && !empty($data['until']) ? esc_html( $data['until'] ) : false;

        $params = array(
            'siteId' => isset($data['siteId']) && !empty($data['siteId']) ? esc_html( $data['siteId'] ) : false
        );

        if( $date_mode == 'range' ) {
            $params['from'] = $from;
            $params['until'] = $until;
        }else {
            $params['dates'] = $formated_date ?: false;
        }


        if( $params['siteId'] ) {
            $endpoint = '/occasions?'.http_build_query($params);
            $response = $this->sendData($endpoint, 'GET');
            $response['raw_data'] = $data;
            return rest_ensure_response($response);
        }else {
            return rest_ensure_response(false);
        }
    }

    /**
     * Time slots
     * GET /slots
     * The availability request is an HTTP GET request with the following structures
     */
    public function getRestaurantSlots(WP_REST_Request $request) {

        $data = $request->get_body_params();
        // --- Auth connection
        if( !isset($data['api_secret']) || $data['api_secret'] != PREVIEW_SECRET_TOKEN ) {
            return rest_ensure_response( array(
            'code' => 401,
            'message' => 'Authorization needed.'
            ) );
        }

        $date = date_create( esc_html( $data['booking_date'] ) );
        $formated_date = date_format( $date, 'Y-m-d' );
        $date_mode = isset($data['date_mode']) && !empty($data['date_mode']) ? esc_html( $data['date_mode'] ) : 'single';
        $from = isset($data['from']) && !empty($data['from']) ? esc_html( $data['from'] ) : false;
        $until = isset($data['until']) && !empty($data['until']) ? esc_html( $data['until'] ) : false;

        $params = array(
            'siteId' => isset($data['rest_code']) && !empty($data['rest_code']) ? esc_html( $data['rest_code'] ) : false,
            'occasionId' => isset($data['occasionId']) && !empty($data['occasionId']) ? esc_html( $data['occasionId'] ) : null,
            'areaId' => isset($data['area_id']) && !empty($data['area_id']) ? esc_html( $data['area_id'] ) : null,
            'adults' => isset( $data['party_size'] ) && !empty( $data['party_size'] ) ? esc_html( $data['party_size'] ) : false
        );

        if( $date_mode == 'range' ) {
            $params['from'] = $from;
            $params['until'] = $until;
        }else {
            $params['dates'] = $formated_date ?: false;
        }

        if( $params['siteId'] && ( isset($params['dates']) || isset($params['from']) ) ) {
            $endpoint = '/slotAreas?'.http_build_query($params);
            $response = $this->sendData($endpoint, 'GET');
            $response['raw_data'] = $data;
            return rest_ensure_response($response);
        }else {
            return rest_ensure_response(false);

        }
    }

    /**
     * List Areas
     * List Areas and their availability
     * GET /areas
     */
    public function getRestaurantAreas(WP_REST_Request $request) {
        $data = $request->get_body_params();
        // --- Auth connection
        if( !isset($data['api_secret']) || $data['api_secret'] != PREVIEW_SECRET_TOKEN ) {
            return rest_ensure_response( array(
            'code' => 401,
            'message' => 'Authorization needed.'
            ) );
        }

        $date = date_create( esc_html( $data['dates'] ) );
        $formated_date = date_format( $date, 'Y-m-d' );
        // $time = esc_html( $data['time'] );

        $params = array(
            'siteId' => isset($data['siteId']) && !empty($data['siteId']) ? esc_html( $data['siteId'] ) : false,
            'occasionId' => isset($data['occasionId']) && !empty($data['occasionId']) ? esc_html( $data['occasionId'] ) : null,
            'adults' => isset($data['adults']) && !empty($data['adults']) ? esc_html( $data['adults'] ) : null,
            "dates" => $formated_date,
            // "time" => $time,
        );
        if( $params['siteId'] ) {
            $endpoint = '/areas?'.http_build_query($params);
            $response = $this->sendData($endpoint, 'GET');
            $response['raw_data'] = $data;
            return rest_ensure_response($response);
        }else {
            return rest_ensure_response(false);
        }
    }

     /**
     * List menus
     * List menus and their availability
     * GET /menus
     */
    public function getRestaurantMenus(WP_REST_Request $request) {
        $data = $request->get_body_params();
        // --- Auth connection
        if( !isset($data['api_secret']) || $data['api_secret'] != PREVIEW_SECRET_TOKEN ) {
            return rest_ensure_response( array(
            'code' => 401,
            'message' => 'Authorization needed.'
            ) );
        }

        $date = date_create( esc_html( $data['dates'] ) );
        $formated_date = date_format( $date, 'Y-m-d' );
        $time = esc_html( $data['time'] );

        $params = array(
            'siteId' => isset($data['siteId']) && !empty($data['siteId']) ? esc_html( $data['siteId'] ) : false,
            'occasionId' => isset($data['occasionId']) && !empty($data['occasionId']) ? esc_html( $data['occasionId'] ) : null,
            'adults' => isset($data['adults']) && !empty($data['adults']) ? esc_html( $data['adults'] ) : null,
            "dates" => $formated_date,
            "time" => $time,
        );
        if( $params['siteId'] ) {
            $endpoint = '/menus?'.http_build_query($params);
            $response = $this->sendData($endpoint, 'GET');
            $response['raw_data'] = $data;
            return rest_ensure_response($response);
        }else {
            return rest_ensure_response(false);
        }
    }

    /**
     * List upsells
     * List upsells and their availability
     * GET /upsells
     */
    public function getRestaurantUpsells(WP_REST_Request $request) {
        $data = $request->get_body_params();
        // --- Auth connection
        if( !isset($data['api_secret']) || $data['api_secret'] != PREVIEW_SECRET_TOKEN ) {
            return rest_ensure_response( array(
            'code' => 401,
            'message' => 'Authorization needed.'
            ) );
        }

        $date = date_create( esc_html( $data['dates'] ) );
        $formated_date = date_format( $date, 'Y-m-d' );
        $time = esc_html( $data['time'] );

        $params = array(
            'siteId' => isset($data['siteId']) && !empty($data['siteId']) ? esc_html( $data['siteId'] ) : false,
            'occasionId' => isset($data['occasionId']) && !empty($data['occasionId']) ? esc_html( $data['occasionId'] ) : null,
            'adults' => isset($data['adults']) && !empty($data['adults']) ? esc_html( $data['adults'] ) : null,
            "dates" => $formated_date,
            "time" => $time,
        );
        if( $params['siteId'] ) {
            $endpoint = '/upsells?'.http_build_query($params);
            $response = $this->sendData($endpoint, 'GET');
            $response['raw_data'] = $data;
            return rest_ensure_response($response);
        }else {
            return rest_ensure_response(false);
        }
    }

    /**
     * Create Event
     * POST /events
     * The create event request is an HTTP POST request with the following structure
    */
    public function createEvent(WP_REST_Request $request) {
        $data = $request->get_body_params();
        // --- Auth connection
        if( !isset($data['api_secret']) || $data['api_secret'] != PREVIEW_SECRET_TOKEN ) {
            return rest_ensure_response( array(
            'code' => 401,
            'message' => 'Authorization needed.'
            ) );
        }

        $date = date_create( esc_html( $data['date'] ) );
        $formated_date = date_format( $date, 'Y-m-d' );
        $time = esc_html( $data['time'] );

        $this->data = array(
            "siteId" => esc_html( $data['siteId'] ),
            "occasionId" => esc_html( $data['occasionId'] ),
            "menuIds" => !empty($data['menuIds']) ? explode(',', $data['menuIds']) : [],
            "areaId" => !empty($data['areaId']) ? esc_html( $data['areaId'] ) : null,
            "upsellIds" => !empty($data['upsellIds']) ? explode(',', $data['upsellIds']) : null,
            "date" => $formated_date,
            "time" => $time,
            "adults" => esc_html( $data['adults'] ),
            "children" => esc_html( $data['children'] ),
            "firstname" => esc_html( $data['firstname'] ),
            "lastname" => esc_html( $data['lastname'] ),
            "emailAddress" => esc_html( $data['emailAddress'] ),
            "telephoneNumber" => esc_html( str_replace(' ','',trim($data['telephoneNumber'])) ),
            "consent" => array(
                "email" => esc_html( $data['EmailOptIn'] ) ?: null,
                "sms" => esc_html( $data['SmsOptIn'] ) ?: null,
            )
        );

        // === Check if PromoCode sent
        if( isset($data['promocode']) && $data['promocode'] && $data['promocode'] != 'null' ) $this->data['SpecialRequest'] = 'PromoCode: ' . $data['promocode'];

        $endpoint = '/events';
        $response = $this->sendData($endpoint);

        return rest_ensure_response(array(
            'request'   => $this->data,
            'response'  => $response,
            "inputDateTime" => esc_html( $data['date'] ),
            "outputDateTime" => date_format( $date, 'Y-m-d' ),
            'raw_data' => $data
        ));

    }

    /**
     * Create Enquiry
     * POST /enquiries
     * The create event request is an HTTP POST request with the following structure
    */
    public function createEnquiry(WP_REST_Request $request) {
        $data = $request->get_body_params();
        // --- Auth connection
        if( !isset($data['api_secret']) || $data['api_secret'] != PREVIEW_SECRET_TOKEN ) {
            return rest_ensure_response( array(
            'code' => 401,
            'message' => 'Authorization needed.'
            ) );
        }

        $date = date_create( esc_html( $data['date'] ) );
        $formated_date = date_format( $date, 'Y-m-d' );
        $time = esc_html( $data['time'] );

        $this->data = array(
            "siteId" => esc_html( $data['siteId'] ),
            "occasionId" => esc_html( $data['occasionId'] ),
            "menuIds" => !empty($data['menuIds']) ? explode(',', $data['menuIds']) : [],
            "areaId" => !empty($data['areaId']) ? esc_html( $data['areaId'] ) : null,
            "upsellIds" => !empty($data['upsellIds']) ? explode(',', $data['upsellIds']) : null,
            "date" => $formated_date,
            "time" => $time,
            "adults" => esc_html( $data['adults'] ),
            "children" => esc_html( $data['children'] ),
            "firstname" => esc_html( $data['firstname'] ),
            "lastname" => esc_html( $data['lastname'] ),
            "emailAddress" => esc_html( $data['emailAddress'] ),
            "telephoneNumber" => esc_html( str_replace(' ','',trim($data['telephoneNumber'])) ),
            "consent" => array(
                "email" => esc_html( $data['EmailOptIn'] ) ?: null,
                "sms" => esc_html( $data['SmsOptIn'] ) ?: null,
            )
        );

        $endpoint = '/enquiries';
        $response = $this->sendData($endpoint);

        return rest_ensure_response(array(
            'request'   => $this->data,
            'response'  => $response,
            "inputDateTime" => esc_html( $data['date'] ),
            "outputDateTime" => date_format( $date, 'Y-m-d' ),
            'raw_data' => $data
        ));

    }


    // ============================ OLD API =====================================

    /**
     * Health Check
     * The restaurant sessions request is an HTTP GET request with the following structure.
     * NO AUTHENTICATION REQUIRED
     */
    public function healthCheck() {

        $headers = array(
            'Accept: application/json',
            'Content-Type: application/json',
        );

        $defaults = array(
            CURLOPT_URL => $this->instance,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FRESH_CONNECT => 1,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_FORBID_REUSE => 1,
            CURLOPT_CUSTOMREQUEST => 'GET'
        );

        try{
			$ch = curl_init();
			curl_setopt_array($ch, $defaults);

			$result = json_decode(curl_exec($ch), true);

			if(FALSE === $result){
				throw new Exception(curl_error($ch), curl_errno($ch));
			}
			curl_close($ch);
		}catch(Exception $e){
          return 'Caught exception:' . $e->getMessage();
		}
		return $result;

    }

    /**
     * Send call to API endpoint
     * @param endpoint string API endpoint
     * @param methos string HTTP request method
     */
    public function sendData( $endpoint = '', $method = 'POST' ){

        $json = json_encode($this->data);

        $headers = array(
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Basic '. base64_encode( $this->api_key . ":" . $this->api_password )
        );

        $result = 'jojo';

        if( $method == 'GET' ) {
            $defaults = array(
                CURLOPT_URL => $this->instance . $endpoint,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FRESH_CONNECT => 1,
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_FORBID_REUSE => 1,
                CURLOPT_CUSTOMREQUEST => $method
            );
        }else{
            $defaults = array(
                CURLOPT_URL => $this->instance . $endpoint,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_FRESH_CONNECT => 1,
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_FORBID_REUSE => 1,
                CURLOPT_POSTFIELDS => $json ?: false,
                CURLOPT_POST => 1,
                CURLOPT_CUSTOMREQUEST => $method
            );
        }

		try{
			$ch = curl_init();
			curl_setopt_array($ch, $defaults);

			$result = json_decode(curl_exec($ch), true);
            $StatusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

			if(FALSE === $result){
				throw new Exception(curl_error($ch), curl_errno($ch));
			}
			curl_close($ch);
		}catch(Exception $e){
          return 'Caught exception:' . $e->getMessage();
		}
		return array(
            "data" => $result,
            "StatusCode" => $StatusCode
        );
	}

}
$zonal_events = new ZonaleEventsAPI();