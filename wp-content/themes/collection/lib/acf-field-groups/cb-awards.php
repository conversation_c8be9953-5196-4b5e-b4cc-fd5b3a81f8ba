<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_663e220e714b6',
	'title' => 'Content Block: Awards',
	'fields' => array(
		array(
			'key' => 'field_663e220e2aaa0',
			'label' => 'Count',
			'name' => 'awards_count',
			'aria-label' => '',
			'type' => 'number',
			'instructions' => 'leave empty to show all',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'min' => '',
			'max' => '',
			'placeholder' => '',
			'step' => '',
			'prepend' => '',
			'append' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'block',
				'operator' => '==',
				'value' => 'acf/awards',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 0,
	'graphql_field_name' => 'contentBlock:Awards',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );

