<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_61164b1a5c816',
	'title' => 'Options - General',
	'fields' => array(
		array(
			'key' => 'field_61979d856e36c',
			'label' => 'General',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'placement' => 'left',
			'endpoint' => 0,
		),
		array(
			'key' => 'field_648c70a1998db',
			'label' => 'Brand',
			'name' => 'opt_brand',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'hwi' => 'Heartwood Pubs & Inns (master)',
				'wbc' => 'Heartwood Pubs & Inns (child)',
				'bb' => 'Brasserie Blanc',
				'hwc' => 'Heartwood Collection',
			),
			'default_value' => 'hwc',
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'ui' => 1,
			'ajax' => 0,
			'placeholder' => '',
		),
		array(
			'key' => 'field_61164b2753668',
			'label' => 'Logo',
			'name' => 'opt_logo',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'wpml_cf_preferences' => 3,
			'return_format' => 'array',
			'preview_size' => 'medium',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
		),
		array(
			'key' => 'field_61164b8b302bf',
			'label' => 'Social channels',
			'name' => 'opt_socials',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'table',
			'pagination' => 0,
			'min' => 1,
			'max' => 4,
			'collapsed' => '',
			'button_label' => 'Add social channel',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_61164b99302c0',
					'label' => 'Provider',
					'name' => 'provider',
					'aria-label' => '',
					'type' => 'select',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '34',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'choices' => array(
						'facebook' => 'Facebook',
						'twitter' => 'Twitter',
						'instagram' => 'Instagram',
						'linkedin' => 'LinkedIn',
					),
					'default_value' => false,
					'return_format' => 'value',
					'multiple' => 0,
					'allow_null' => 0,
					'ui' => 0,
					'ajax' => 0,
					'placeholder' => '',
					'parent_repeater' => 'field_61164b8b302bf',
				),
				array(
					'key' => 'field_61164c30302c1',
					'label' => 'Link',
					'name' => 'link',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'wpml_cf_preferences' => 1,
					'default_value' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'maxlength' => '',
					'parent_repeater' => 'field_61164b8b302bf',
				),
			),
		),
		array(
			'key' => 'field_6489935ec4301',
			'label' => 'Integrations',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'placement' => 'left',
			'endpoint' => 0,
		),
		array(
			'key' => 'field_648997d62d6bc',
			'label' => 'Cookiebot ID',
			'name' => 'opt_cookiebot',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(example: ae13daba-0b29-4a9c-930b-6546bf0522bd)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_64899378c4302',
			'label' => 'Google Tag Manager',
			'name' => 'opt_gtm',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(only ID, example: GTM-P4FCHFZ)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_648993bcc4303',
			'label' => 'Facebook Pixel',
			'name' => 'opt_pixel',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(only ID, example: 829773804199314)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_65e7156eba1ef',
			'label' => 'Video assets',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'placement' => 'left',
			'endpoint' => 0,
		),
		array(
			'key' => 'field_65e7157eba1f0',
			'label' => 'Assets timestamp',
			'name' => 'opt_assets_timestamp',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(Change it every time you upload new batch of videos, it will add as a parameter to video url\'s effectively force browsers to use new versions instead of cache. Use something unique like current date: 2024-03-04)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'options_page',
				'operator' => '==',
				'value' => 'heartwood-settings',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 1,
	'graphql_field_name' => 'optGeneral',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );
