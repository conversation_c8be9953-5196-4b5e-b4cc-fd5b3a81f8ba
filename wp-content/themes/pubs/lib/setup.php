<?php
namespace Saint;

/**
 * Theme setup
 */
function setup() {
  // Enable features from Soil when plugin is activated
  // https://roots.io/plugins/soil/
  add_theme_support('soil-clean-up');
  add_theme_support('soil-nav-walker');
  add_theme_support('soil-nice-search');
  add_theme_support('soil-jquery-cdn');
  add_theme_support('soil-relative-urls');

  // Make theme available for translation
  // Community translations can be found at https://github.com/roots/sage-translations
  load_theme_textdomain('sage', get_template_directory() . '/lang');

  // Enable plugins to manage the document title
  // http://codex.wordpress.org/Function_Reference/add_theme_support#Title_Tag
  add_theme_support('title-tag');

  // Register wp_nav_menu() menus
  // http://codex.wordpress.org/Function_Reference/register_nav_menus
  register_nav_menus([
    'primary_navigation' => __('Primary Navigation', 'sage')
  ]);
  register_nav_menus([
    'footer_navigation' => __('Footer Navigation', 'sage')
  ]);

  // Enable post thumbnails
  // http://codex.wordpress.org/Post_Thumbnails
  // http://codex.wordpress.org/Function_Reference/set_post_thumbnail_size
  // http://codex.wordpress.org/Function_Reference/add_image_size
  add_theme_support('post-thumbnails');

  // Add image sizes
  add_image_size('portfolio-thumb', 1000, 9999, false);
  add_image_size('portfolio-half', 1116, 9999, false);
  add_image_size('portfolio-full', 1920, 9999, false);

  // Enable post formats
  // http://codex.wordpress.org/Post_Formats
  add_theme_support('post-formats', ['aside', 'gallery', 'link', 'image', 'quote', 'video', 'audio']);

  // Enable HTML5 markup support
  // http://codex.wordpress.org/Function_Reference/add_theme_support#HTML5
  add_theme_support('html5', ['caption', 'comment-form', 'comment-list', 'gallery', 'search-form']);

  // Use main stylesheet for visual editor
  // To add custom styles edit /assets/styles/layouts/_tinymce.scss
  // add_editor_style('styles/main.css');

  /**
   * Add stylesheet to Gutenberg editor
   */
  add_action( 'enqueue_block_editor_assets', function() {
    wp_enqueue_style( 'sage/gutenberg.css', HEADLESS_FRONTEND_URL.'/css/cms.css', false, ASSETS_TIMESTAMP, 'all' );
    wp_enqueue_script( 'sage/block-filters.js', get_stylesheet_directory_uri() . '/js/block-filters.js', array( 'wp-hooks' ), '1.0.0', true );

  }, 95 );
}
add_action('after_setup_theme', __NAMESPACE__ . '\\setup');

/**
 * Add custom image sizes to resolution dropdown
 */
function add_custom_sizes_to_dropdown($sizes){
  $custom_sizes = array(
  'portfolio-thumb' => 'Portfolio thumb (1000)',
  'portfolio-half' => 'Portfolio half (1116)',
  'portfolio-full' => 'Portfolio full (1920)',
  );
  return array_merge( $sizes, $custom_sizes );
}
add_filter('image_size_names_choose', __NAMESPACE__ . '\\add_custom_sizes_to_dropdown');

/**
 * Disable image scaling
 */
add_filter( 'big_image_size_threshold', '__return_false' );

/**
 * Register sidebars
 */
function widgets_init() {
  register_sidebar([
    'name'          => __('Primary', 'sage'),
    'id'            => 'sidebar-primary',
    'before_widget' => '<section class="widget %1$s %2$s">',
    'after_widget'  => '</section>',
    'before_title'  => '<h3>',
    'after_title'   => '</h3>'
  ]);

  register_sidebar([
    'name'          => __('Footer', 'sage'),
    'id'            => 'sidebar-footer',
    'before_widget' => '<section class="widget %1$s %2$s">',
    'after_widget'  => '</section>',
    'before_title'  => '<h3>',
    'after_title'   => '</h3>'
  ]);
}
add_action('widgets_init', __NAMESPACE__ . '\\widgets_init');

/**
 * Theme assets
 */
function assets() {
  //
}
add_action('wp_enqueue_scripts', __NAMESPACE__ . '\\assets', 100);