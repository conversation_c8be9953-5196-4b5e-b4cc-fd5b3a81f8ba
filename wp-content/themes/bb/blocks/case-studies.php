<?php
/**
 * Case Studies - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'studies-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-studies my-6'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
$posts_count = get_field('studies_count') ?: 1;
$offset = get_field('studies_offset') ?: 0;
$posts = get_posts(array(
    'post_type' => 'case',
    'offset' => $offset,
    'posts_per_page' => $posts_count
));
if( !$posts ) return;
?>
<div data-animated="true" id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?> <?= $image_align ?>">

    <?php foreach( $posts as $post ): ?>
        <div class="item row my-40 py-40 bg-light">
            <div class="col-12col-md-4 col-lg-6">
                <?php if( has_post_thumbnail( $post ) ): ?>
                    <figure class="image">
                        <?= get_the_post_thumbnail( $post, 'medium_large', array('class'=>'img-fluid') ) ?>
                    </figure>
                <?php endif; ?>
            </div>
            <div class="col-12 col-md-8 col-lg-6">
                <h3 class="title"><?= get_the_title( $post ) ?></h3>
                <div class="content">
                    <?= get_the_content( '', false, $post ) ?>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

</div><!-- .block-studies -->