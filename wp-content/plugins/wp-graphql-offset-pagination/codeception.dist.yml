paths:
    tests: tests
    output: tests/_output
    data: tests/_data
    support: tests/_support
    envs: tests/_envs
actor_suffix: Tester
settings:
    colors: true
    memory_limit: 1024M
extensions:
    enabled:
        - Codeception\Extension\RunFailed
    commands:
        - Codeception\Command\GenerateWPUnit
        - Codeception\Command\GenerateWPRestApi
        - Codeception\Command\GenerateWPRestController
        - Codeception\Command\GenerateWPRestPostTypeController
        - Codeception\Command\GenerateWPAjax
        - Codeception\Command\GenerateWPCanonical
        - Codeception\Command\GenerateWPXMLRPC
params:
    - .env
modules:
    config:
        WPLoader:
            wpRootFolder: "%WPTT_INSTALL_DIR%/web"
            dbName: "%WPTT_DB_NAME%"
            dbHost: "%WPTT_DB_HOST%"
            dbUser: "%WPTT_DB_USER%"
            dbPassword: "%WPTT_DB_PASSWORD%"
            tablePrefix: "_wp"
            domain: "%WPTT_SITE_HOST%"
            adminEmail: "%WPTT_SITE_ADMIN_EMAIL%"
            title: "Test"
            plugins:
                - "wp-graphql/wp-graphql.php"
                - "wp-graphql-offset-pagination/plugin.php"
            activatePlugins:
                - "wp-graphql/wp-graphql.php"
                - "wp-graphql-offset-pagination/plugin.php"
            configFile: "tests/_data/config.php"
