{"key": "group_65f2cef8dfc0e", "title": "NEW Inn - checklist", "fields": [{"key": "field_660fe27f747dc", "label": "Content checklist", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0, "selected": 0}, {"key": "field_660fe1a2395f9", "label": "Notes - Content", "name": "C_checklist_notes", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 1}, {"key": "field_65f2cef9589fb", "label": "Option 1 description", "name": "c_option_1", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fe3071f6f8", "label": "Option 2 description", "name": "c_option_2", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fe30e1f6f9", "label": "Option 3 description", "name": "c_option_3", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fe31a1f6fb", "label": "Option 4 description", "name": "c_option_4", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fe2a550eb1", "label": "DEV checklist", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0, "selected": 0}, {"key": "field_660fe2aa50eb2", "label": "Notes - DEV", "name": "D_checklist_notes", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 1}, {"key": "field_660fe3ad9f323", "label": "WP SMTP - update details", "name": "d_option_1", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fe3c39f325", "label": "Add to reCaptcha admin console [production-domain]", "name": "d_option_2", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fed898abc2", "label": "Add to Cookiebot setup  [production-domain]", "name": "d_option_3", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fedf18abc6", "label": "Contact form / update details - CF7", "name": "d_option_7", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fedb28abc3", "label": "Supply the word mark", "name": "d_option_4", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fedb98abc4", "label": "Supply the swing sign", "name": "d_option_5", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fedd18abc5", "label": "Update Theme Settings: word mark, swing sign, Location details etc", "name": "d_option_6", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fee7f8abc9", "label": "Atreemo Pub ID", "name": "d_option_10", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fee218abc7", "label": "Zonal booking - update details", "name": "d_option_8", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_660fee498abc8", "label": "Guestline booking - update details", "name": "d_option_9", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}], "location": [[{"param": "options_page", "operator": "==", "value": "heartwood-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "show_in_graphql": 0, "graphql_field_name": "newInnChecklist", "map_graphql_types_from_location_rules": 0, "graphql_types": "", "modified": 1744195934}