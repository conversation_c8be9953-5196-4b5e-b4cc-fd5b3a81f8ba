<?php
if (
    !is_plugin_active( 'wordpress-seo/wp-seo.php' )
    && !is_plugin_active( 'wordpress-seo-premium/wp-seo-premium.php' )
) return false;
/**
 * Class Faqs
 */
class SaintFaqs {

    /**
     * A value object with context variables.
     *
     * @var WPSEO_Schema_Context
     */
    public $context;

    /**
     * Faqs constructor.
     *
     * @param WPSEO_Schema_Context $context Value object with context variables.
     */
    public function __construct( WPSEO_Schema_Context $context ) {
        $this->context = $context;
    }

    /**
     * Determines whether or not a piece should be added to the graph.
     *
     * @return bool Whether or not a piece should be added.
     */
    public function is_needed() {

        $faqs = get_posts(array(
            'post_type' => 'faq',
            'post_per_page' => -1
        ));

        // We only ever want to output this on post with acf/faqs bock present.
        if ($faqs && is_singular() && has_block("acf/faqs", $this->context->post) ) {
            return true;
        }

        return false;
    }

    /**
     * Adds our Faqs piece of the graph.
     *
     * @return array Faqs Schema markup.
     */
    public function generate() {

        $canonical = $this->context->canonical;
        $post = $this->context->post;
        $post_id = $post->ID;
        $faqs = get_posts(array(
            'post_type' => 'faq',
            'post_per_page' => -1
        ));

        if( $faqs ) {
            $data['@type'] = 'FAQPage';
            // $data['mainEntityOfPage'] = [ '@id' => $canonical ];
            // Set the type.
            $data['mainEntity'] = array();
            foreach($faqs as $faq) {
                $data['mainEntity'][] = array(
                    "@type" => "Question",
                    "@id" => esc_url($canonical . '#/schema/Question/' . $faq->ID),
                    "name" => strip_tags($faq->post_title),
                    "acceptedAnswer" => array(
                        "@type" => "Answer",
                        "text" =>  strip_tags($faq->post_content)
                    )
                );
            }
        }

        return $data;
    }
}