<?php
/**
 * Our rooms - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'block-rooms-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-rooms'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$posts_json = null;
$posts_per_page = get_field('rooms_qty') ?: 3;
$rooms_layout = get_field('rooms_layout') ?: 'carousel';
if( $rooms_layout == 'grid' ) $posts_per_page = -1;

$posts = get_posts(array(
    'post_type' => 'room',
    'posts_per_page' => $posts_per_page
));
if( !$posts ) return;
foreach($posts as $item) {
    $title = get_the_title( $item );
    $slug = $item->post_name;
    $featuredImage = wp_get_attachment_image_src( get_post_thumbnail_id( $item ), "medium_large" );
    $excerpt = get_the_excerpt( $item );
    $posts_json[] = array(
        "node" => array(
            "title" => $title,
            "slug" => $slug,
            "date" => get_the_date( "MMMM d, Y", $item ),
            "excerpt" => $excerpt ? '<p>'.$excerpt.'</p>' : null,
            "roomDetails" => array(
                "guestlineRoomId" => get_field('guestline_room_id', $item) ?: null
            ),
            "featuredImage" => $featuredImage ? array(
                "node" => array(
                    "sourceUrl" => $featuredImage[0],
                    "mediaDetails" => array(
                        "sizes" => array(
                            array(
                                "width" => $featuredImage[1],
                                "height" => $featuredImage[2]
                            )
                        ),
                        "width" => $featuredImage[1],
                        "height" => $featuredImage[2]
                    )
                )
            ) : null
        )
    );
}

$posts_json = json_encode($posts_json);
// var_dump($posts_json);
if( !is_admin() ):
?>
<div data-animated="true">
    <div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
        <div data-our-rooms="true" data-layout="<?= $rooms_layout ?>" data-posts="<?= htmlspecialchars($posts_json, ENT_QUOTES, 'UTF-8') ?>" data-title="Our rooms">&nbsp;</div>
    </div>
</div><!-- .block-rooms -->
<?php
else: ?>
    <!-- Backend placeholder -->
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Our Rooms</h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>
<?php endif; ?>