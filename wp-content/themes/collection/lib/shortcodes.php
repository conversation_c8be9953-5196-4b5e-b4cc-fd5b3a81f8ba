<?php
add_action('init', function(){

    add_shortcode( 'cookiebot_declaration', function(){
        return '<div data-cookiebot-declaration="true">&nbsp;</div>';
    });

    add_shortcode( 'animated_animal', function($atts, $content){
        extract( shortcode_atts( array(
            'type' => 'bird',
            'text' => 'Default animated text goes here...',
            'class' => ''
          ), $atts ) );
        $html = '<div class="'.$class.'" data-animated-animal="true" data-type="'.$type.'" data-text="'.$text.'">&nbsp;</div>';

        return $html;
    } );

    /**
     * Locations Map: segmented by brand [BB, WBC]
     */
    add_shortcode( 'locations_map', function(){

        $markers = array(
            [
                "brand" => "bb",
                "name" => "Chancery Lane",
                "address" => "119 Chancery Ln<br>London, WC2A 1PP",
                "tel" => "01932 862 105",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc+-+Chancery+Lane/@51.5144162,-0.1139314,17z/data=!3m2!4b1!5s0x4875f0879797b99d:0xfbfe5ec504c6b264!4m6!3m5!1s0x487604b357ae7a75:0xf237f67b56ddf7ed!8m2!3d51.5144162!4d-0.1113565!16s%2Fg%2F11g_gg37b",
                "website" => "https://brasserieblanc.com/restaurants/chancery-lane/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=89",
                "image" => "",
                "position" => [
                    51.5144162,
                    -0.1139314
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Fulham Reach",
                "address" => "Goldhurst House, Parr's Wy., <br>London, W6 9AN",
                "tel" => "020 8237 5566",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc/@51.4868293,-0.2289937,17z/data=!3m1!4b1!4m6!3m5!1s0x48760fb11c290445:0xbd43034221a5d6dd!8m2!3d51.4868293!4d-0.2264188!16s%2Fg%2F11dztq6qz0",
                "website" => "https://brasserieblanc.com/restaurants/fulham-reach/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=18468",
                "image" => "",
                "position" => [
                    51.4868293,
                    -0.2289937
                ]
            ],
            [
                "brand" => "bb",
                "name" => "South Bank",
                "address" => "9 Belvedere Rd, <br>London, SE1 8YL",
                "tel" => "020 7202 8470",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc+-+Southbank/@51.5056765,-0.1175138,17z/data=!3m1!4b1!4m6!3m5!1s0x487604b7a2f31a09:0x90ce498e36ae7998!8m2!3d51.5056765!4d-0.1149389!16s%2Fg%2F11xmzjhmb",
                "website" => "https://brasserieblanc.com/restaurants/southbank/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=95",
                "image" => "",
                "position" => [
                    51.5056765,
                    -0.1175138
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Threadneedle St",
                "address" => "60 Threadneedle St, <br>London, EC2R 8HP",
                "tel" => "020 7710 9440",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc+-+Threadneedle+Street/@51.5143463,-0.0891323,17z/data=!3m1!4b1!4m6!3m5!1s0x4876035338619b1b:0x9721bdbf8d90abc8!8m2!3d51.5143463!4d-0.0865574!16s%2Fg%2F1hd__n753",
                "website" => "https://brasserieblanc.com/restaurants/threadneedle-street/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=99",
                "image" => "",
                "position" => [
                    51.5143463,
                    -0.0891323
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Beaconsfield",
                "address" => "41 London End, <br>Beaconsfield, HP9 2HW",
                "tel" => "01494 685960",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc/@51.6022471,-0.6366281,17z/data=!3m1!4b1!4m6!3m5!1s0x4876669ea9fbb75b:0x1f848d9a7be47697!8m2!3d51.6022471!4d-0.6340532!16s%2Fg%2F11b81xjxhc",
                "website" => "https://brasserieblanc.com/restaurants/beaconsfield/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=105",
                "image" => "",
                "position" => [
                    51.6022471,
                    -0.6366281
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Bournemouth",
                "address" => "105 St Michael's Rd, <br>W Cliff Rd, <br>Bournemouth, BH2 5DU",
                "tel" => "012 0220 0899",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc+-+Bournemouth/@50.715427,-1.8849814,17z/data=!3m1!4b1!4m6!3m5!1s0x4873a1b87d15bc17:0x9672a62dd4aa44e!8m2!3d50.715427!4d-1.8824065!16s%2Fg%2F11g8vptls5",
                "website" => "https://brasserieblanc.com/restaurants/bournemouth/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=18472",
                "image" => "",
                "position" => [
                    50.715427,
                    -1.8849814
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Cheltenham",
                "address" => "The Promenade, <br>Cheltenham, GL50 1NN",
                "tel" => "01242 266800",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc/@51.8964119,-2.0831575,17z/data=!3m1!4b1!4m6!3m5!1s0x48711b98723878a7:0x46a05eaba32a03ef!8m2!3d51.8964119!4d-2.0805826!16s%2Fg%2F1tl8k4mk",
                "website" => "https://brasserieblanc.com/restaurants/cheltenham/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=111",
                "image" => "",
                "position" => [
                    51.8964119,
                    -2.0831575
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Chichester",
                "address" => "Richmond House, <br>The Square, <br>Chichester, PO19 7SJ",
                "tel" => "01243 534200",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc+-+Chichester/@50.8367081,-0.7763429,17z/data=!3m2!4b1!5s0x48745284b6843635:0x2f808d5ac8881a16!4m6!3m5!1s0x48745284ca6f5e69:0x219e89c37ca104b9!8m2!3d50.8367081!4d-0.773768!16s%2Fg%2F1tg155qc",
                "website" => "https://brasserieblanc.com/restaurants/chichester/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=113",
                "position" => [
                    50.8367081,
                    -0.7763429
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Hale Barns",
                "address" => "Marriott Hotel, <br>Hale Road, Hale Barns, <br>Manchester, WA15 8XW",
                "tel" => "0161 870 2633",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc/@53.3639103,-2.3002432,17z/data=!3m1!4b1!4m6!3m5!1s0x487a53ff45b2f99b:0x763d6c033abd2370!8m2!3d53.3639103!4d-2.2976683!16s%2Fg%2F11g18wkyv_",
                "website" => "https://brasserieblanc.com/restaurants/hale-barns/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=24254",
                "image" => "",
                "position" => [
                    53.3639103,
                    -2.3002432
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Leeds",
                "address" => "4 The Embankment, <br>Leeds, LS1 4BA",
                "tel" => "0113 220 6060",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc/@53.7929815,-1.5469665,17z/data=!3m2!4b1!5s0x48795c18b5ad6dc9:0xd436b5b10323aa99!4m6!3m5!1s0x48795c18c68fc98d:0x906863a6b6fcacb6!8m2!3d53.7929815!4d-1.5443916!16s%2Fg%2F1td9_61s",
                "website" => "https://brasserieblanc.com/restaurants/leeds/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=117",
                "image" => "",
                "position" => [
                    53.7929815,
                    -1.5469665
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Milton Keynes",
                "address" => "301 Avebury Boulevard, <br>Milton Keynes, MK9 2GA",
                "tel" => "01908 546590",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc/@52.0369057,-0.7654679,17z/data=!3m1!4b1!4m6!3m5!1s0x4877aaa3b21358fd:0x68e5d1c7f6a1f8b9!8m2!3d52.0369057!4d-0.762893!16s%2Fg%2F1tflxdcw",
                "website" => "https://brasserieblanc.com/restaurants/milton-keynes/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=119",
                "image" => "",
                "position" => [
                    52.0369057,
                    -0.7654679
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Oxford",
                "address" => "71-72 Walton Street, <br>Oxford, OX2 6AG",
                "tel" => "01865 510999",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc/@51.7609243,-1.2700712,17z/data=!3m1!4b1!4m6!3m5!1s0x4876c6a081ce3109:0x4c2a3af55e532130!8m2!3d51.7609243!4d-1.2674963!16s%2Fg%2F1wt3mxn9",
                "website" => "https://brasserieblanc.com/restaurants/oxford/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=121",
                "image" => "",
                "position" => [
                    51.7609243,
                    -1.2700712
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Portsmouth",
                "address" => "1 Gunwharf Quays, <br>Portsmouth, PO1 3FR",
                "tel" => "023 9289 1320",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc/@50.79539,-1.1061869,17z/data=!3m2!4b1!5s0x48745d810d618f49:0xd1063be35e401fd5!4m6!3m5!1s0x48745d815d50868d:0x21e5836972abae91!8m2!3d50.79539!4d-1.103612!16s%2Fg%2F1thlyxjc",
                "website" => "https://brasserieblanc.com/restaurants/portsmouth/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=123",
                "image" => "",
                "position" => [
                    50.79539,
                    -1.1061869
                ]
            ],
            [
                "brand" => "bb",
                "name" => "Winchester",
                "address" => "19/20 Jewry Street, <br>Winchester, SO23 8RZ",
                "tel" => "01962 810870",
                "email" => "<EMAIL>",
                "directions" => "https://www.google.com/maps/place/Brasserie+Blanc+-+Winchester/@51.0647222,-1.3184082,17z/data=!3m2!4b1!5s0x48740c0eacc723c9:0xbb6f116dd0878ff7!4m6!3m5!1s0x48740d8b8c3964c5:0xc2962f14c3c6ad15!8m2!3d51.0647222!4d-1.3158333!16s%2Fg%2F1tj3kjmf",
                "website" => "https://brasserieblanc.com/restaurants/winchester/",
                "bookTable" => "https://brasserieblanc.com/standard-booking/?location_id=127",
                "image" => "",
                "position" => [
                    51.0647222,
                    -1.3184082
                ]
            ]
        );

        // --- Get Locations (Pubs) data
        $locations = get_all_pubs_sorted();
        foreach ( $locations as $site ) :
            switch_to_blog( $site->blog_id );
            $comingsoon = get_field('opt_coming_soon','option') ?: false;
            if( !$site->public && !$comingsoon ) continue;

            $brand = get_field('opt_brand', 'option');
            $disable_rooms = get_field('disable_rooms', 'option') ?: false;
            $opt_hotel_id = get_field('opt_hotel_id', 'option') ?: false;
            $opt_cutom_button_label = get_field('opt_cutom_button_label', 'option') ?: false;
            $opt_cutom_button_link = get_field('opt_cutom_button_link', 'option') ?: false;
            $opt_cutom_button_newtab = get_field('opt_cutom_button_newtab', 'option') ?: false;
            if( $brand !== 'hc' ) {
                $headless_config = get_option('headless_config');
                $geo_lat = get_field('opt_geo_lat', 'option') ?: 0;
                $geo_lng = get_field('opt_geo_lng', 'option') ?: 0;

                $markers[] = array(
                    "brand" => $brand,
                    "name" => get_bloginfo( "name" ),
                    "address" => get_field('opt_address_multiline', 'option'),
                    "tel" => get_field( 'opt_phone', 'option' ),
                    "email" => get_field('opt_email', 'option'),
                    "directions" => get_field('opt_directions', 'option'),
                    "website" => $headless_config['frontend_url'],
                    "bookTable" => $headless_config['frontend_url']."book-a-table/",
                    "bookStay" => $headless_config['frontend_url']."rooms/",
                    "image" => "",
                    "position" => [$geo_lat, $geo_lng],
                    "comingsoon" => $comingsoon,
                    "rooms_enabled" => !$disable_rooms && $opt_hotel_id ? true : false,
                    "custom_button" => [
                        'label'=>$opt_cutom_button_label,
                        'link'=>$opt_cutom_button_link,
                        'newtab'=> $opt_cutom_button_newtab
                    ]
                );
            }

            restore_current_blog();
        endforeach;
        restore_current_blog();

        $options = json_encode(array(
            "centerPos" => [52.412951,-0.8642876],
            "zoom" => 7,
            "showFilters" => true,
            "showPopups" => true
        ));

        $markers_json = json_encode($markers);

        return '<div data-locations-map="true" data-options="'. htmlspecialchars($options, ENT_QUOTES, 'UTF-8') .'" data-markers="'. htmlspecialchars($markers_json, ENT_QUOTES, 'UTF-8') .'">&nbsp;</div>';
    } );

    /**
     * Vidoe shortcodes to serve dual source (our solution for cross browser transparent video)
     */
    add_shortcode( 'video_transparent', function($atts, $content) {
        extract( shortcode_atts( array(
            'mp4' => false,
            'webm' => false,
            'autoplay' => '',
            'loop' => '',
            'muted' => '',
            'playsinline' => '',
            'controls' => ''
        ), $atts ) );

        $assets_timestamp = multisite_acf_option(1, 'opt_assets_timestamp') ?: false;
        $assets_timestamp_param = $assets_timestamp ? '?v='.$assets_timestamp : '';

        $html = '';

        if( in_array('autoplay', $atts) ) $autoplay = 'autoplay ';
        if( in_array('loop', $atts) ) $loop = 'loop ';
        if( in_array('muted', $atts) ) $muted = 'muted ';
        if( in_array('playsinline', $atts) ) $playsinline = 'playsinline ';
        if( in_array('controls', $atts) ) $controls = 'controls ';

        if( $mp4 || $webm ) {
            $html .= '<div class="wp-block-video"><video '.$autoplay.$loop.$muted.$playsinline.$controls.'>';
            // Provide the Safari video
            if( $mp4 ) $html .= '<source src="'.$mp4.$assets_timestamp_param.'" type="video/mp4; codecs=hvc1">';
            // .. and the Chrome video
            if( $webm ) $html .= '<source src="'.$webm.$assets_timestamp_param.'" type="video/webm">';
            $html .= '</video></div>';
        }

        return $html;
    } );

    /**
     * Awards shortcode (list awards associated with current blog ID)
     */
    add_shortcode( 'awards_feed', function($atts, $content) {
        extract( shortcode_atts( array(
            'count' => false
        ), $atts ) );

        $html = '';
        $found = 0;
        $current_blog_id = get_current_blog_id();
        // --- get Awards from Collection site
        switch_to_blog( 1 );
        $awards = get_field('opt_awards', 'option') ?: false;
        if($awards) {
            $html .= '<div class="shortcode-awards">';
                $html .= '<div class="row justify-content-center align-items-center">';
                    foreach( $awards as $award ) {
                        if( $count && $found >= $count ) break;
                        $site_selection = $award['site_selection'];
                        if( in_array($current_blog_id, array_values($site_selection)) ) {
                            $found++;
                        }else {
                            continue;
                        }
                        $img = $award['image'];
                        $img_alt = $img['alt'] ?: $img['title'];
                        $link  = $award['link'];
                        $new_tab = $award['new_tab'] ? ' target="_blank"' : '';

                        $html .= '<div class="col-12 col-md-3 px-50 px-md-15 mb-30">';
                        $html .= '<figure>';
                        if( $link ) $html .= '<a href="'.$link.'" '.$new_tab.'>';
                        $html .= '<img src="'.$img['url'].'" class="img-fluid" alt="'.$img_alt.'">';
                        if( $link ) $html .= '</a>';
                        $html .= '</figure>';
                        $html .= '</div>';
                    }
                $html .= '</div>';
            $html .= '</div>';
        }

        restore_current_blog();
        return $html;
    } );

}); // init