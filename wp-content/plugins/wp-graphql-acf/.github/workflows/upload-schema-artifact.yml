name: Upload Schema Artifact

on:
  release:
    types: [ published ]

jobs:
  run:
    runs-on: ubuntu-latest
    name: Generate and Upload WPGraphQL for ACF Schema Artifact
    services:
      mariadb:
        image: mariadb
        ports:
          - 3306:3306
        env:
          MYSQL_ROOT_PASSWORD: root
        # Ensure docker waits for mariadb to start
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP w/ Composer & WP-CLI
        uses: shivammathur/setup-php@v2
        with:
          php-version: 7.3
          extensions: mbstring, intl, bcmath, exif, gd, mysqli, opcache, zip, pdo_mysql
          coverage: none
          tools: composer, wp-cli

      - name: Setup WordPress
        run: |
          composer run install-test-env

      - name: Install WP CLI for ACF
        run: |
          wp plugin install https://github.com/hoppinger/advanced-custom-fields-wpcli/archive/refs/heads/master.zip --activate --path="/tmp/wordpress"
      - name: Import test Field Group
        run: |
          wp acf import --json_file="${GITHUB_WORKSPACE}/docs/field-group-examples-export.json" --path="/tmp/wordpress"
      - name: Generate the Static Schema
        run: |
          cd /tmp/wordpress/
          # Output: /tmp/schema.graphql
          wp graphql generate-static-schema
      - name: Upload schema as release artifact
        uses: softprops/action-gh-release@v1
        with:
          files: /tmp/schema.graphql
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
