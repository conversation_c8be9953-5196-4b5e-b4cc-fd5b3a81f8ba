<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'WPGraphQL\\ACF\\ACF' => $baseDir . '/src/class-acf.php',
    'WPGraphQL\\ACF\\ACF_Settings' => $baseDir . '/src/class-acfsettings.php',
    'WPGraphQL\\ACF\\Config' => $baseDir . '/src/class-config.php',
    'WPGraphQL\\ACF\\LocationRules' => $baseDir . '/src/location-rules.php',
);
