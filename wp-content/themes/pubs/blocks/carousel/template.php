<?php
/**
 * Carousel - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'carousel-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-carousel'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// --- Custom fields
$autoplay = get_field('carousel_autoplay') ?: false;

$options = array(
    'autoplay' => $autoplay,
);

$inner_blocks_template = array(
    array(
        'acf/carousel-slide',
        array(
            'name' => 'acf/carousel-slide',
            'data' => array(),
            'mode' => 'preview'
        ),
        array()
    ),
    array(
        'acf/carousel-slide',
        array(
            'name' => 'acf/carousel-slide',
            'data' => array(),
            'mode' => 'preview'
        ),
        array()
    ),
);

?>
<?php if ( ! $is_preview ) { ?>
    <div data-carousel="true"
        <?php
        echo wp_kses_data(
            get_block_wrapper_attributes(
                array(
                    'id'    => esc_attr($id),
                    'class' => esc_attr( $class_name ),
                )
            )
        );
        ?>
        data-options="<?php echo esc_attr( wp_json_encode( $options ) ); ?>"
    >
<?php } ?>

    <InnerBlocks
        template="<?php echo esc_attr( wp_json_encode( $inner_blocks_template ) ); ?>"
    />

<?php if ( ! $is_preview ) { ?>
    </div><!-- .block-carousel -->
<?php } ?>