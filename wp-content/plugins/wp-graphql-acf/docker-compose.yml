version: '3.3'

services:
  app:
    depends_on:
      - app_db
    image: wpgraphql-acf-app:latest
    volumes:
      - '.:/var/www/html/wp-content/plugins/wp-graphql-acf'
      - './.log/app:/var/log/apache2'
    env_file:
      - .env
    environment:
      WP_URL: 'http://localhost:8091'
      WP_DOMAIN: 'localhost:8091'
      DB_HOST: app_db
      DB_NAME: wordpress
      DB_USER: wordpress
      DB_PASSWORD: wordpress
      WP_DOMAIN: localhost
      ADMIN_EMAIL: <EMAIL>
      ADMIN_USERNAME: admin
      ADMIN_PASSWORD: password
      USING_XDEBUG: ${USING_XDEBUG:-}
      WPGRAPHQL_VERSION: ${WPGRAPHQL_VERSION:-}
    ports:
      - '8091:80'
    networks:
      local:

  app_db:
    image: mariadb:10.2
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE:      wordpress
      MYSQL_USER:          wordpress
      MYSQL_PASSWORD:      wordpress
    ports:
      - '3306'
    networks:
      testing:
      local:

  testing:
    depends_on:
      - app_db
    image: wpgraphql-acf-testing:latest
    volumes:
      - '.:/var/www/html/wp-content/plugins/wp-graphql-acf'
      - './.log/testing:/var/log/apache2'
      - './codeception.dist.yml:/var/www/html/wp-content/plugins/wp-graphql-acf/codeception.yml'
    env_file:
      - .env
    environment:
      DB_HOST: app_db
      WP_URL: 'http://localhost'
      WP_DOMAIN: 'localhost'
      SUITES: ${SUITES:-}
    networks:
      testing:

networks:
  local:
  testing:
