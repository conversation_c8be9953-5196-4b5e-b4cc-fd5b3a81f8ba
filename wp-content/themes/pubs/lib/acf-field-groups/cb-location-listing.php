<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_6543bd8d49ed7',
	'title' => 'Content Block - Location Listing',
	'fields' => array(
		array(
			'key' => 'field_6543bd8e928b7',
			'label' => 'Layout',
			'name' => 'locations_layout',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'default' => 'Default',
				'rooms' => 'Book a Stay (Rooms)',
				'areas' => 'Bookable Areas',
			),
			'default_value' => 'default',
			'return_format' => 'array',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 1,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
		),
		array(
			'key' => 'field_682f080c8fe7f',
			'label' => 'Bookable Area type',
			'name' => 'locations_area_type',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_6543bd8e928b7',
						'operator' => '==',
						'value' => 'areas',
					),
				),
			),
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'all' => 'All',
				'meetings' => 'Meetings',
				'celebrations' => 'Celebrations',
			),
			'default_value' => 'all',
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 0,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'block',
				'operator' => '==',
				'value' => 'acf/locations',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 0,
	'graphql_field_name' => 'contentBlockLocationListing',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );