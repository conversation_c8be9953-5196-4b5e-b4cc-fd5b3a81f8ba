<?php
namespace Saint;

/**
 * Add <body> classes
 */
function body_class($classes) {
  // Add page slug if it doesn't exist
  if (is_single() || is_page() && !is_front_page()) {
    if (!in_array(basename(get_permalink()), $classes)) {
      $classes[] = basename(get_permalink());
    }
  }

  return $classes;
}
add_filter('body_class', __NAMESPACE__ . '\\body_class');

/**
 * Clean up the_excerpt()
 */
function excerpt_more() {
  return ' &hellip; <a href="' . get_permalink() . '">' . __('Continued', 'sage') . '</a>';
}
add_filter('excerpt_more', __NAMESPACE__ . '\\excerpt_more');

// allow to upload SVG files
function custom_mime_types( $mimes ){
  $mimes['svg'] = 'image/svg+xml';
  return $mimes;
}
add_filter('upload_mimes', __NAMESPACE__ . '\\custom_mime_types');

function fix_svg_thumb_display() {
  echo '<style type="text/css">
    table.media .media-icon img[src$=".svg"], img[src$=".svg"].attachment-post-thumbnail {
      width: 100% !important;
      height: auto !important;
    }
  </style>';
}
add_action('admin_head', __NAMESPACE__ . '\\fix_svg_thumb_display');

/**
 * To allow users with the roles editor and administrator who can edit pages (in single and also multisite instances)
 * to edit and delete the privacy policy page
 */
add_filter('map_meta_cap', function ($caps, $cap, $user_id, $args)
{
  if (!is_user_logged_in()) return $caps;

  $user_meta = get_userdata($user_id);
  if (array_intersect(['editor', 'administrator'], $user_meta->roles)) {
    if ('manage_privacy_options' === $cap) {
      $manage_name = is_multisite() ? 'manage_network' : 'manage_options';
      $caps = array_diff($caps, [ $manage_name ]);
    }
  }
  return $caps;
}, 1, 4);

/* Remove Images From Yoast Sitemap */
add_filter( 'wpseo_xml_sitemap_img', '__return_false' );

/**
 * Display backend notice for DEV cms only
 */
add_action( 'admin_notices', function() {
  if(SAINT_ENV == "DEV"):
  ?>
  <div class="notice notice-alt notice-warning"><p>Hello folks! You're in DEVELOPMENT cms. If you want to edit PRODUCTION content then please follow this <a href="https://cms.heartwoodcollection.com/wp-admin/" target="_blank">link</a>.</p></div>
  <?php
  endif;
} );

add_action( 'init', function(){
  /**
 * GraphQL: add extra custom fields to Post object
 */
add_action( 'graphql_register_types', function() {
  $post_type = ['Page', 'Post'];
  foreach($post_type as $type) {
    register_graphql_field( $type, 'postPassword', [
      'type' => 'String',
      'description' => __( $type.' Password', 'sage' ),
      'resolve' => function( $post, $args, $context, $info ) {
        $page = get_post($post->ID);
        if(!$page) return null;
        $pass = $page->post_password;
        return $pass ?: null;
      }
  ]);
  }
});
}, 20);

/**
 * Add Query for blog posts
 */
add_action( 'graphql_register_types', function() {

  // This registers a connection to the Schema at the root of the Graph
	// The connection field name is "filteredPosts"
	register_graphql_connection( [
		'fromType'           => 'RootQuery',
		'toType'             => 'Post',
		'fromFieldName'      => 'filteredPosts', // This is the field name that will be exposed in the Schema to query this connection by
		'connectionTypeName' => 'RootQueryToFilteredPostsConnection',
		'connectionArgs'     => \WPGraphQL\Connection\PostObjects::get_connection_args(), // This adds Post connection args to the connection
		'resolve'            => function( $root, $args, \WPGraphQL\AppContext $context, $info ) {

			$resolver = new \WPGraphQL\Data\Connection\PostObjectConnectionResolver( $root, $args, $context, $info );

			// Note, these args will override anything the user passes in as { where: { ... } } args in the GraphQL Query
      $resolver->set_query_arg('post_type', 'post');
      if( isset($args['first']) && !empty($args['first']) ) $resolver->set_query_arg('posts_per_page', $args['first']);
      if( isset($args['after']) && !empty($args['after']) ) $resolver->set_query_arg('offset', $args['after']);
      if( isset($args['before']) && !empty($args['before']) ) $resolver->set_query_arg('category_name', $args['before']); // proxy for category name

			return $resolver->get_connection();
		}
	] );

},99 );