<?php
/**
 * Features Carousel - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'features-carousel-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-features-carousel'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
$features = get_field('opt_features_carousel', 'option') ?: false;
if( $features && $features["enabled"] ):
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>" data-timestamp="<?= $assets_timestamp; ?>">
    <?php echo do_shortcode( '[features_carousel]' ); ?>
</div><!-- .block-features-carousel -->
<?php endif; ?>