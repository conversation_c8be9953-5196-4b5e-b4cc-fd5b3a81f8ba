DB_NAME=wordpress
DB_HOST=app_db
DB_USER=wordpress
DB_PASSWORD=wordpress
WP_TABLE_PREFIX=wp_
WP_URL=http://localhost
WP_DOMAIN=localhost
ADMIN_EMAIL=<EMAIL>
ADMIN_USERNAME=admin
ADMIN_PASSWORD=password
ADMIN_PATH=/wp-admin

TEST_DB_NAME=wpgraphql_acf_tests
TEST_DB_HOST=127.0.0.1
TEST_DB_USER=root
TEST_DB_PASSWORD=root
TEST_WP_TABLE_PREFIX=wp_

SKIP_DB_CREATE=false
TEST_WP_ROOT_FOLDER=/tmp/wordpress
TEST_ADMIN_EMAIL=<EMAIL>

TESTS_DIR=tests
TESTS_OUTPUT=tests/_output
TESTS_DATA=tests/_data
TESTS_SUPPORT=tests/_support
TESTS_ENVS=tests/_envs

#WPGRAPHQL_VERSION=v1.3.3
SKIP_TESTS_CLEANUP=1
SUITES=wpunit

WORDPRESS_DB_HOST=${DB_HOST}
WORDPRESS_DB_USER=${DB_USER}
WORDPRESS_DB_PASSWORD=${DB_PASSWORD}
WORDPRESS_DB_NAME=${DB_NAME}
WORDPRESS_TABLE_PREFIX=${WP_TABLE_PREFIX}
