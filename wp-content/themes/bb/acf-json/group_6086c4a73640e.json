{"key": "group_6086c4a73640e", "title": "Hero content", "fields": [{"key": "field_6086c55e93213", "label": "Enable", "name": "hp_enable", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "20", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_61e8188975c44", "label": "Type", "name": "hp_type", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "20", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"image": "Image", "carousel": "Image Carousel", "video": "Video (Vimeo)"}, "default_value": "image", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_60a68424a8ece", "label": "Overlay color", "name": "hp_overlay", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"none": "None", "dusk": "Dusk (#1d1d1b)", "linen": "Linen (#fff7f1)"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_6130f19c3d419", "label": "Background color", "name": "hp_ctn_bg", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"none": "None", "dusk": "Dusk (#1d1d1b)", "linen": "Linen (#fff7f1)"}, "default_value": false, "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_61e823909340a", "label": "Video autoplay", "name": "hp_video_autoplay", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_61e8188975c44", "operator": "==", "value": "video"}]], "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 0, "ui": 1, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_61e818f7e5583", "label": "Video player URL (Vimeo)", "name": "hp_video", "aria-label": "", "type": "url", "instructions": "( in this format please: https://player.vimeo.com/video/{video_ID} )", "required": 0, "conditional_logic": [[{"field": "field_61e8188975c44", "operator": "==", "value": "video"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": ""}, {"key": "field_62823dc46c74c", "label": "Video aspect ratio - Desktop", "name": "hp_video_ratio", "aria-label": "", "type": "number", "instructions": "(height/width*100 - default is 56.25%)", "required": 0, "conditional_logic": [[{"field": "field_61e8188975c44", "operator": "==", "value": "video"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "56.25", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": "%"}, {"key": "field_62823dae6c74b", "label": "Mobile: Video player URL (Vimeo) - Optional", "name": "hp_video_mobile", "aria-label": "", "type": "url", "instructions": "( in this format please: https://player.vimeo.com/video/{video_ID} )", "required": 0, "conditional_logic": [[{"field": "field_61e8188975c44", "operator": "==", "value": "video"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": ""}, {"key": "field_62823dea6c74d", "label": "Video aspect ratio - Mobile", "name": "hp_video_mobile_ratio", "aria-label": "", "type": "number", "instructions": "(height/width*100 - default is 56.25%)", "required": 0, "conditional_logic": [[{"field": "field_61e8188975c44", "operator": "==", "value": "video"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "56.25", "min": "", "max": "", "placeholder": "", "step": "", "prepend": "", "append": "%"}, {"key": "field_60b0e4b81cfa0", "label": "Title", "name": "hp_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": "", "wpml_cf_preferences": 2}, {"key": "field_6086c4a73c267", "label": "Content", "name": "hp_content", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "40", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 0}, {"key": "field_6086c4a73c1f9", "label": "BG image", "name": "hp_bg_image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_61e8188975c44", "operator": "==", "value": "image"}]], "wrapper": {"width": "30", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}, {"key": "field_64623e2cac7d9", "label": "BG mobile image (optional)", "name": "hp_bg_image_mobile", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_61e8188975c44", "operator": "==", "value": "image"}]], "wrapper": {"width": "30", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}, {"key": "field_620e57afc36c5", "label": "Carousel images", "name": "hp_carousel_images", "aria-label": "", "type": "gallery", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_61e8188975c44", "operator": "==", "value": "carousel"}]], "wrapper": {"width": "40", "class": "", "id": ""}, "return_format": "array", "preview_size": "thumbnail", "insert": "append", "library": "all", "min": "", "max": "", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "page"}], [{"param": "post_type", "operator": "==", "value": "room"}]], "menu_order": 5, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "show_in_graphql": 1, "graphql_field_name": "Hero<PERSON><PERSON><PERSON>", "map_graphql_types_from_location_rules": 0, "graphql_types": "", "modified": 1744196003}