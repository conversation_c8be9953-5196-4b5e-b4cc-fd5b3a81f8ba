<?php
/**
 * Terms & Conditions - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'terms-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-terms'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// === Get Terms from all posts
$posts = get_posts(array(
    'posts_per_page' => -1
));
if( !$posts ) return;
$accordions = null;
foreach( $posts as $item ) {
    $terms = get_field('terms', $item->ID) ?: false;
    if( $terms ) $accordions[] = [
        'title' => get_the_title( $item->ID ),
        'url' => get_permalink( $item->ID ),
        'expiryDate' => get_field('expiry_date', $item->ID) ?: false,
        'content' => $terms
    ];
}

if( $accordions ):
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
<div class="accordion" data-bs-accordion="true">
    <?php
    $i=1;
    foreach( $accordions as $accordion ): ?>
        <div class="card" data-bs-accordion-item="true" data-key="<?= $i ?>">
            <div class="card-header mb-2 p-0" data-bs-accordion-header="true">
                <?= $accordion['title'] ?>
            </div>

            <div class="collapse" aria-labelledby="heading-<?= $i ?>" data-bs-accordion-body="true">
                <div class="card-body pt-0 pb-5">
                    <p>
                        <?php if($accordion['expiryDate']): ?> Valid until <?= trim(substr($accordion['expiryDate'], 0, strpos($accordion['expiryDate'], "at") )) ?>. <?php endif; ?>
                        <a href="<?= \SAINT_Headless_Core\replace_url($accordion['url']) ?>">Read more</a>
                    </p>
                    <?= $accordion['content'] ?>
                </div>
            </div>
        </div>
    <?php
    $i++;
    endforeach; ?>
</div>
</div><!-- .block-accordions -->
<?php
else:
    echo '<p class="alert alert-info text-center">No posts with "Terms & Conditions" found.</p>';
endif;
?>