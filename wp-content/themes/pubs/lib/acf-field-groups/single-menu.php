<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_64906d958da50',
	'title' => 'Single Menu - Details',
	'fields' => array(
		array(
			'key' => 'field_64906d95bda9f',
			'label' => 'Menu type',
			'name' => 'menu_type',
			'aria-label' => '',
			'type' => 'button_group',
			'instructions' => 'choose menu type',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'food' => 'Food',
				'drink' => 'Drink',
			),
			'default_value' => 'food',
			'return_format' => 'value',
			'allow_null' => 0,
			'allow_in_bindings' => 1,
			'layout' => 'horizontal',
		),
		array(
			'key' => 'field_677d07a887e53',
			'label' => 'Hide menu from listings and dropdowns',
			'name' => 'menu_hide',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'allow_in_bindings' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_649152d220b3f',
			'label' => 'TenKites Menu (URL)',
			'name' => 'menu_menu',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => 'please use full url',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'allow_in_bindings' => 1,
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_66278ffadf333',
			'label' => 'Custom Title (optional)',
			'name' => 'menu_custom_title',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => 'used for Menus listing and Single post title',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'allow_in_bindings' => 1,
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_type',
				'operator' => '==',
				'value' => 'menu',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 1,
	'graphql_field_name' => 'singleMenu',
	'map_graphql_types_from_location_rules' => 1,
	'graphql_types' => array(
		0 => 'TenKitesMenu',
	),
) );
} );