<?php
/**
 * Get Togethers: Footer (Features & Enquiry) - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'togethers-footer-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-togeters-footer'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// --- get ACf fields
$enquiry_form = get_field('enquiry_form') ?: false;

?>
<?php if ( ! $is_preview ) { ?>
    <div
        <?php
        echo wp_kses_data(
            get_block_wrapper_attributes(
                array(
                    'id'    => esc_attr($id),
                    'class' => esc_attr( $className ),
                )
            )
        );
        ?>
    >
<?php } ?>
<!-- ===== Section: Features ===== -->
<div class="wp-block-group bg-primary p-10 text-linen mb-50 edge-2-edge" id="pub-features">
    <div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
        <div class="wp-block-group container">
            <div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
                <div class="wp-bootstrap-blocks-row row mb-50 bg ">


                    <div class="col-12 offset-lg-1  text-center text-lg-start">

                        <h2 class="wp-block-heading" id="site-features">Site features</h2>
                    </div>



                    <div class="col-12 col-md-3 offset-lg-1  text-center text-lg-start">

                        <h3 class="wp-block-heading mt-0" id="parking">Parking</h3>
                        <div>{{parking_info}}</div>

                        <h3 class="wp-block-heading" id="public-transport-links">Public Transport Links</h3>
                        <div>{{public_transport_info}}</div>

                    </div>

                    <div class="col-12 col-md-3 offset-lg-1  text-center text-lg-start">
                        <?php echo do_shortcode( '[opening_times heading_style="h3"]' )?>
                    </div>

                    <div class="col-12 col-md-3 offset-lg-1  text-center text-lg-start">
                        <p class="font-family-headings fs-3 text-center text-lg-start mb-0">{{pubname}}</p>
                        <?php echo do_shortcode( '[contact btn="outline-linen"]' )?>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- ===== Section: Enquiry Form ===== -->
 <div id="enquire" class="wp-block-group pt-10">
     <div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
         <div class="wp-bootstrap-blocks-row row my-50">
             <div class="col-12 col-md-4 offset-lg-1  text-center text-lg-start">
                 <p class="mt-0 h2">Host your event at {{pubname}} in {{publocation}}</p>
                 <p>If you are looking to host your event in {{publocation}} at {{pubname}}, please complete the enquiry form and a member of our friendly, professional team will be in touch to discuss your requirements.</p>
             </div>
             <div class="col-12 col-lg-5 offset-lg-1  text-center text-lg-start">
                 <?php if( $enquiry_form ): echo do_shortcode( '[contact-form-7 id="'.$enquiry_form->ID.'" title="'.$enquiry_form->post_title.'"]' ); endif; ?>
             </div>

         </div>
     </div>
 </div>

<?php if ( ! $is_preview ) { ?>
    </div><!-- .block-image -->
<?php } ?>