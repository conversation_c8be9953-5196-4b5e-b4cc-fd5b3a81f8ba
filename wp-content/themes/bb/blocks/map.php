<?php
/**
 * Responsive Map embed - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'map-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-map my-6'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$headless_config = get_option('headless_config');
$geo_lat = get_field('opt_geo_lat', 'option') ?: 0;
$geo_lng = get_field('opt_geo_lng', 'option') ?: 0;

$options = json_encode(array(
    "centerPos" => [$geo_lat, $geo_lng],
    "zoom" => 10,
    "showFilters" => false,
    "showPopups" => false
));

$markers = array(
    [
        "brand" => "hc",
        "name" => get_bloginfo( "name" ),
        "address" => get_field('opt_address_multiline', 'option'),
        "tel" => get_field( 'opt_phone', 'option' ),
        "email" => get_field('opt_email', 'option'),
        "directions" => get_field('opt_directions', 'option'),
        "website" => $headless_config['frontend_url'],
        "bookTable" => $headless_config['frontend_url']."book-a-table/",
        "image" => "",
        "position" => [$geo_lat, $geo_lng]
    ]);

$markers_json = json_encode($markers);

?>
<?php if( is_admin() ): ?>
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Responsive Map</h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>
<?php else: ?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?> <?= $image_align ?>">
    <div data-animated="true" class="ratio">
        <div data-locations-map="true"
        data-options="<?= htmlspecialchars($options, ENT_QUOTES, 'UTF-8') ?>"
        data-markers="<?= htmlspecialchars($markers_json, ENT_QUOTES, 'UTF-8') ?>">&nbsp;</div>
    </div>
</div><!-- .block-map -->
<?php endif; ?>