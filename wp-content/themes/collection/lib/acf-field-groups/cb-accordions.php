<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_6286180c3278a',
	'title' => 'Content Block: Accordions',
	'fields' => array(
		array(
			'key' => 'field_62861937fb1a5',
			'label' => 'Accordions',
			'name' => 'accordions',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'collapsed' => 'field_6286196bfb1a6',
			'min' => 0,
			'max' => 0,
			'layout' => 'block',
			'button_label' => 'Add Item',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_6286196bfb1a6',
					'label' => 'Title',
					'name' => 'title',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'default_value' => '',
					'maxlength' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'parent_repeater' => 'field_62861937fb1a5',
				),
				array(
					'key' => 'field_62861970fb1a7',
					'label' => 'Content',
					'name' => 'content',
					'aria-label' => '',
					'type' => 'wysiwyg',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'default_value' => '',
					'tabs' => 'all',
					'toolbar' => 'full',
					'media_upload' => 0,
					'delay' => 0,
					'parent_repeater' => 'field_62861937fb1a5',
				),
			),
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'block',
				'operator' => '==',
				'value' => 'acf/accordions',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 0,
	'graphql_field_name' => 'gutenberg:Accordions',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );
