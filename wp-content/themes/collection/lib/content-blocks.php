<?php
/**
 * Add 'Heartwood Blocks category
 */
add_filter( 'block_categories_all' , function( $categories ) {

    // Adding a new category.
    array_unshift( $categories, array(
        'slug'  => 'heartwood',
        'title' => 'Heartwood'
    ) );

	return $categories;
} );

/**
 * Register All content blocks
 */
function register_acf_block_types() {

    /**
     * Register content block: Heartwood: Featured content with  image
     */
    acf_register_block_type(array(
        'name'              => 'featured-content',
        'title'             => __('Heartwood: Featured content with image'),
        'description'       => __('Featured content with image, content, CTA buttons'),
        'render_template'   => 'blocks/featured-content.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'featured', 'content', 'Heartwood Collection' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Call To Action
     */
    acf_register_block_type(array(
        'name'              => 'cta',
        'title'             => __('Heartwood: Call To Action'),
        'description'       => __('Call To Action'),
        'render_template'   => 'blocks/cta.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'cta', 'content', 'Heartwood Collection' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Testimonials
     */
    acf_register_block_type(array(
        'name'              => 'testimonials',
        'title'             => __('Heartwood: Testimonials'),
        'description'       => __('Testimonials carousel'),
        'render_template'   => 'blocks/testimonials.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'testimonials', 'content', 'Heartwood Collection' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

     /**
     * Register content block: Heartwood: Case Studies
     */
    acf_register_block_type(array(
        'name'              => 'studies',
        'title'             => __('Heartwood: Case Studies'),
        'description'       => __('Case Studies feed'),
        'render_template'   => 'blocks/case-studies.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'case studies', 'content', 'Heartwood Collection' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Responsive Map
     */
    acf_register_block_type(array(
        'name'              => 'map',
        'title'             => __('Heartwood: Responsive Map'),
        'description'       => __('Responsive Map'),
        'render_template'   => 'blocks/map.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'map', 'content', 'Heartwood Collection' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Accordions
     */
    acf_register_block_type(array(
        'name'              => 'accordions',
        'title'             => __('Heartwood: Accordions'),
        'description'       => __('Accordions'),
        'render_template'   => 'blocks/accordions.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'accordions', 'content', 'Heartwood Collection' ),
        'mode' => 'auto',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Accordions
     */
    acf_register_block_type(array(
        'name'              => 'socials',
        'title'             => __('Heartwood: Socials'),
        'description'       => __('Social links'),
        'render_template'   => 'blocks/socials.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'socials', 'content', 'Heartwood Collection' ),
        'mode' => 'edit',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Accordions
     */
    acf_register_block_type(array(
        'name'              => 'sitemap',
        'title'             => __('Heartwood: Sitemap'),
        'description'       => __('Website Sitemap'),
        'render_template'   => 'blocks/sitemap.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'sitemap', 'content', 'Heartwood Collection' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Transparent Video
    */
    acf_register_block_type(array(
        'name'              => 'video',
        'title'             => __('Heartwood: Transparent Video'),
        'description'       => __('Embed a video from your media library or upload a new one. Allows for dual video source to keep Safari browser happy.'),
        'render_template'   => 'blocks/video.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'video', 'transparent', 'content', 'Heartwood Pubs & Inns' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

    /**
     * Register content block: Heartwood: Accordions
     */
    acf_register_block_type(array(
        'name'              => 'awards',
        'title'             => __('Heartwood: Awards'),
        'description'       => __('Awards feed for current site'),
        'render_template'   => 'blocks/awards.php',
        'category'          => 'heartwood',
        'icon'              => 'images-alt2',
        'keywords'          => array( 'awards', 'feed', 'Heartwood Collection' ),
        'mode' => 'preview',
        'align' => 'center',
        'supports' => array(
            'anchor' => true
        )
    ));

}

/**
 * Check if function exists and hook into setup.
 */
if( function_exists('acf_register_block_type') ) {
    add_action('acf/init', 'register_acf_block_types');
}