{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "ef783f44fd70cbcb13db50153dd63a49", "packages": [{"name": "composer/installers", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/12fb2dfe5e16183de69e784a7b84046c43d97e8e", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "^1.10.27 || ^2.7", "composer/semver": "^1.7.2 || ^3.4.0", "phpstan/phpstan": "^1.11", "phpstan/phpstan-phpunit": "^1", "symfony/phpunit-bridge": "^7.1.1", "symfony/process": "^5 || ^6 || ^7"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "concreteCMS", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "matomo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "tastyigniter", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v2.3.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-06-24T20:46:46+00:00"}, {"name": "firebase/php-jwt", "version": "v6.1.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "fbb2967a3a68b07e37678c00c0cf51165051495f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/fbb2967a3a68b07e37678c00c0cf51165051495f", "reference": "fbb2967a3a68b07e37678c00c0cf51165051495f", "shasum": ""}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5||9.5"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.1.0"}, "time": "2022-03-23T18:26:04+00:00"}, {"name": "valu/wp-graphql-offset-pagination", "version": "v0.2.0", "source": {"type": "git", "url": "https://github.com/valu-digital/wp-graphql-offset-pagination.git", "reference": "e32b594d40a8974e1c6a82686bd8657699b04be6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/valu-digital/wp-graphql-offset-pagination/zipball/e32b594d40a8974e1c6a82686bd8657699b04be6", "reference": "e32b594d40a8974e1c6a82686bd8657699b04be6", "shasum": ""}, "require-dev": {"codeception/module-asserts": "^1.0", "codeception/module-cli": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-rest": "^1.0", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "lucatume/wp-browser": "^2.2", "phpunit/phpunit": "^8.0", "valu/wp-testing-tools": "^0.4.0"}, "type": "wordpress-plugin", "autoload": {"psr-4": {"WPGraphQL\\Extensions\\OffsetPagination\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Esa<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "developer"}], "description": "Add offset pagination to wp-graphql", "support": {"issues": "https://github.com/valu-digital/wp-graphql-offset-pagination/issues", "source": "https://github.com/valu-digital/wp-graphql-offset-pagination"}, "time": "2020-05-06T07:36:57+00:00"}, {"name": "wp-graphql/wp-graphql-acf", "version": "v0.6.2", "source": {"type": "git", "url": "https://github.com/wp-graphql/wp-graphql-acf.git", "reference": "131311035284db6f2ed51844bf375e057194353f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-graphql/wp-graphql-acf/zipball/131311035284db6f2ed51844bf375e057194353f", "reference": "131311035284db6f2ed51844bf375e057194353f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"codeception/module-asserts": "^1.0", "codeception/module-cli": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-rest": "^1.2", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.1", "lucatume/wp-browser": "^2.4", "phpcompatibility/phpcompatibility-wp": "2.1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^0.12.64", "phpunit/phpunit": "^8.5", "simpod/php-coveralls-mirror": "^3.0", "squizlabs/php_codesniffer": "3.5.4", "szepeviktor/phpstan-wordpress": "^0.7.1", "wp-coding-standards/wpcs": "2.1.1", "wp-graphql/wp-graphql-testcase": "~2.1"}, "type": "wordpress-plugin", "autoload": {"psr-4": {"WPGraphQL\\ACF\\": "src/"}, "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0+"], "authors": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Advanced Custom Fields bindings for wp-graphql", "support": {"issues": "https://github.com/wp-graphql/wp-graphql-acf/issues", "source": "https://github.com/wp-graphql/wp-graphql-acf/tree/v0.6.2"}, "abandoned": true, "time": "2024-07-22T18:01:33+00:00"}, {"name": "wp-graphql/wp-graphql-jwt-authentication", "version": "v0.6.0", "source": {"type": "git", "url": "https://github.com/wp-graphql/wp-graphql-jwt-authentication.git", "reference": "5a7d9a95e9e5d5e1b988bd1d6e5fa069a8b1eb69"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wp-graphql/wp-graphql-jwt-authentication/zipball/5a7d9a95e9e5d5e1b988bd1d6e5fa069a8b1eb69", "reference": "5a7d9a95e9e5d5e1b988bd1d6e5fa069a8b1eb69", "shasum": ""}, "require": {"firebase/php-jwt": "6.1.0"}, "require-dev": {"codeception/module-asserts": "^1.0", "codeception/module-cli": "^1.0", "codeception/module-db": "^1.0", "codeception/module-filesystem": "^1.0", "codeception/module-phpbrowser": "^1.0", "codeception/module-rest": "^1.2", "codeception/module-webdriver": "^1.0", "codeception/util-universalframework": "^1.0", "lucatume/wp-browser": "3.1.0", "phpunit/phpunit": "^8.5"}, "type": "wordpress-plugin", "autoload": {"psr-4": {"WPGraphQL\\JWT_Authentication\\": "src/"}, "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0+"], "authors": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "JWT Authentication for WPGraphQL", "support": {"issues": "https://github.com/wp-graphql/wp-graphql-jwt-authentication/issues", "source": "https://github.com/wp-graphql/wp-graphql-jwt-authentication/tree/v0.6.0"}, "time": "2022-10-25T14:49:08+00:00"}, {"name": "wpackagist-plugin/add-wpgraphql-seo", "version": "4.22.5", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/add-wpgraphql-seo/", "reference": "tags/4.22.5"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/add-wpgraphql-seo.4.22.5.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/add-wpgraphql-seo/"}, {"name": "wpackagist-plugin/wp-graphql", "version": "1.29.3", "source": {"type": "svn", "url": "https://plugins.svn.wordpress.org/wp-graphql/", "reference": "tags/1.29.3"}, "dist": {"type": "zip", "url": "https://downloads.wordpress.org/plugin/wp-graphql.1.29.3.zip"}, "require": {"composer/installers": "^1.0 || ^2.0"}, "type": "wordpress-plugin", "homepage": "https://wordpress.org/plugins/wp-graphql/"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "platform-overrides": {"php": "7.4"}, "plugin-api-version": "2.6.0"}