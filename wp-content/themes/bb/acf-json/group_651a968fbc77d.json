{"key": "group_651a968fbc77d", "title": "Options - Rooms (Guestline)", "fields": [{"key": "field_65709abefe022", "label": "Hotel Group (estate)", "name": "opt_hotel_group", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"HWI": "HWI (Production)", "VENDOR": "VENDOR (testing sandbox)"}, "default_value": "HWI", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_651a9690569fc", "label": "Hotel ID", "name": "opt_hotel_id", "aria-label": "", "type": "text", "instructions": "{{hotel_id}}", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_651bc60ac37cc", "label": "Disable booking", "name": "disable_rooms", "aria-label": "", "type": "true_false", "instructions": "(This button removes all book a stay buttons from child's website, and removes all related booking buttons on master pub site.)", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_65719d858c43a", "label": "Guestline RezLynx API credentials", "name": "", "aria-label": "", "type": "message", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 0, "message": "", "new_lines": "wpautop", "esc_html": 0}, {"key": "field_65719de48c43b", "label": "API Url", "name": "gl_api_url", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "https://pmsws.eu.guestline.net/RLXSoapRouter/rlxsoap.asmx", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_65719e228c43c", "label": "API Password", "name": "gl_api_pass", "aria-label": "", "type": "password", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_65719e6b8c43d", "label": "Operator Code", "name": "gl_api_operator", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_65719e978c43e", "label": "Interface ID", "name": "gl_api_interface", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": 812, "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_651a9cbef7096", "label": "Hero Content (Rooms archive)", "name": "hero_content", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "layout": "block", "sub_fields": [{"key": "field_651a96e8569fd", "label": "Rooms Hero panel", "name": "opt_rooms_hero", "aria-label": "", "type": "clone", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "clone": ["group_6086c4a73640e"], "display": "seamless", "layout": "block", "prefix_label": 0, "prefix_name": 0}]}], "location": [[{"param": "options_page", "operator": "==", "value": "heartwood-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "show_in_graphql": 1, "graphql_field_name": "optRooms", "map_graphql_types_from_location_rules": 0, "graphql_types": "", "modified": 1744195942}