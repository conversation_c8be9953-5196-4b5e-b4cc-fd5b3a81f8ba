<?php
/**
 * Latest from blog - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'latest-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-latest bg-sap text-white'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$posts_json = null;
$isCategory = false;
$type = get_field('latest_type') ?: 'all';
$cats = get_field('latest_cat') ?: false;
$qty = get_field('latest_qty') ?: false;
if( $type=='category' && $cats ) {
    $isCategory = true;
    $cats = array($cats->slug);
}
$posts_per_page = $isCategory && $qty ? $qty : 1;
if( !$cats ) $cats = array('news', 'offers', 'events', 'recipes', 'bedrooms');

foreach($cats as $cat) {
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => -1,
        'category_name' => $cat
    );
    $incoming_events = null;

    // If we are in "events" category and have incoming events (based on expiry_date custom field)
    // then find them for later exclude from default query
    // We will shift these in begining of category posts array afterwards
    if( $cat === 'events' ) {
        $args['meta_query'] = array(
            array(
                array(
                'key' => 'expiry_date',
                'value' => null,
                'compare' => '='
                )
            )
        );
        $incoming_events = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => -1,
            'category_name' => $cat,
            'meta_key' => 'expiry_date',
            'meta_type'  => 'DATETIME',
            'orderby' => 'meta_value',
            'order' => 'ASC',
            'meta_query' => array(
                array(
                  'relation' => 'AND',
                  array(
                    'key' => 'expiry_date',
                    'value' => date("Y-m-d H:i"),
                    'compare' => '>',
                    'type' => 'DATETIME',
                  ),
                  array(
                    'key' => 'expiry_date',
                    'value' => null,
                    'compare' => '!='
                  )
                )
              )
        ));
        if( $incoming_events ) {
            $args['post__not_in'] = wp_list_pluck($incoming_events, 'ID');
        }
    }

    $posts = get_posts($args);
    // var_dump(count($posts));

    // Again, if "events" category and we found "expiry_date" posts in previous search
    // we will add them now as first positions in array
    if( $cat === 'events' && $incoming_events ) {
        $posts = [...$incoming_events, ...$posts];
    }

    // trim $posts array to only given length
    $posts = array_slice($posts, 0, $isCategory ? $posts_per_page : 1);


    if( !$posts ) continue;
    foreach($posts as $item) {
        $title = get_the_title( $item );
        $slug = $item->post_name;
        $featuredImage = wp_get_attachment_image_src( get_post_thumbnail_id( $item ), "medium_large" );
        $posts_json[$cat][] = array(
            "node" => array(
                "title" => $title,
                "slug" => $slug,
                "date" => get_the_date( "MMMM d, Y", $item ),
                "excerpt" => $isCategory ? get_the_excerpt( $item ) : null,
                "singlePost" => array(
                    "expiryDate" => get_field('expiry_date', $item) ?: null
                ),
                "featuredImage" => $featuredImage ? array(
                    "node" => array(
                        "sourceUrl" => $featuredImage[0],
                        "mediaDetails" => array(
                            "sizes" => array(
                                array(
                                    "width" => $featuredImage[1],
                                    "height" => $featuredImage[2]
                                )
                            ),
                            "width" => $featuredImage[1],
                            "height" => $featuredImage[2]
                        )
                    )
                ) : null
            )
        );
    }
}

// var_dump($posts_json);
if( !is_admin() ):
    if( $posts_json ):
    ?>
    <div data-animated="true">
        <div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
            <?php if( count($cats) > 1 ): // Latest Blog
                $posts_json = json_encode($posts_json);
                ?>
                <div data-latest-blog="true" data-posts="<?= htmlspecialchars($posts_json, ENT_QUOTES, 'UTF-8') ?>" >&nbsp;</div>
            <?php else: // Latest from category
                $posts_json = json_encode(reset($posts_json));
                ?>
                <div data-latest-category="true" data-posts="<?= htmlspecialchars($posts_json, ENT_QUOTES, 'UTF-8') ?>" data-category="<?= reset($cats) ?>">&nbsp;</div>
            <?php endif; ?>
        </div>
    </div><!-- .block-latest -->
    <?php
    endif;
else: ?>
    <!-- Backend placeholder -->
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Latest from Blog <?= $isCategory && $cats ? "(".str_replace('-', ' ', $cats[0]).")" : ''; ?></h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>
<?php endif; ?>