<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_6645b4ae94286',
	'title' => 'Content Block - Newsletter signup',
	'fields' => array(
		array(
			'key' => 'field_6645b4af87946',
			'label' => 'Atreemo "SourceID" origin',
			'name' => 'newsletter_atreemo_origin',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_685e52f18a468',
			'label' => 'Submit button label',
			'name' => 'newsletter_btn_label',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => 'default to "Subscribe"',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_67363203d590f',
			'label' => 'Ignore global newsletter settings',
			'name' => 'newsletter_ignore',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => 'if you tick this box, this form will ignore the values set in Heartwood settings',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_6788c1b87793d',
			'label' => 'Layout',
			'name' => 'newsletter_layout',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'default' => 'Default',
				'short' => 'Short version (First, Last name, Email)',
				'business_membership' => 'Business Club Sign up',
			),
			'default_value' => 'default',
			'return_format' => 'array',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 0,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
			'create_options' => 0,
			'save_options' => 0,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'block',
				'operator' => '==',
				'value' => 'acf/newsletter',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 0,
	'graphql_field_name' => 'contentBlockNewsletterSignup',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );