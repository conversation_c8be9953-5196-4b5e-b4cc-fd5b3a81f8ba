<?php
if (
    !is_plugin_active( 'wordpress-seo/wp-seo.php' )
    && !is_plugin_active( 'wordpress-seo-premium/wp-seo-premium.php' )
) return false;
/**
 * Class Recipe
 */
class SaintRecipe {

    /**
     * A value object with context variables.
     *
     * @var WPSEO_Schema_Context
     */
    public $context;

    /**
     * Recipe constructor.
     *
     * @param WPSEO_Schema_Context $context Value object with context variables.
     */
    public function __construct( WPSEO_Schema_Context $context ) {
        $this->context = $context;
    }

    /**
     * Determines whether or not a piece should be added to the graph.
     *
     * @return bool Whether or not a piece should be added.
     */
    public function is_needed() {

        // We only ever want to output this on 'recipe' category post.
        if ( is_singular( 'post' ) && has_category( 'recipes' ) ) {
            return true;
        }

        return false;
    }

    /**
     * Adds our Recipe piece of the graph.
     *
     * @return array Recipe Schema markup.
     */
    public function generate() {

        $canonical = $this->context->canonical;
        $post = $this->context->post;
        $post_id = $post->ID;
        $post_image = get_the_post_thumbnail_url( $post_id );
        $recipe_ingredients = get_field('recipe_ingredients', $post_id) ?: false;
        $recipe_method_list = get_field('recipe_method_list', $post_id) ?: false;
        $recipe_video = get_field('recipe-video', $post_id) ?: false;

        // Set the type.
        $data['@type'] = 'Recipe';

        // Give it a unique ID, based on the URL and the Post ID.
        $data['@id'] = esc_url($canonical . '#/schema/Recipe/' . $post_id);

        // Give it a name.
        $data['name'] = the_title_attribute( array( 'echo' => false ) );

        $data['description'] = strip_tags(get_the_excerpt($post_id));

        // Make it the main entity of the webpage we're on.
        $data['mainEntityOfPage'] = [ '@id' => $canonical ];

        if( $post_image ) {
            $data['image'] = ["@id" => esc_url($canonical.'#primaryimage')];
        }

        if( $recipe_ingredients ) {
            $data['recipeIngredient'] = array_map(function ($item) { return strip_tags($item['recipe_ingredients_item']); }, $recipe_ingredients);
        }

        if( $recipe_method_list ) {
            $data['recipeInstructions'] = array();
            $i=1;
            foreach($recipe_method_list as $recipe_method) {
                $data['recipeInstructions'][] = array(
                    "@type" => "HowToStep",
                    "text" => strip_tags($recipe_method['recipe_method_item']),
                    "url" => esc_url($canonical . "#recipe-method-" . $i)
                );
                $i++;
            }
        }

        return $data;
    }
}