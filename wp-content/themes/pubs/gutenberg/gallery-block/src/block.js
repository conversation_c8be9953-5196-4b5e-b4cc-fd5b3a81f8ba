/**
 * WordPress Dependencies
 */
const { __ } = wp.i18n;
const { addFilter } = wp.hooks;
const { createHigherOrderComponent } = wp.compose;
const { Fragment }	= wp.element;
const { InspectorControls }	= wp.blockEditor;
const { PanelBody, ToggleControl } = wp.components;

//restrict to specific block names
const allowedBlocks = [ 'core/gallery' ];

/**
 * Add custom attribute for mobile visibility.
 *
 * @param {Object} settings Settings for the block.
 *
 * @return {Object} settings Modified settings.
 */
function addAttributes( settings ) {

	//check if object exists for old Gutenberg version compatibility
	//add allowedBlocks restriction
	if( typeof settings.attributes !== 'undefined' && allowedBlocks.includes( settings.name ) ){

		settings.attributes = Object.assign( settings.attributes, {
			addLightbox:{
				type: 'boolean',
				default: false,
			}
		});

	}

	return settings;
}

/**
 * Add mobile visibility controls on Advanced Block Panel.
 *
 * @param {function} BlockEdit Block edit component.
 *
 * @return {function} BlockEdit Modified block edit component.
 */
const withAdvancedControls = createHigherOrderComponent( ( BlockEdit ) => {
	return ( props ) => {

        // Check if the block is a gallery block
        if ( !allowedBlocks.includes(props.name)) {
            return <BlockEdit {...props} />;
        }

		const {
			name,
			attributes,
			setAttributes,
			isSelected,
		} = props;

		const {
			addLightbox,
		} = attributes;


		return (
            <Fragment>
            <BlockEdit {...props} />
            {isSelected && (
                <InspectorControls>
                    <PanelBody title="Lightbox">
                        <ToggleControl
                            label="Enable for all images"
                            checked={addLightbox}
                            onChange={() => setAttributes({ addLightbox: !addLightbox })}
                        />
                    </PanelBody>
                </InspectorControls>
            )}
        </Fragment>
		);
	};
}, 'withAdvancedControls');

/**
 * Add custom element class in save element.
 *
 * @param {Object} extraProps     Block element.
 * @param {Object} blockType      Blocks object.
 * @param {Object} attributes     Blocks attributes.
 *
 * @return {Object} extraProps Modified block element.
 */
function applyExtraClass( extraProps, blockType, attributes ) {

	const { addLightbox } = attributes;
	//check if attribute exists for old Gutenberg version compatibility
	//add class only when addLightbox = false
	//add allowedBlocks restriction
	if ( typeof addLightbox !== 'undefined' && addLightbox && allowedBlocks.includes( blockType.name ) ) {
        console.log(blockType, attributes);
        return {
            ...extraProps,
            'data-lightbox': addLightbox
        }
	}

	return extraProps;
}

//add filters

addFilter(
	'blocks.registerBlockType',
	'saint/gallery/custom-attributes',
	addAttributes
);

addFilter(
	'editor.BlockEdit',
	'saint/gallery/custom-advanced-control',
	withAdvancedControls
);

addFilter(
	'blocks.getSaveContent.extraProps',
	'saint/gallery/applyExtraClass',
	applyExtraClass
);