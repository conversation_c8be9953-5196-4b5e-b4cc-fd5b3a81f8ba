<?php
/**
 * Newsletter sign up - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'signup-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-signup'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// --- get Atreemo SourceID origin
$origin = get_field('newsletter_atreemo_origin') ?: false;
$origin_html = $origin ? ' origin="'.$origin.'"' : '';
$ignore = get_field('newsletter_ignore') ?: false;
$ignore_html = $ignore ? ' ignore="'.$ignore.'"' : '';
$layout = get_field('newsletter_layout') ?: false;
$layout_html = $layout ? ' layout="'.$layout["value"].'"' : '';
$submit_label = get_field('newsletter_btn_label') ?: false;
$submit_label_html = $submit_label ? ' submit_label="'.$submit_label.'"' : '';

if( !is_admin() ):
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
    <?php echo do_shortcode( '[newsletter_form type="standalone"'.$origin_html.$ignore_html.$layout_html.$submit_label_html.']' )?>
</div><!-- .block-cta -->
<?php
else: ?>
    <!-- Backend placeholder -->
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Newsletter signup <br><small><?= $layout["label"] ?: 'Default' ?></small></h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>
<?php endif; ?>