"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
/**
 * WordPress Dependencies
 */
var __ = wp.i18n.__;
var addFilter = wp.hooks.addFilter;
var createHigherOrderComponent = wp.compose.createHigherOrderComponent;
var Fragment = wp.element.Fragment;
var InspectorControls = wp.blockEditor.InspectorControls;
var _wp$components = wp.components,
  PanelBody = _wp$components.PanelBody,
  ToggleControl = _wp$components.ToggleControl;

//restrict to specific block names
var allowedBlocks = ['core/gallery'];

/**
 * Add custom attribute for mobile visibility.
 *
 * @param {Object} settings Settings for the block.
 *
 * @return {Object} settings Modified settings.
 */
function addAttributes(settings) {
  //check if object exists for old Gutenberg version compatibility
  //add allowedBlocks restriction
  if (typeof settings.attributes !== 'undefined' && allowedBlocks.includes(settings.name)) {
    settings.attributes = Object.assign(settings.attributes, {
      addLightbox: {
        type: 'boolean',
        "default": false
      }
    });
  }
  return settings;
}

/**
 * Add mobile visibility controls on Advanced Block Panel.
 *
 * @param {function} BlockEdit Block edit component.
 *
 * @return {function} BlockEdit Modified block edit component.
 */
var withAdvancedControls = createHigherOrderComponent(function (BlockEdit) {
  return function (props) {
    // Check if the block is a gallery block
    if (!allowedBlocks.includes(props.name)) {
      return /*#__PURE__*/React.createElement(BlockEdit, props);
    }
    var name = props.name,
      attributes = props.attributes,
      setAttributes = props.setAttributes,
      isSelected = props.isSelected;
    var addLightbox = attributes.addLightbox;
    return /*#__PURE__*/React.createElement(Fragment, null, /*#__PURE__*/React.createElement(BlockEdit, props), isSelected && /*#__PURE__*/React.createElement(InspectorControls, null, /*#__PURE__*/React.createElement(PanelBody, {
      title: "Lightbox"
    }, /*#__PURE__*/React.createElement(ToggleControl, {
      label: "Enable for all images",
      checked: addLightbox,
      onChange: function onChange() {
        return setAttributes({
          addLightbox: !addLightbox
        });
      }
    }))));
  };
}, 'withAdvancedControls');

/**
 * Add custom element class in save element.
 *
 * @param {Object} extraProps     Block element.
 * @param {Object} blockType      Blocks object.
 * @param {Object} attributes     Blocks attributes.
 *
 * @return {Object} extraProps Modified block element.
 */
function applyExtraClass(extraProps, blockType, attributes) {
  var addLightbox = attributes.addLightbox;
  //check if attribute exists for old Gutenberg version compatibility
  //add class only when addLightbox = false
  //add allowedBlocks restriction
  if (typeof addLightbox !== 'undefined' && addLightbox && allowedBlocks.includes(blockType.name)) {
    console.log(blockType, extraProps, attributes);
    return _objectSpread(_objectSpread({}, extraProps), {}, {
      'data-lightbox': addLightbox
    });
  }
  return extraProps;
}

//add filters

addFilter('blocks.registerBlockType', 'saint/gallery/custom-attributes', addAttributes);
addFilter('editor.BlockEdit', 'saint/gallery/custom-advanced-control', withAdvancedControls);
addFilter('blocks.getSaveContent.extraProps', 'saint/gallery/applyExtraClass', applyExtraClass);