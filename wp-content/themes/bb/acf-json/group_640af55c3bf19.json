{"key": "group_640af55c3bf19", "title": "Content Block: CTA", "fields": [{"key": "field_640af55c3e751", "label": "Title", "name": "cta_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_6481863c78445", "label": "Add animation", "name": "cta_animation", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "30", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_648183fed1dec", "label": "Animation text", "name": "cta_anim_text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_6481863c78445", "operator": "==", "value": "1"}]], "wrapper": {"width": "70", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "A lovely statement goes here", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_640af55c3e7c0", "label": "Content", "name": "cta_content", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 0}, {"key": "field_640af55c3e7f6", "label": "CTA Buttons", "name": "cta_buttons", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "layout": "table", "pagination": 0, "min": 0, "max": 0, "collapsed": "", "button_label": "<PERSON>d <PERSON>", "rows_per_page": 20, "sub_fields": [{"key": "field_640af55c40891", "label": "Label", "name": "label", "aria-label": "", "type": "text", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "30", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": "", "parent_repeater": "field_640af55c3e7f6"}, {"key": "field_640af55c408c9", "label": "URL", "name": "url", "aria-label": "", "type": "url", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "70", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "parent_repeater": "field_640af55c3e7f6"}]}], "location": [[{"param": "block", "operator": "==", "value": "acf/cta"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "show_in_graphql": 0, "graphql_field_name": "contentBlock:FeaturedContentSideImage", "map_graphql_types_from_location_rules": 0, "graphql_types": "", "modified": 1744195905}