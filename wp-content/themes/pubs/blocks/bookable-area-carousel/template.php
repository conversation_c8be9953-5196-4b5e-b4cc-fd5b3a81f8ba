<?php
/**
 * Bookable Areas Carousel - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'carousel-area-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-area-carousel'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// --- Carousel options
$options = array(
    'autoplay' => false,
);

// --- Posts to display
$areas = get_posts(array(
    'post_type' => 'area',
    'posts_per_page' => -1,
    'orderby' => 'menu_order',
    'order' => 'ASC',
));

?>
<?php if ( ! $is_preview ) { ?>
<div class="bookable-areas-carousel">
    <div data-carousel="true"
        <?php
        echo wp_kses_data(
            get_block_wrapper_attributes(
                array(
                    'id'    => esc_attr($id),
                    'class' => esc_attr( $class_name ),
                )
            )
        );
        ?>
        data-options="<?php echo esc_attr( wp_json_encode( $options ) ); ?>"
    >
<?php } ?>

    <?php foreach( $areas as $area ):
        $area_id = $area->ID;
        $area_slug = $area->post_name;
        ?>
        <div data-carousel-slide="true" data-hash="<?= esc_attr($area_slug) ?>">
            <?php
            Saint\render_acf_block('acf/bookable-area', [
                'area' => $area_id
            ]);
            ?>
        </div>
    <?php endforeach; ?>

<?php if ( ! $is_preview ) { ?>
    </div><!-- .block-area-carousel -->
</div>
<?php } ?>