<?php
/**
 * Sitemap - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'sitemap-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-sitemap'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$sitemap = null;
// === Get Pages
$locations_pages = wp_list_pages( array(
    // 'depth'        => 0,
    'show_date'    => '',
    'title_li'     => '',
    'echo'         => 0,
    'sort_column'  => 'menu_order, post_title',
    'link_before'  => '',
    'link_after'   => '',
    'item_spacing' => 'preserve',
    'post_type'    => 'page',
    'post_status'  => 'publish'
) );

if( !is_admin() ):
    // var_dump($socials['opt_socials']);
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
    <div class="row">
        <div class="col-12 col-lg-10 offset-lg-1">
            <ul class="d-flex listing">
                <div class="row">
                    <?= str_replace( get_site_url().'/', HEADLESS_FRONTEND_URL, $locations_pages ); ?>
                </div>
            </ul>
        </div>
    </div>
</div><!-- .block-sitemap -->
<?php
else: ?>
    <!-- Backend placeholder -->
    <div class="alert alert-info text-center">
        <h4 class="alert-heading">Website Sitemap</h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>
<?php endif; ?>
