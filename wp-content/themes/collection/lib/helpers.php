<?php
/**
 * Reaplce elements in non-associative array
 */
function SaintMoveKeyBefore($arr, $find, $move) {
    if (!isset($arr[$find], $arr[$move])) {
        return $arr;
    }

    $elem = [$arr[$move]];  // cache the element to be moved
    unset($arr[$move]);
    $start = array_splice($arr, 0, $find);
    unset($start[$move]);  // only important if $move is in $start
    return array_merge($start, $elem, $arr);
}

/**
 * Get All Pubs sorted
 */
function get_all_pubs_sorted() {
    $sites = get_sites([
        'site__not_in' => [1,2,3]
    ]);

    // Order by ->blogname
    usort( $sites, function( $a, $b ) {
        return strcmp( $a->blogname, $b->blogname );
    });

    // --- Replace black Horse pubs positions so that Reigate is first
    $thame_key = $reigate_key = null;
    foreach( $sites as $key => $site ) {
        if( strstr($site->domain, 'blackhorsethame') !== false ) $thame_key = $key;
        if( strstr($site->domain, 'theblackhorsereigate') !== false ) $reigate_key = $key;
    }
    if( $thame_key && $reigate_key && $reigate_key > $thame_key ) {
        // var_dump($thame_key, $reigate_key);
        $sites = SaintMoveKeyBefore($sites, $thame_key, $reigate_key);
    }

    return $sites;
}

/**
 * Get All Pubs (published) sorted
 */
function get_published_pubs_sorted() {
    $sites = get_sites([
        'site__not_in' => [1,2,3],
        'public' => 1,
    ]);

    // Order by ->blogname
    usort( $sites, function( $a, $b ) {
        return strcmp( $a->blogname, $b->blogname );
    });

    // --- Replace black Horse pubs positions so that Reigate is first
    $thame_key = $reigate_key = null;
    foreach( $sites as $key => $site ) {
        if( strstr($site->domain, 'blackhorsethame') !== false ) $thame_key = $key;
        if( strstr($site->domain, 'theblackhorsereigate') !== false ) $reigate_key = $key;
    }
    if( $thame_key && $reigate_key && $reigate_key > $thame_key ) {
        // var_dump($thame_key, $reigate_key);
        $sites = SaintMoveKeyBefore($sites, $thame_key, $reigate_key);
    }

    return $sites;
}

/**
 * Get ACF option in network
 *
 * @param   int $blog
 * @param   string $key
 * @return  string
 */

 function multisite_acf_option( $blog, $key )
 {
     switch_to_blog( $blog );
     $meta_value = get_field($key,'option');
     restore_current_blog();
     return $meta_value;
 }