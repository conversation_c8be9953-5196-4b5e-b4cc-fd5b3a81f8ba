{"key": "group_6789385ea134a", "title": "Dining Area Settings", "fields": [{"key": "field_67894828ae600", "label": "Featured Images", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0, "selected": 0}, {"key": "field_6789385ff2218", "label": "Desktop image", "name": "image", "aria-label": "", "type": "image", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 1, "preview_size": "medium"}, {"key": "field_678938fd6fb2f", "label": "Mobile image (optional)", "name": "image_mobile", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 1, "preview_size": "medium"}, {"key": "field_6789480eae5ff", "label": "Area Details", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0, "selected": 0}, {"key": "field_67893bd74484e", "label": "Bookable days", "name": "bookable_days", "aria-label": "", "type": "checkbox", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "default_value": [], "return_format": "array", "allow_custom": 0, "allow_in_bindings": 1, "layout": "horizontal", "toggle": 1, "save_custom": 0, "custom_choice_button_text": "Add new choice"}, {"key": "field_67893d434aee4", "label": "Booking fee", "name": "booking_fee", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "allow_in_bindings": 1, "rows": 4, "placeholder": "", "new_lines": "br"}, {"key": "field_67893e574aee5", "label": "Type", "name": "type", "aria-label": "", "type": "select", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"private": "Private", "semi-private": "Semi Private", "not-private": "Not Private"}, "default_value": false, "return_format": "array", "multiple": 0, "allow_null": 0, "allow_in_bindings": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_67893f04bc9a9", "label": "Features", "name": "features", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"screen": "Screen Available", "mic": "Mic/AV Equipment Available", "dog": "Dog Friendly", "wheelchair": "Wheelchair Accessible"}, "default_value": [], "return_format": "array", "multiple": 1, "allow_null": 1, "allow_in_bindings": 0, "ui": 1, "ajax": 0, "placeholder": ""}, {"key": "field_678947501f279", "label": "Capacity", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0, "selected": 0}, {"key": "field_67893aadb958d", "label": "Seated", "name": "capacity_seated", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "layout": "block", "sub_fields": [{"key": "field_678939ea1f7e7", "label": "From", "name": "from", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "min": "", "max": "", "allow_in_bindings": 1, "placeholder": "", "step": 1, "prepend": "", "append": ""}, {"key": "field_67893ad1b958e", "label": "To", "name": "to", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "min": "", "max": "", "allow_in_bindings": 1, "placeholder": "", "step": 1, "prepend": "", "append": ""}]}, {"key": "field_67893b9ebcb0e", "label": "Standing", "name": "capacity_standing", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "layout": "block", "sub_fields": [{"key": "field_67893b9ebcb0f", "label": "From", "name": "from", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "min": "", "max": "", "allow_in_bindings": 1, "placeholder": "", "step": 1, "prepend": "", "append": ""}, {"key": "field_67893b9ebcb10", "label": "To", "name": "to", "aria-label": "", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "min": "", "max": "", "allow_in_bindings": 1, "placeholder": "", "step": 1, "prepend": "", "append": ""}]}, {"key": "field_678947c81f27d", "label": "Description", "name": "", "aria-label": "", "type": "tab", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "placement": "left", "endpoint": 0, "selected": 0}, {"key": "field_67893f8d8b3c2", "label": "Description", "name": "description", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "allow_in_bindings": 0, "tabs": "all", "toolbar": "full", "media_upload": 0, "delay": 0}], "location": [[{"param": "post_type", "operator": "==", "value": "area"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "show_in_graphql": 1, "graphql_field_name": "singleArea", "map_graphql_types_from_location_rules": 0, "graphql_types": "", "modified": 1744195923}