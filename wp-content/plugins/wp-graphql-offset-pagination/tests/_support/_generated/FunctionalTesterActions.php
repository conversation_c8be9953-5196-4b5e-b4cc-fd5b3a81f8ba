<?php  //[STAMP] 03887793658c19b6b2f443d5b7719a6c
namespace _generated;

// This class was automatically generated by build task
// You should not change it manually as it will be overwritten on next build
// @codingStandardsIgnoreFile

trait FunctionalTesterActions
{
    /**
     * @return \Codeception\Scenario
     */
    abstract protected function getScenario();

    
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sets the HTTP header to the passed value - which is used on
     * subsequent HTTP requests through PhpBrowser.
     *
     * Example:
     * ```php
     * <?php
     * $I->haveHttpHeader('X-Requested-With', 'Codeception');
     * $I->amOnPage('test-headers.php');
     * ?>
     * ```
     *
     * To use special chars in Header Key use HTML Character Entities:
     * Example:
     * Header with underscore - 'Client_Id'
     * should be represented as - 'Client&#x0005F;Id' or 'Client&#95;Id'
     *
     * ```php
     * <?php
     * $I->haveHttpHeader('Client&#95;Id', 'Codeception');
     * ?>
     * ```
     *
     * @param string $name the name of the request header
     * @param string $value the value to set it to for subsequent
     *        requests
     * @see \Codeception\Lib\InnerBrowser::haveHttpHeader()
     */
    public function haveHttpHeader($name, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveHttpHeader', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Deletes the header with the passed name.  Subsequent requests
     * will not have the deleted header in its request.
     *
     * Example:
     * ```php
     * <?php
     * $I->haveHttpHeader('X-Requested-With', 'Codeception');
     * $I->amOnPage('test-headers.php');
     * // ...
     * $I->deleteHeader('X-Requested-With');
     * $I->amOnPage('some-other-page.php');
     * ?>
     * ```
     *
     * @param string $name the name of the header to delete.
     * @see \Codeception\Lib\InnerBrowser::deleteHeader()
     */
    public function deleteHeader($name) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('deleteHeader', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks over the given HTTP header and (optionally)
     * its value, asserting that are there
     *
     * @param $name
     * @param $value
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::seeHttpHeader()
     */
    public function seeHttpHeader($name, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeHttpHeader', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks over the given HTTP header and (optionally)
     * its value, asserting that are there
     *
     * @param $name
     * @param $value
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::seeHttpHeader()
     */
    public function canSeeHttpHeader($name, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeHttpHeader', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks over the given HTTP header and (optionally)
     * its value, asserting that are not there
     *
     * @param $name
     * @param $value
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::dontSeeHttpHeader()
     */
    public function dontSeeHttpHeader($name, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeHttpHeader', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks over the given HTTP header and (optionally)
     * its value, asserting that are not there
     *
     * @param $name
     * @param $value
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::dontSeeHttpHeader()
     */
    public function cantSeeHttpHeader($name, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeHttpHeader', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that http response header is received only once.
     * HTTP RFC2616 allows multiple response headers with the same name.
     * You can check that you didn't accidentally sent the same header twice.
     *
     * ``` php
     * <?php
     * $I->seeHttpHeaderOnce('Cache-Control');
     * ?>>
     * ```
     *
     * @param $name
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::seeHttpHeaderOnce()
     */
    public function seeHttpHeaderOnce($name) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeHttpHeaderOnce', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that http response header is received only once.
     * HTTP RFC2616 allows multiple response headers with the same name.
     * You can check that you didn't accidentally sent the same header twice.
     *
     * ``` php
     * <?php
     * $I->seeHttpHeaderOnce('Cache-Control');
     * ?>>
     * ```
     *
     * @param $name
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::seeHttpHeaderOnce()
     */
    public function canSeeHttpHeaderOnce($name) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeHttpHeaderOnce', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the value of the specified header name
     *
     * @param $name
     * @param Boolean $first Whether to return the first value or all header values
     *
     * @return string|array The first header value if $first is true, an array of values otherwise
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::grabHttpHeader()
     */
    public function grabHttpHeader($name, $first = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabHttpHeader', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Authenticates user for HTTP_AUTH
     *
     * @param $username
     * @param $password
     * @see \Codeception\Module\PhpBrowser::amHttpAuthenticated()
     */
    public function amHttpAuthenticated($username, $password) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amHttpAuthenticated', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Adds Digest authentication via username/password.
     *
     * @param $username
     * @param $password
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::amDigestAuthenticated()
     */
    public function amDigestAuthenticated($username, $password) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amDigestAuthenticated', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Adds Bearer authentication via access token.
     *
     * @param $accessToken
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::amBearerAuthenticated()
     */
    public function amBearerAuthenticated($accessToken) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amBearerAuthenticated', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Adds NTLM authentication via username/password.
     * Requires client to be Guzzle >=6.3.0
     * Out of scope for functional modules.
     *
     * Example:
     * ```php
     * <?php
     * $I->amNTLMAuthenticated('jon_snow', 'targaryen');
     * ?>
     * ```
     *
     * @param $username
     * @param $password
     * @throws ModuleException
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::amNTLMAuthenticated()
     */
    public function amNTLMAuthenticated($username, $password) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amNTLMAuthenticated', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Allows to send REST request using AWS Authorization
     *
     * Only works with PhpBrowser
     * Example Config:
     * ```yml
     * modules:
     *      enabled:
     *          - REST:
     *              aws:
     *                  key: accessKey
     *                  secret: accessSecret
     *                  service: awsService
     *                  region: awsRegion
     * ```
     * Code:
     * ```php
     * <?php
     * $I->amAWSAuthenticated();
     * ?>
     * ```
     * @param array $additionalAWSConfig
     * @throws ModuleException
     * @see \Codeception\Module\REST::amAWSAuthenticated()
     */
    public function amAWSAuthenticated($additionalAWSConfig = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amAWSAuthenticated', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends a POST request to given uri. Parameters and files can be provided separately.
     *
     * Example:
     * ```php
     * <?php
     * //simple POST call
     * $I->sendPOST('/message', ['subject' => 'Read this!', 'to' => '<EMAIL>']);
     * //simple upload method
     * $I->sendPOST('/message/24', ['inline' => 0], ['attachmentFile' => codecept_data_dir('sample_file.pdf')]);
     * //uploading a file with a custom name and mime-type. This is also useful to simulate upload errors.
     * $I->sendPOST('/message/24', ['inline' => 0], [
     *     'attachmentFile' => [
     *          'name' => 'document.pdf',
     *          'type' => 'application/pdf',
     *          'error' => UPLOAD_ERR_OK,
     *          'size' => filesize(codecept_data_dir('sample_file.pdf')),
     *          'tmp_name' => codecept_data_dir('sample_file.pdf')
     *     ]
     * ]);
     * ```
     *
     * @param $url
     * @param array|\JsonSerializable $params
     * @param array $files A list of filenames or "mocks" of $_FILES (each entry being an array with the following
     *                     keys: name, type, error, size, tmp_name (pointing to the real file path). Each key works
     *                     as the "name" attribute of a file input field.
     *
     * @see http://php.net/manual/en/features.file-upload.post-method.php
     * @see codecept_data_dir()
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendPOST()
     */
    public function sendPOST($url, $params = null, $files = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendPOST', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends a HEAD request to given uri.
     *
     * @param $url
     * @param array $params
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendHEAD()
     */
    public function sendHEAD($url, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendHEAD', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends an OPTIONS request to given uri.
     *
     * @param $url
     * @param array $params
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendOPTIONS()
     */
    public function sendOPTIONS($url, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendOPTIONS', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends a GET request to given uri.
     *
     * @param $url
     * @param array $params
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendGET()
     */
    public function sendGET($url, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendGET', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends PUT request to given uri.
     *
     * @param $url
     * @param array $params
     * @param array $files
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendPUT()
     */
    public function sendPUT($url, $params = null, $files = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendPUT', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends PATCH request to given uri.
     *
     * @param       $url
     * @param array $params
     * @param array $files
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendPATCH()
     */
    public function sendPATCH($url, $params = null, $files = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendPATCH', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends DELETE request to given uri.
     *
     * @param $url
     * @param array $params
     * @param array $files
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendDELETE()
     */
    public function sendDELETE($url, $params = null, $files = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendDELETE', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends LINK request to given uri.
     *
     * @param       $url
     * @param array $linkEntries (entry is array with keys "uri" and "link-param")
     *
     * @link http://tools.ietf.org/html/rfc2068#section-19.6.2.4
     *
     * <AUTHOR>
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendLINK()
     */
    public function sendLINK($url, $linkEntries) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendLINK', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sends UNLINK request to given uri.
     *
     * @param       $url
     * @param array $linkEntries (entry is array with keys "uri" and "link-param")
     * @link http://tools.ietf.org/html/rfc2068#section-19.6.2.4
     * <AUTHOR>
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::sendUNLINK()
     */
    public function sendUNLINK($url, $linkEntries) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendUNLINK', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks whether last response was valid JSON.
     * This is done with json_last_error function.
     *
     * @part json
     * @see \Codeception\Module\REST::seeResponseIsJson()
     */
    public function seeResponseIsJson() {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseIsJson', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks whether last response was valid JSON.
     * This is done with json_last_error function.
     *
     * @part json
     * @see \Codeception\Module\REST::seeResponseIsJson()
     */
    public function canSeeResponseIsJson() {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseIsJson', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks whether the last response contains text.
     *
     * @param $text
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::seeResponseContains()
     */
    public function seeResponseContains($text) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseContains', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks whether the last response contains text.
     *
     * @param $text
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::seeResponseContains()
     */
    public function canSeeResponseContains($text) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseContains', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks whether last response do not contain text.
     *
     * @param $text
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::dontSeeResponseContains()
     */
    public function dontSeeResponseContains($text) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeResponseContains', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks whether last response do not contain text.
     *
     * @param $text
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::dontSeeResponseContains()
     */
    public function cantSeeResponseContains($text) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeResponseContains', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks whether the last JSON response contains provided array.
     * The response is converted to array with json_decode($response, true)
     * Thus, JSON is represented by associative array.
     * This method matches that response array contains provided array.
     *
     * Examples:
     *
     * ``` php
     * <?php
     * // response: {name: john, email: <EMAIL>}
     * $I->seeResponseContainsJson(array('name' => 'john'));
     *
     * // response {user: john, profile: { email: <EMAIL> }}
     * $I->seeResponseContainsJson(array('email' => '<EMAIL>'));
     *
     * ?>
     * ```
     *
     * This method recursively checks if one array can be found inside of another.
     *
     * @param array $json
     * @part json
     * @see \Codeception\Module\REST::seeResponseContainsJson()
     */
    public function seeResponseContainsJson($json = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseContainsJson', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks whether the last JSON response contains provided array.
     * The response is converted to array with json_decode($response, true)
     * Thus, JSON is represented by associative array.
     * This method matches that response array contains provided array.
     *
     * Examples:
     *
     * ``` php
     * <?php
     * // response: {name: john, email: <EMAIL>}
     * $I->seeResponseContainsJson(array('name' => 'john'));
     *
     * // response {user: john, profile: { email: <EMAIL> }}
     * $I->seeResponseContainsJson(array('email' => '<EMAIL>'));
     *
     * ?>
     * ```
     *
     * This method recursively checks if one array can be found inside of another.
     *
     * @param array $json
     * @part json
     * @see \Codeception\Module\REST::seeResponseContainsJson()
     */
    public function canSeeResponseContainsJson($json = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseContainsJson', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns current response so that it can be used in next scenario steps.
     *
     * Example:
     *
     * ``` php
     * <?php
     * $user_id = $I->grabResponse();
     * $I->sendPUT('/user', array('id' => $user_id, 'name' => 'davert'));
     * ?>
     * ```
     *
     * @return string
     * @part json
     * @part xml
     * @version 1.1
     * @see \Codeception\Module\REST::grabResponse()
     */
    public function grabResponse() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabResponse', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns data from the current JSON response using [JSONPath](http://goessner.net/articles/JsonPath/) as selector.
     * JsonPath is XPath equivalent for querying Json structures.
     * Try your JsonPath expressions [online](http://jsonpath.curiousconcept.com/).
     * Even for a single value an array is returned.
     *
     * This method **require [`flow/jsonpath` > 0.2](https://github.com/FlowCommunications/JSONPath/) library to be installed**.
     *
     * Example:
     *
     * ``` php
     * <?php
     * // match the first `user.id` in json
     * $firstUserId = $I->grabDataFromResponseByJsonPath('$..users[0].id');
     * $I->sendPUT('/user', array('id' => $firstUserId[0], 'name' => 'davert'));
     * ?>
     * ```
     *
     * @param string $jsonPath
     * @return array Array of matching items
     * @throws \Exception
     * @part json
     * @version 2.0.9
     * @see \Codeception\Module\REST::grabDataFromResponseByJsonPath()
     */
    public function grabDataFromResponseByJsonPath($jsonPath) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabDataFromResponseByJsonPath', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if json structure in response matches the xpath provided.
     * JSON is not supposed to be checked against XPath, yet it can be converted to xml and used with XPath.
     * This assertion allows you to check the structure of response json.
     *     *
     * ```json
     *   { "store": {
     *       "book": [
     *         { "category": "reference",
     *           "author": "Nigel Rees",
     *           "title": "Sayings of the Century",
     *           "price": 8.95
     *         },
     *         { "category": "fiction",
     *           "author": "Evelyn Waugh",
     *           "title": "Sword of Honour",
     *           "price": 12.99
     *         }
     *    ],
     *       "bicycle": {
     *         "color": "red",
     *         "price": 19.95
     *       }
     *     }
     *   }
     * ```
     *
     * ```php
     * <?php
     * // at least one book in store has author
     * $I->seeResponseJsonMatchesXpath('//store/book/author');
     * // first book in store has author
     * $I->seeResponseJsonMatchesXpath('//store/book[1]/author');
     * // at least one item in store has price
     * $I->seeResponseJsonMatchesXpath('/store//price');
     * ?>
     * ```
     * @param string $xpath
     * @part json
     * @version 2.0.9
     * @see \Codeception\Module\REST::seeResponseJsonMatchesXpath()
     */
    public function seeResponseJsonMatchesXpath($xpath) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseJsonMatchesXpath', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks if json structure in response matches the xpath provided.
     * JSON is not supposed to be checked against XPath, yet it can be converted to xml and used with XPath.
     * This assertion allows you to check the structure of response json.
     *     *
     * ```json
     *   { "store": {
     *       "book": [
     *         { "category": "reference",
     *           "author": "Nigel Rees",
     *           "title": "Sayings of the Century",
     *           "price": 8.95
     *         },
     *         { "category": "fiction",
     *           "author": "Evelyn Waugh",
     *           "title": "Sword of Honour",
     *           "price": 12.99
     *         }
     *    ],
     *       "bicycle": {
     *         "color": "red",
     *         "price": 19.95
     *       }
     *     }
     *   }
     * ```
     *
     * ```php
     * <?php
     * // at least one book in store has author
     * $I->seeResponseJsonMatchesXpath('//store/book/author');
     * // first book in store has author
     * $I->seeResponseJsonMatchesXpath('//store/book[1]/author');
     * // at least one item in store has price
     * $I->seeResponseJsonMatchesXpath('/store//price');
     * ?>
     * ```
     * @param string $xpath
     * @part json
     * @version 2.0.9
     * @see \Codeception\Module\REST::seeResponseJsonMatchesXpath()
     */
    public function canSeeResponseJsonMatchesXpath($xpath) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseJsonMatchesXpath', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Opposite to seeResponseJsonMatchesXpath
     *
     * @param string $xpath
     * @part json
     * @see \Codeception\Module\REST::dontSeeResponseJsonMatchesXpath()
     */
    public function dontSeeResponseJsonMatchesXpath($xpath) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeResponseJsonMatchesXpath', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Opposite to seeResponseJsonMatchesXpath
     *
     * @param string $xpath
     * @part json
     * @see \Codeception\Module\REST::dontSeeResponseJsonMatchesXpath()
     */
    public function cantSeeResponseJsonMatchesXpath($xpath) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeResponseJsonMatchesXpath', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if json structure in response matches [JsonPath](http://goessner.net/articles/JsonPath/).
     * JsonPath is XPath equivalent for querying Json structures.
     * Try your JsonPath expressions [online](http://jsonpath.curiousconcept.com/).
     * This assertion allows you to check the structure of response json.
     *
     * This method **require [`flow/jsonpath` > 0.2](https://github.com/FlowCommunications/JSONPath/) library to be installed**.
     *
     * ```json
     *   { "store": {
     *       "book": [
     *         { "category": "reference",
     *           "author": "Nigel Rees",
     *           "title": "Sayings of the Century",
     *           "price": 8.95
     *         },
     *         { "category": "fiction",
     *           "author": "Evelyn Waugh",
     *           "title": "Sword of Honour",
     *           "price": 12.99
     *         }
     *    ],
     *       "bicycle": {
     *         "color": "red",
     *         "price": 19.95
     *       }
     *     }
     *   }
     * ```
     *
     * ```php
     * <?php
     * // at least one book in store has author
     * $I->seeResponseJsonMatchesJsonPath('$.store.book[*].author');
     * // first book in store has author
     * $I->seeResponseJsonMatchesJsonPath('$.store.book[0].author');
     * // at least one item in store has price
     * $I->seeResponseJsonMatchesJsonPath('$.store..price');
     * ?>
     * ```
     *
     * @param string $jsonPath
     * @part json
     * @version 2.0.9
     * @see \Codeception\Module\REST::seeResponseJsonMatchesJsonPath()
     */
    public function seeResponseJsonMatchesJsonPath($jsonPath) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseJsonMatchesJsonPath', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks if json structure in response matches [JsonPath](http://goessner.net/articles/JsonPath/).
     * JsonPath is XPath equivalent for querying Json structures.
     * Try your JsonPath expressions [online](http://jsonpath.curiousconcept.com/).
     * This assertion allows you to check the structure of response json.
     *
     * This method **require [`flow/jsonpath` > 0.2](https://github.com/FlowCommunications/JSONPath/) library to be installed**.
     *
     * ```json
     *   { "store": {
     *       "book": [
     *         { "category": "reference",
     *           "author": "Nigel Rees",
     *           "title": "Sayings of the Century",
     *           "price": 8.95
     *         },
     *         { "category": "fiction",
     *           "author": "Evelyn Waugh",
     *           "title": "Sword of Honour",
     *           "price": 12.99
     *         }
     *    ],
     *       "bicycle": {
     *         "color": "red",
     *         "price": 19.95
     *       }
     *     }
     *   }
     * ```
     *
     * ```php
     * <?php
     * // at least one book in store has author
     * $I->seeResponseJsonMatchesJsonPath('$.store.book[*].author');
     * // first book in store has author
     * $I->seeResponseJsonMatchesJsonPath('$.store.book[0].author');
     * // at least one item in store has price
     * $I->seeResponseJsonMatchesJsonPath('$.store..price');
     * ?>
     * ```
     *
     * @param string $jsonPath
     * @part json
     * @version 2.0.9
     * @see \Codeception\Module\REST::seeResponseJsonMatchesJsonPath()
     */
    public function canSeeResponseJsonMatchesJsonPath($jsonPath) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseJsonMatchesJsonPath', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Opposite to seeResponseJsonMatchesJsonPath
     *
     * @param string $jsonPath
     * @part json
     * @see \Codeception\Module\REST::dontSeeResponseJsonMatchesJsonPath()
     */
    public function dontSeeResponseJsonMatchesJsonPath($jsonPath) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeResponseJsonMatchesJsonPath', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Opposite to seeResponseJsonMatchesJsonPath
     *
     * @param string $jsonPath
     * @part json
     * @see \Codeception\Module\REST::dontSeeResponseJsonMatchesJsonPath()
     */
    public function cantSeeResponseJsonMatchesJsonPath($jsonPath) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeResponseJsonMatchesJsonPath', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Opposite to seeResponseContainsJson
     *
     * @part json
     * @param array $json
     * @see \Codeception\Module\REST::dontSeeResponseContainsJson()
     */
    public function dontSeeResponseContainsJson($json = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeResponseContainsJson', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Opposite to seeResponseContainsJson
     *
     * @part json
     * @param array $json
     * @see \Codeception\Module\REST::dontSeeResponseContainsJson()
     */
    public function cantSeeResponseContainsJson($json = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeResponseContainsJson', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that Json matches provided types.
     * In case you don't know the actual values of JSON data returned you can match them by type.
     * Starts check with a root element. If JSON data is array it will check the first element of an array.
     * You can specify the path in the json which should be checked with JsonPath
     *
     * Basic example:
     *
     * ```php
     * <?php
     * // {'user_id': 1, 'name': 'davert', 'is_active': false}
     * $I->seeResponseMatchesJsonType([
     *      'user_id' => 'integer',
     *      'name' => 'string|null',
     *      'is_active' => 'boolean'
     * ]);
     *
     * // narrow down matching with JsonPath:
     * // {"users": [{ "name": "davert"}, {"id": 1}]}
     * $I->seeResponseMatchesJsonType(['name' => 'string'], '$.users[0]');
     * ?>
     * ```
     *
     * In this case you can match that record contains fields with data types you expected.
     * The list of possible data types:
     *
     * * string
     * * integer
     * * float
     * * array (json object is array as well)
     * * boolean
     *
     * You can also use nested data type structures:
     *
     * ```php
     * <?php
     * // {'user_id': 1, 'name': 'davert', 'company': {'name': 'Codegyre'}}
     * $I->seeResponseMatchesJsonType([
     *      'user_id' => 'integer|string', // multiple types
     *      'company' => ['name' => 'string']
     * ]);
     * ?>
     * ```
     *
     * You can also apply filters to check values. Filter can be applied with `:` char after the type declaration.
     *
     * Here is the list of possible filters:
     *
     * * `integer:>{val}` - checks that integer is greater than {val} (works with float and string types too).
     * * `integer:<{val}` - checks that integer is lower than {val} (works with float and string types too).
     * * `string:url` - checks that value is valid url.
     * * `string:date` - checks that value is date in JavaScript format: https://weblog.west-wind.com/posts/2014/Jan/06/JavaScript-JSON-Date-Parsing-and-real-Dates
     * * `string:email` - checks that value is a valid email according to http://emailregex.com/
     * * `string:regex({val})` - checks that string matches a regex provided with {val}
     *
     * This is how filters can be used:
     *
     * ```php
     * <?php
     * // {'user_id': 1, 'email' => '<EMAIL>'}
     * $I->seeResponseMatchesJsonType([
     *      'user_id' => 'string:>0:<1000', // multiple filters can be used
     *      'email' => 'string:regex(~\@~)' // we just check that @ char is included
     * ]);
     *
     * // {'user_id': '1'}
     * $I->seeResponseMatchesJsonType([
     *      'user_id' => 'string:>0', // works with strings as well
     * }
     * ?>
     * ```
     *
     * You can also add custom filters y accessing `JsonType::addCustomFilter` method.
     * See [JsonType reference](http://codeception.com/docs/reference/JsonType).
     *
     * @part json
     * @param array $jsonType
     * @param string $jsonPath
     * @version 2.1.3
     * @see \Codeception\Module\REST::seeResponseMatchesJsonType()
     */
    public function seeResponseMatchesJsonType($jsonType, $jsonPath = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseMatchesJsonType', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that Json matches provided types.
     * In case you don't know the actual values of JSON data returned you can match them by type.
     * Starts check with a root element. If JSON data is array it will check the first element of an array.
     * You can specify the path in the json which should be checked with JsonPath
     *
     * Basic example:
     *
     * ```php
     * <?php
     * // {'user_id': 1, 'name': 'davert', 'is_active': false}
     * $I->seeResponseMatchesJsonType([
     *      'user_id' => 'integer',
     *      'name' => 'string|null',
     *      'is_active' => 'boolean'
     * ]);
     *
     * // narrow down matching with JsonPath:
     * // {"users": [{ "name": "davert"}, {"id": 1}]}
     * $I->seeResponseMatchesJsonType(['name' => 'string'], '$.users[0]');
     * ?>
     * ```
     *
     * In this case you can match that record contains fields with data types you expected.
     * The list of possible data types:
     *
     * * string
     * * integer
     * * float
     * * array (json object is array as well)
     * * boolean
     *
     * You can also use nested data type structures:
     *
     * ```php
     * <?php
     * // {'user_id': 1, 'name': 'davert', 'company': {'name': 'Codegyre'}}
     * $I->seeResponseMatchesJsonType([
     *      'user_id' => 'integer|string', // multiple types
     *      'company' => ['name' => 'string']
     * ]);
     * ?>
     * ```
     *
     * You can also apply filters to check values. Filter can be applied with `:` char after the type declaration.
     *
     * Here is the list of possible filters:
     *
     * * `integer:>{val}` - checks that integer is greater than {val} (works with float and string types too).
     * * `integer:<{val}` - checks that integer is lower than {val} (works with float and string types too).
     * * `string:url` - checks that value is valid url.
     * * `string:date` - checks that value is date in JavaScript format: https://weblog.west-wind.com/posts/2014/Jan/06/JavaScript-JSON-Date-Parsing-and-real-Dates
     * * `string:email` - checks that value is a valid email according to http://emailregex.com/
     * * `string:regex({val})` - checks that string matches a regex provided with {val}
     *
     * This is how filters can be used:
     *
     * ```php
     * <?php
     * // {'user_id': 1, 'email' => '<EMAIL>'}
     * $I->seeResponseMatchesJsonType([
     *      'user_id' => 'string:>0:<1000', // multiple filters can be used
     *      'email' => 'string:regex(~\@~)' // we just check that @ char is included
     * ]);
     *
     * // {'user_id': '1'}
     * $I->seeResponseMatchesJsonType([
     *      'user_id' => 'string:>0', // works with strings as well
     * }
     * ?>
     * ```
     *
     * You can also add custom filters y accessing `JsonType::addCustomFilter` method.
     * See [JsonType reference](http://codeception.com/docs/reference/JsonType).
     *
     * @part json
     * @param array $jsonType
     * @param string $jsonPath
     * @version 2.1.3
     * @see \Codeception\Module\REST::seeResponseMatchesJsonType()
     */
    public function canSeeResponseMatchesJsonType($jsonType, $jsonPath = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseMatchesJsonType', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Opposite to `seeResponseMatchesJsonType`.
     *
     * @part json
     * @param $jsonType jsonType structure
     * @param null $jsonPath optionally set specific path to structure with JsonPath
     * @see seeResponseMatchesJsonType
     * @version 2.1.3
     * @see \Codeception\Module\REST::dontSeeResponseMatchesJsonType()
     */
    public function dontSeeResponseMatchesJsonType($jsonType, $jsonPath = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeResponseMatchesJsonType', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Opposite to `seeResponseMatchesJsonType`.
     *
     * @part json
     * @param $jsonType jsonType structure
     * @param null $jsonPath optionally set specific path to structure with JsonPath
     * @see seeResponseMatchesJsonType
     * @version 2.1.3
     * @see \Codeception\Module\REST::dontSeeResponseMatchesJsonType()
     */
    public function cantSeeResponseMatchesJsonType($jsonType, $jsonPath = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeResponseMatchesJsonType', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if response is exactly the same as provided.
     *
     * @part json
     * @part xml
     * @param $response
     * @see \Codeception\Module\REST::seeResponseEquals()
     */
    public function seeResponseEquals($expected) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseEquals', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks if response is exactly the same as provided.
     *
     * @part json
     * @part xml
     * @param $response
     * @see \Codeception\Module\REST::seeResponseEquals()
     */
    public function canSeeResponseEquals($expected) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that response code is equal to value provided.
     *
     * ```php
     * <?php
     * $I->seeResponseCodeIs(200);
     *
     * // recommended \Codeception\Util\HttpCode
     * $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
     * ```
     *
     * @param $code
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIs()
     */
    public function seeResponseCodeIs($code) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseCodeIs', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that response code is equal to value provided.
     *
     * ```php
     * <?php
     * $I->seeResponseCodeIs(200);
     *
     * // recommended \Codeception\Util\HttpCode
     * $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
     * ```
     *
     * @param $code
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIs()
     */
    public function canSeeResponseCodeIs($code) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseCodeIs', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that response code is equal to value provided.
     *
     * ```php
     * <?php
     * $I->dontSeeResponseCodeIs(200);
     *
     * // recommended \Codeception\Util\HttpCode
     * $I->dontSeeResponseCodeIs(\Codeception\Util\HttpCode::OK);
     * ```
     * @param $code
     * @see \Codeception\Lib\InnerBrowser::dontSeeResponseCodeIs()
     */
    public function dontSeeResponseCodeIs($code) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeResponseCodeIs', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that response code is equal to value provided.
     *
     * ```php
     * <?php
     * $I->dontSeeResponseCodeIs(200);
     *
     * // recommended \Codeception\Util\HttpCode
     * $I->dontSeeResponseCodeIs(\Codeception\Util\HttpCode::OK);
     * ```
     * @param $code
     * @see \Codeception\Lib\InnerBrowser::dontSeeResponseCodeIs()
     */
    public function cantSeeResponseCodeIs($code) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeResponseCodeIs', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the response code 2xx
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsSuccessful()
     */
    public function seeResponseCodeIsSuccessful() {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseCodeIsSuccessful', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the response code 2xx
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsSuccessful()
     */
    public function canSeeResponseCodeIsSuccessful() {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseCodeIsSuccessful', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the response code 3xx
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsRedirection()
     */
    public function seeResponseCodeIsRedirection() {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseCodeIsRedirection', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the response code 3xx
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsRedirection()
     */
    public function canSeeResponseCodeIsRedirection() {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseCodeIsRedirection', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the response code is 4xx
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsClientError()
     */
    public function seeResponseCodeIsClientError() {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseCodeIsClientError', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the response code is 4xx
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsClientError()
     */
    public function canSeeResponseCodeIsClientError() {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseCodeIsClientError', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the response code is 5xx
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsServerError()
     */
    public function seeResponseCodeIsServerError() {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseCodeIsServerError', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the response code is 5xx
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsServerError()
     */
    public function canSeeResponseCodeIsServerError() {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseCodeIsServerError', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks whether last response was valid XML.
     * This is done with libxml_get_last_error function.
     *
     * @part xml
     * @see \Codeception\Module\REST::seeResponseIsXml()
     */
    public function seeResponseIsXml() {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseIsXml', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks whether last response was valid XML.
     * This is done with libxml_get_last_error function.
     *
     * @part xml
     * @see \Codeception\Module\REST::seeResponseIsXml()
     */
    public function canSeeResponseIsXml() {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseIsXml', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks whether XML response matches XPath
     *
     * ```php
     * <?php
     * $I->seeXmlResponseMatchesXpath('//root/user[@id=1]');
     * ```
     * @part xml
     * @param $xpath
     * @see \Codeception\Module\REST::seeXmlResponseMatchesXpath()
     */
    public function seeXmlResponseMatchesXpath($xpath) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeXmlResponseMatchesXpath', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks whether XML response matches XPath
     *
     * ```php
     * <?php
     * $I->seeXmlResponseMatchesXpath('//root/user[@id=1]');
     * ```
     * @part xml
     * @param $xpath
     * @see \Codeception\Module\REST::seeXmlResponseMatchesXpath()
     */
    public function canSeeXmlResponseMatchesXpath($xpath) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeXmlResponseMatchesXpath', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks whether XML response does not match XPath
     *
     * ```php
     * <?php
     * $I->dontSeeXmlResponseMatchesXpath('//root/user[@id=1]');
     * ```
     * @part xml
     * @param $xpath
     * @see \Codeception\Module\REST::dontSeeXmlResponseMatchesXpath()
     */
    public function dontSeeXmlResponseMatchesXpath($xpath) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeXmlResponseMatchesXpath', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks whether XML response does not match XPath
     *
     * ```php
     * <?php
     * $I->dontSeeXmlResponseMatchesXpath('//root/user[@id=1]');
     * ```
     * @part xml
     * @param $xpath
     * @see \Codeception\Module\REST::dontSeeXmlResponseMatchesXpath()
     */
    public function cantSeeXmlResponseMatchesXpath($xpath) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeXmlResponseMatchesXpath', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Finds and returns text contents of element.
     * Element is matched by either CSS or XPath
     *
     * @param $cssOrXPath
     * @return string
     * @part xml
     * @see \Codeception\Module\REST::grabTextContentFromXmlElement()
     */
    public function grabTextContentFromXmlElement($cssOrXPath) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTextContentFromXmlElement', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Finds and returns attribute of element.
     * Element is matched by either CSS or XPath
     *
     * @param $cssOrXPath
     * @param $attribute
     * @return string
     * @part xml
     * @see \Codeception\Module\REST::grabAttributeFromXmlElement()
     */
    public function grabAttributeFromXmlElement($cssOrXPath, $attribute) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabAttributeFromXmlElement', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks XML response equals provided XML.
     * Comparison is done by canonicalizing both xml`s.
     *
     * Parameters can be passed either as DOMDocument, DOMNode, XML string, or array (if no attributes).
     *
     * @param $xml
     * @part xml
     * @see \Codeception\Module\REST::seeXmlResponseEquals()
     */
    public function seeXmlResponseEquals($xml) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeXmlResponseEquals', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks XML response equals provided XML.
     * Comparison is done by canonicalizing both xml`s.
     *
     * Parameters can be passed either as DOMDocument, DOMNode, XML string, or array (if no attributes).
     *
     * @param $xml
     * @part xml
     * @see \Codeception\Module\REST::seeXmlResponseEquals()
     */
    public function canSeeXmlResponseEquals($xml) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeXmlResponseEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks XML response does not equal to provided XML.
     * Comparison is done by canonicalizing both xml`s.
     *
     * Parameter can be passed either as XmlBuilder, DOMDocument, DOMNode, XML string, or array (if no attributes).
     *
     * @param $xml
     * @part xml
     * @see \Codeception\Module\REST::dontSeeXmlResponseEquals()
     */
    public function dontSeeXmlResponseEquals($xml) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeXmlResponseEquals', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks XML response does not equal to provided XML.
     * Comparison is done by canonicalizing both xml`s.
     *
     * Parameter can be passed either as XmlBuilder, DOMDocument, DOMNode, XML string, or array (if no attributes).
     *
     * @param $xml
     * @part xml
     * @see \Codeception\Module\REST::dontSeeXmlResponseEquals()
     */
    public function cantSeeXmlResponseEquals($xml) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeXmlResponseEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks XML response includes provided XML.
     * Comparison is done by canonicalizing both xml`s.
     * Parameter can be passed either as XmlBuilder, DOMDocument, DOMNode, XML string, or array (if no attributes).
     *
     * Example:
     *
     * ``` php
     * <?php
     * $I->seeXmlResponseIncludes("<result>1</result>");
     * ?>
     * ```
     *
     * @param $xml
     * @part xml
     * @see \Codeception\Module\REST::seeXmlResponseIncludes()
     */
    public function seeXmlResponseIncludes($xml) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeXmlResponseIncludes', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks XML response includes provided XML.
     * Comparison is done by canonicalizing both xml`s.
     * Parameter can be passed either as XmlBuilder, DOMDocument, DOMNode, XML string, or array (if no attributes).
     *
     * Example:
     *
     * ``` php
     * <?php
     * $I->seeXmlResponseIncludes("<result>1</result>");
     * ?>
     * ```
     *
     * @param $xml
     * @part xml
     * @see \Codeception\Module\REST::seeXmlResponseIncludes()
     */
    public function canSeeXmlResponseIncludes($xml) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeXmlResponseIncludes', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks XML response does not include provided XML.
     * Comparison is done by canonicalizing both xml`s.
     * Parameter can be passed either as XmlBuilder, DOMDocument, DOMNode, XML string, or array (if no attributes).
     *
     * @param $xml
     * @part xml
     * @see \Codeception\Module\REST::dontSeeXmlResponseIncludes()
     */
    public function dontSeeXmlResponseIncludes($xml) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeXmlResponseIncludes', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks XML response does not include provided XML.
     * Comparison is done by canonicalizing both xml`s.
     * Parameter can be passed either as XmlBuilder, DOMDocument, DOMNode, XML string, or array (if no attributes).
     *
     * @param $xml
     * @part xml
     * @see \Codeception\Module\REST::dontSeeXmlResponseIncludes()
     */
    public function cantSeeXmlResponseIncludes($xml) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeXmlResponseIncludes', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if the hash of a binary response is exactly the same as provided.
     * Parameter can be passed as any hash string supported by hash(), with an
     * optional second parameter to specify the hash type, which defaults to md5.
     *
     * Example: Using md5 hash key
     *
     * ```php
     * <?php
     * $I->seeBinaryResponseEquals("8c90748342f19b195b9c6b4eff742ded");
     * ?>
     * ```
     *
     * Example: Using md5 for a file contents
     *
     * ```php
     * <?php
     * $fileData = file_get_contents("test_file.jpg");
     * $I->seeBinaryResponseEquals(md5($fileData));
     * ?>
     * ```
     * Example: Using sha256 hash
     *
     * ```php
     * <?php
     * $fileData = '/9j/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/yQALCAABAAEBAREA/8wABgAQEAX/2gAIAQEAAD8A0s8g/9k='; // very small jpeg
     * $I->seeBinaryResponseEquals(hash("sha256", base64_decode($fileData)), 'sha256');
     * ?>
     * ```
     *
     * @param $hash the hashed data response expected
     * @param $algo the hash algorithm to use. Default md5.
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::seeBinaryResponseEquals()
     */
    public function seeBinaryResponseEquals($hash, $algo = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeBinaryResponseEquals', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks if the hash of a binary response is exactly the same as provided.
     * Parameter can be passed as any hash string supported by hash(), with an
     * optional second parameter to specify the hash type, which defaults to md5.
     *
     * Example: Using md5 hash key
     *
     * ```php
     * <?php
     * $I->seeBinaryResponseEquals("8c90748342f19b195b9c6b4eff742ded");
     * ?>
     * ```
     *
     * Example: Using md5 for a file contents
     *
     * ```php
     * <?php
     * $fileData = file_get_contents("test_file.jpg");
     * $I->seeBinaryResponseEquals(md5($fileData));
     * ?>
     * ```
     * Example: Using sha256 hash
     *
     * ```php
     * <?php
     * $fileData = '/9j/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkKDA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/yQALCAABAAEBAREA/8wABgAQEAX/2gAIAQEAAD8A0s8g/9k='; // very small jpeg
     * $I->seeBinaryResponseEquals(hash("sha256", base64_decode($fileData)), 'sha256');
     * ?>
     * ```
     *
     * @param $hash the hashed data response expected
     * @param $algo the hash algorithm to use. Default md5.
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::seeBinaryResponseEquals()
     */
    public function canSeeBinaryResponseEquals($hash, $algo = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeBinaryResponseEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if the hash of a binary response is not the same as provided.
     *
     * ```php
     * <?php
     * $I->dontSeeBinaryResponseEquals("8c90748342f19b195b9c6b4eff742ded");
     * ?>
     * ```
     * Opposite to `seeBinaryResponseEquals`
     *
     * @param $hash the hashed data response expected
     * @param $algo the hash algorithm to use. Default md5.
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::dontSeeBinaryResponseEquals()
     */
    public function dontSeeBinaryResponseEquals($hash, $algo = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeBinaryResponseEquals', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks if the hash of a binary response is not the same as provided.
     *
     * ```php
     * <?php
     * $I->dontSeeBinaryResponseEquals("8c90748342f19b195b9c6b4eff742ded");
     * ?>
     * ```
     * Opposite to `seeBinaryResponseEquals`
     *
     * @param $hash the hashed data response expected
     * @param $algo the hash algorithm to use. Default md5.
     * @part json
     * @part xml
     * @see \Codeception\Module\REST::dontSeeBinaryResponseEquals()
     */
    public function cantSeeBinaryResponseEquals($hash, $algo = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeBinaryResponseEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Prevents automatic redirects to be followed by the client
     *
     * ```php
     * <?php
     * $I->stopFollowingRedirects();
     * ```
     *
     * @part xml
     * @part json
     * @see \Codeception\Module\REST::stopFollowingRedirects()
     */
    public function stopFollowingRedirects() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('stopFollowingRedirects', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Enables automatic redirects to be followed by the client
     *
     * ```php
     * <?php
     * $I->startFollowingRedirects();
     * ```
     *
     * @part xml
     * @part json
     * @see \Codeception\Module\REST::startFollowingRedirects()
     */
    public function startFollowingRedirects() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('startFollowingRedirects', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Import the SQL dump file if populate is enabled.
     *
     * @example
     * ```php
     * // Import a dump file passing the absolute path.
     * $I->importSqlDumpFile(codecept_data_dir('dumps/start.sql'));
     * ```
     *
     * Specifying a dump file that file will be imported.
     *
     * @param null|string $dumpFile The dump file that should be imported in place of the default one.
     *
     * @throws \InvalidArgumentException If the specified file does not exist.
     * @see \Codeception\Module\WPDb::importSqlDumpFile()
     */
    public function importSqlDumpFile($dumpFile = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('importSqlDumpFile', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that an option is not in the database for the current blog.
     *
     * If the value is an object or an array then the serialized option will be checked.
     *
     * @example
     * ```php
     * $I->dontHaveOptionInDatabase('posts_per_page');
     * $I->dontSeeOptionInDatabase('posts_per_page');
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeOptionInDatabase()
     */
    public function dontSeeOptionInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeOptionInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that an option is not in the database for the current blog.
     *
     * If the value is an object or an array then the serialized option will be checked.
     *
     * @example
     * ```php
     * $I->dontHaveOptionInDatabase('posts_per_page');
     * $I->dontSeeOptionInDatabase('posts_per_page');
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeOptionInDatabase()
     */
    public function cantSeeOptionInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeOptionInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns a prefixed table name for the current blog.
     *
     * If the table is not one to be prefixed (e.g. `users`) then the proper table name will be returned.
     *
     * @example
     * ```php
     * // Will return wp_users.
     * $usersTable = $I->grabPrefixedTableNameFor('users');
     * // Will return wp_options.
     * $optionsTable = $I->grabPrefixedTableNameFor('options');
     * // Use a different blog and get its options table.
     * $I->useBlog(2);
     * $blogOptionsTable = $I->grabPrefixedTableNameFor('options');
     * ```
     *
     * @param  string $tableName The table name, e.g. `options`.
     *
     * @return string            The prefixed table name, e.g. `wp_options` or `wp_2_options`.
     * @see \Codeception\Module\WPDb::grabPrefixedTableNameFor()
     */
    public function grabPrefixedTableNameFor($tableName = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabPrefixedTableNameFor', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a post meta value in the database for the current blog.
     *
     * If the `meta_value` is an object or an array then the check will be made for serialized values.
     *
     * @example
     * ```php
     * $postId = $I->havePostInDatabase(['meta_input' => ['foo' => 'bar']];
     * $I->seePostMetaInDatabase(['post_id' => '$postId', 'meta_key' => 'foo']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seePostMetaInDatabase()
     */
    public function seePostMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seePostMetaInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a post meta value in the database for the current blog.
     *
     * If the `meta_value` is an object or an array then the check will be made for serialized values.
     *
     * @example
     * ```php
     * $postId = $I->havePostInDatabase(['meta_input' => ['foo' => 'bar']];
     * $I->seePostMetaInDatabase(['post_id' => '$postId', 'meta_key' => 'foo']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seePostMetaInDatabase()
     */
    public function canSeePostMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seePostMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a link in the `links` table of the database.
     *
     * @example
     * ```php
     * // Asserts a link exists by name.
     * $I->seeLinkInDatabase(['link_name' => 'my-link']);
     * // Asserts at least one link exists for the user.
     * $I->seeLinkInDatabase(['link_owner' => $userId]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeLinkInDatabase()
     */
    public function seeLinkInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeLinkInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a link in the `links` table of the database.
     *
     * @example
     * ```php
     * // Asserts a link exists by name.
     * $I->seeLinkInDatabase(['link_name' => 'my-link']);
     * // Asserts at least one link exists for the user.
     * $I->seeLinkInDatabase(['link_owner' => $userId]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeLinkInDatabase()
     */
    public function canSeeLinkInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeLinkInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a link is not in the `links` database table.
     *
     * @example
     * ```php
     * $I->dontSeeLinkInDatabase(['link_url' => 'http://example.com']);
     * $I->dontSeeLinkInDatabase(['link_url' => 'http://example.com', 'link_name' => 'example']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeLinkInDatabase()
     */
    public function dontSeeLinkInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeLinkInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a link is not in the `links` database table.
     *
     * @example
     * ```php
     * $I->dontSeeLinkInDatabase(['link_url' => 'http://example.com']);
     * $I->dontSeeLinkInDatabase(['link_url' => 'http://example.com', 'link_name' => 'example']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeLinkInDatabase()
     */
    public function cantSeeLinkInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeLinkInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a post meta value does not exist.
     *
     * If the meta value is an object or an array then the check will be made on its serialized version.
     *
     * @example
     * ```php
     * $postId = $I->havePostInDatabase(['meta_input' => ['foo' => 'bar']]);
     * $I->dontSeePostMetaInDatabase(['post_id' => $postId, 'meta_key' => 'woot']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeePostMetaInDatabase()
     */
    public function dontSeePostMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeePostMetaInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a post meta value does not exist.
     *
     * If the meta value is an object or an array then the check will be made on its serialized version.
     *
     * @example
     * ```php
     * $postId = $I->havePostInDatabase(['meta_input' => ['foo' => 'bar']]);
     * $I->dontSeePostMetaInDatabase(['post_id' => $postId, 'meta_key' => 'woot']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeePostMetaInDatabase()
     */
    public function cantSeePostMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeePostMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a post to term relation exists in the database.
     *
     * The method will check the "term_relationships" table.
     *
     * @example
     * ```php
     * $fiction = $I->haveTermInDatabase('fiction', 'genre');
     * $postId = $I->havePostInDatabase(['tax_input' => ['genre' => ['fiction']]]);
     * $I->seePostWithTermInDatabase($postId, $fiction['term_taxonomy_id']);
     * ```
     *
     * @param  int          $post_id           The post ID.
     * @param  int          $term_taxonomy_id  The term `term_id` or `term_taxonomy_id`; if the `$taxonomy` argument is
     *                                         passed this parameter will be interpreted as a `term_id`, else as a
     *                                         `term_taxonomy_id`.
     * @param  int|null     $term_order        The order the term applies to the post, defaults to `null` to not use
     *                                         the
     *                                         term order.
     * @param  string|null  $taxonomy          The taxonomy the `term_id` is for; if passed this parameter will be used
     *                                         to build a `taxonomy_term_id` from the `term_id`.
     * @throws ModuleException If a `term_id` is specified but it cannot be matched to the `taxonomy`.
     * @see \Codeception\Module\WPDb::seePostWithTermInDatabase()
     */
    public function seePostWithTermInDatabase($post_id, $term_taxonomy_id, $term_order = null, $taxonomy = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seePostWithTermInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a post to term relation exists in the database.
     *
     * The method will check the "term_relationships" table.
     *
     * @example
     * ```php
     * $fiction = $I->haveTermInDatabase('fiction', 'genre');
     * $postId = $I->havePostInDatabase(['tax_input' => ['genre' => ['fiction']]]);
     * $I->seePostWithTermInDatabase($postId, $fiction['term_taxonomy_id']);
     * ```
     *
     * @param  int          $post_id           The post ID.
     * @param  int          $term_taxonomy_id  The term `term_id` or `term_taxonomy_id`; if the `$taxonomy` argument is
     *                                         passed this parameter will be interpreted as a `term_id`, else as a
     *                                         `term_taxonomy_id`.
     * @param  int|null     $term_order        The order the term applies to the post, defaults to `null` to not use
     *                                         the
     *                                         term order.
     * @param  string|null  $taxonomy          The taxonomy the `term_id` is for; if passed this parameter will be used
     *                                         to build a `taxonomy_term_id` from the `term_id`.
     * @throws ModuleException If a `term_id` is specified but it cannot be matched to the `taxonomy`.
     * @see \Codeception\Module\WPDb::seePostWithTermInDatabase()
     */
    public function canSeePostWithTermInDatabase($post_id, $term_taxonomy_id, $term_order = null, $taxonomy = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seePostWithTermInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a user is in the database.
     *
     * The method will check the "users" table.
     *
     * @example
     * ```php
     * $I->seeUserInDatabase([
     *     "user_email" => "<EMAIL>",
     *     "user_login" => "login name"
     * ])
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeUserInDatabase()
     */
    public function seeUserInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeUserInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a user is in the database.
     *
     * The method will check the "users" table.
     *
     * @example
     * ```php
     * $I->seeUserInDatabase([
     *     "user_email" => "<EMAIL>",
     *     "user_login" => "login name"
     * ])
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeUserInDatabase()
     */
    public function canSeeUserInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeUserInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a user is not in the database.
     *
     * @example
     * ```php
     * // Asserts a user does not exist in the database.
     * $I->dontSeeUserInDatabase(['user_login' => 'luca']);
     * // Asserts a user with email and login is not in the database.
     * $I->dontSeeUserInDatabase(['user_login' => 'luca', 'user_email' => '<EMAIL>']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeUserInDatabase()
     */
    public function dontSeeUserInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeUserInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a user is not in the database.
     *
     * @example
     * ```php
     * // Asserts a user does not exist in the database.
     * $I->dontSeeUserInDatabase(['user_login' => 'luca']);
     * // Asserts a user with email and login is not in the database.
     * $I->dontSeeUserInDatabase(['user_login' => 'luca', 'user_email' => '<EMAIL>']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeUserInDatabase()
     */
    public function cantSeeUserInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeUserInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a page in the database.
     *
     * @example
     * ```php
     * // Creates a test page in the database with random values.
     * $randomPageId = $I->havePageInDatabase();
     * // Creates a test page in the database defining its title.
     * $testPageId = $I->havePageInDatabase(['post_title' => 'Test page']);
     * ```
     *
     * @param array $overrides An array of values to override the default ones.
     *
     * @return int The inserted page post ID.
     *
     * @see \Codeception\Module\WPDb::havePostInDatabase()
     * @see \Codeception\Module\WPDb::havePageInDatabase()
     */
    public function havePageInDatabase($overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('havePageInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a post in the database.
     *
     * @example
     * ```php
     * // Insert a post with random values in the database.
     * $randomPostId = $I->havePostInDatabase();
     * // Insert a post with specific values in the database.
     * $I->havePostInDatabase([
     *         'post_type' => 'book',
     *         'post_title' => 'Alice in Wonderland',
     *         'meta_input' => [
     *              'readers_count' => 23
     *          ],
     *         'tax_input' => [
     *              ['genre' => 'fiction']
     *          ]
     * ]);
     * ```
     *
     * @param  array $data An associative array of post data to override default and random generated values.
     *
     * @return int post_id The inserted post ID.
     * @see \Codeception\Module\WPDb::havePostInDatabase()
     */
    public function havePostInDatabase($data = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('havePostInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the posts prefixed table name.
     *
     * @example
     * ```php
     * // Given a `wp_` table prefix returns `wp_posts`.
     * $postsTable = $I->grabPostsTableName();
     * // Given a `wp_` table prefix returns `wp_23_posts`.
     * $I->useBlog(23);
     * $postsTable = $I->grabPostsTableName();
     * ```
     *
     * @return string The prefixed table name, e.g. `wp_posts`
     * @see \Codeception\Module\WPDb::grabPostsTableName()
     */
    public function grabPostsTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabPostsTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the id value of the last table entry.
     * @example
     * ```php
     * $I->haveManyPostsInDatabase();
     * $postsTable = $I->grabPostsTableName();
     * $last = $I->grabLatestEntryByFromDatabase($postsTable, 'ID');
     * ```
     *
     * @param string $tableName The table to fetch the last insertion for.
     * @param string $idColumn The column that is used, in the table, to uniquely identify
     *                         items.
     *
     * @return int The last insertion id.
     * @see \Codeception\Module\WPDb::grabLatestEntryByFromDatabase()
     */
    public function grabLatestEntryByFromDatabase($tableName, $idColumn = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabLatestEntryByFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Adds one or more meta key and value couples in the database for a post.
     *
     * @example
     * ```php
     * // Set the post-meta for a post.
     * $I->havePostmetaInDatabase($postId, 'karma', 23);
     * // Set an array post-meta for a post, it will be serialized in the db.
     * $I->havePostmetaInDatabase($postId, 'data', ['one', 'two']);
     * // Use a loop to insert one meta per row.
     * foreach( ['one', 'two'] as $value){
     *      $I->havePostmetaInDatabase($postId, 'data', $value);
     * }
     * ```
     * @param int    $postId     The post ID.
     * @param string $meta_key   The meta key.
     * @param mixed  $meta_value The value to insert in the database, objects and arrays will be serialized.
     *
     * @return int The inserted meta `meta_id`.
     *
     * @see \Codeception\Module\WPDb::havePostmetaInDatabase()
     */
    public function havePostmetaInDatabase($postId, $meta_key, $meta_value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('havePostmetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the prefixed post meta table name.
     *
     * @example
     * ```php
     * // Returns 'wp_postmeta'.
     * $I->grabPostmetaTableName();
     * // Returns 'wp_23_postmeta'.
     * $I->useBlog(23);
     * $I->grabPostmetaTableName();
     * ```
     *
     * @return string The prefixed `postmeta` table name, e.g. `wp_postmeta`.
     * @see \Codeception\Module\WPDb::grabPostmetaTableName()
     */
    public function grabPostmetaTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabPostmetaTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets a term ID from the database.
     * Looks up the prefixed `terms` table, e.g. `wp_terms`.
     *
     * @example
     * ```php
     * // Return the 'fiction' term 'term_id'.
     * $termId = $I->grabTermIdFromDatabase(['name' => 'fiction']);
     * // Get a term ID by more stringent criteria.
     * $termId = $I->grabTermIdFromDatabase(['name' => 'fiction', 'slug' => 'genre--fiction']);
     * // Return the 'term_id' of the first term for a group.
     * $termId = $I->grabTermIdFromDatabase(['term_group' => 23]);
     * ```
     *
     * @param array $criteria An array of search criteria.
     *
     * @return int The matching term `term_id`
     * @see \Codeception\Module\WPDb::grabTermIdFromDatabase()
     */
    public function grabTermIdFromDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTermIdFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed terms table name, e.g. `wp_terms`.
     *
     * @example
     * ```php
     * // Returns 'wp_terms'.
     * $I->grabTermsTableName();
     * // Returns 'wp_23_terms'.
     * $I->useBlog(23);
     * $I->grabTermsTableName();
     * ```
     *
     * @return string The prefixed terms table name.
     * @see \Codeception\Module\WPDb::grabTermsTableName()
     */
    public function grabTermsTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTermsTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a term in the database.
     *
     * @example
     * ```php
     * // Insert a random 'genre' term in the database.
     * $I->haveTermInDatabase('non-fiction', 'genre');
     * // Insert a term in the database with term meta.
     * $I->haveTermInDatabase('fiction', 'genre', [
     *      'slug' => 'genre--fiction',
     *      'meta' => [
     *         'readers_count' => 23
     *      ]
     * ]);
     * ```
     *
     * @param  string $name      The term name, e.g. "Fuzzy".
     * @param string  $taxonomy  The term taxonomy
     * @param array   $overrides An array of values to override the default ones.
     *
     * @return array An array containing `term_id` and `term_taxonomy_id` of the inserted term.
     * @see \Codeception\Module\WPDb::haveTermInDatabase()
     */
    public function haveTermInDatabase($name, $taxonomy, $overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveTermInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed term and taxonomy table name, e.g. `wp_term_taxonomy`.
     *
     * @example
     * ```php
     * // Returns 'wp_term_taxonomy'.
     * $I->grabTermTaxonomyTableName();
     * // Returns 'wp_23_term_taxonomy'.
     * $I->useBlog(23);
     * $I->grabTermTaxonomyTableName();
     * ```
     *
     * @return string The prefixed term taxonomy table name.
     * @see \Codeception\Module\WPDb::grabTermTaxonomyTableName()
     */
    public function grabTermTaxonomyTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTermTaxonomyTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a term meta row in the database.
     * Objects and array meta values will be serialized.
     *
     * @example
     * ```php
     * $I->haveTermMetaInDatabase($fictionId, 'readers_count', 23);
     * // Insert some meta that will be serialized.
     * $I->haveTermMetaInDatabase($fictionId, 'flags', [3, 4, 89]);
     * // Use a loop to insert one meta per row.
     * foreach([3, 4, 89] as $value) {
     *      $I->haveTermMetaInDatabase($fictionId, 'flag', $value);
     * }
     * ```
     *
     * @param int    $term_id The ID of the term to insert the meta for.
     * @param string $meta_key The key of the meta to insert.
     * @param mixed  $meta_value The value of the meta to insert, if serializable it will be serialized.
     *
     * @return int The inserted term meta `meta_id`.
     * @see \Codeception\Module\WPDb::haveTermMetaInDatabase()
     */
    public function haveTermMetaInDatabase($term_id, $meta_key, $meta_value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveTermMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the terms meta table prefixed name.
     *
     * @example
     * ```php
     * // Returns 'wp_termmeta'.
     * $I->grabTermMetaTableName();
     * // Returns 'wp_23_termmeta'.
     * $I->useBlog(23);
     * $I->grabTermMetaTableName();
     * ```
     *
     * @return string The prefixed term meta table name.
     * @see \Codeception\Module\WPDb::grabTermMetaTableName()
     */
    public function grabTermMetaTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTermMetaTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets a `term_taxonomy_id` from the database.
     *
     * Looks up the prefixed `terms_relationships` table, e.g. `wp_term_relationships`.
     *
     * @example
     * ```php
     * // Get the `term_taxonomy_id` for a term and a taxonomy.
     * $I->grabTermTaxonomyIdFromDatabase(['term_id' => $fictionId, 'taxonomy' => 'genre']);
     * // Get the `term_taxonomy_id` for the first term with a count of 23.
     * $I->grabTermTaxonomyIdFromDatabase(['count' => 23]);
     * ```
     *
     * @param array $criteria An array of search criteria.
     *
     * @return int The matching term `term_taxonomy_id`
     * @see \Codeception\Module\WPDb::grabTermTaxonomyIdFromDatabase()
     */
    public function grabTermTaxonomyIdFromDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTermTaxonomyIdFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Creates a term relationship in the database.
     *
     * No check about the consistency of the insertion is made. E.g. a post could be assigned a term from
     * a taxonomy that's not registered for that post type.
     *
     * @example
     * ```php
     * // Assign the `fiction` term to a book.
     * $I->haveTermRelationshipInDatabase($bookId, $fictionId);
     * ```
     *
     * @param int $object_id  A post ID, a user ID or anything that can be assigned a taxonomy term.
     * @param int $term_taxonomy_id The `term_taxonomy_id` of the term and taxonomy to create a relation with.
     * @param int $term_order Defaults to `0`.
     * @see \Codeception\Module\WPDb::haveTermRelationshipInDatabase()
     */
    public function haveTermRelationshipInDatabase($object_id, $term_taxonomy_id, $term_order = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveTermRelationshipInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed term relationships table name, e.g. `wp_term_relationships`.
     *
     * @example
     * ```php
     * $I->grabTermRelationshipsTableName();
     * ```
     *
     * @return string The `term_relationships` table complete name, including the table prefix.
     * @see \Codeception\Module\WPDb::grabTermRelationshipsTableName()
     */
    public function grabTermRelationshipsTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTermRelationshipsTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a page in the database.
     *
     * @example
     * ```php
     * // Asserts a page with an exists in the database.
     * $I->seePageInDatabase(['ID' => 23]);
     * // Asserts a page with a slug and ID exists in the database.
     * $I->seePageInDatabase(['post_title' => 'Test Page', 'ID' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seePageInDatabase()
     */
    public function seePageInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seePageInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a page in the database.
     *
     * @example
     * ```php
     * // Asserts a page with an exists in the database.
     * $I->seePageInDatabase(['ID' => 23]);
     * // Asserts a page with a slug and ID exists in the database.
     * $I->seePageInDatabase(['post_title' => 'Test Page', 'ID' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seePageInDatabase()
     */
    public function canSeePageInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seePageInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a post in the database.
     *
     * @example
     * ```php
     * // Assert a post exists in the database.
     * $I->seePostInDatabase(['ID' => 23]);
     * // Assert a post with a slug and ID exists in the database.
     * $I->seePostInDatabase(['post_content' => 'test content', 'ID' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seePostInDatabase()
     */
    public function seePostInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seePostInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a post in the database.
     *
     * @example
     * ```php
     * // Assert a post exists in the database.
     * $I->seePostInDatabase(['ID' => 23]);
     * // Assert a post with a slug and ID exists in the database.
     * $I->seePostInDatabase(['post_content' => 'test content', 'ID' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seePostInDatabase()
     */
    public function canSeePostInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seePostInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a page is not in the database.
     *
     * @example
     * ```php
     * // Assert a page with an ID does not exist.
     * $I->dontSeePageInDatabase(['ID' => 23]);
     * // Assert a page with a slug and ID.
     * $I->dontSeePageInDatabase(['post_name' => 'test', 'ID' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeePageInDatabase()
     */
    public function dontSeePageInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeePageInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a page is not in the database.
     *
     * @example
     * ```php
     * // Assert a page with an ID does not exist.
     * $I->dontSeePageInDatabase(['ID' => 23]);
     * // Assert a page with a slug and ID.
     * $I->dontSeePageInDatabase(['post_name' => 'test', 'ID' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeePageInDatabase()
     */
    public function cantSeePageInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeePageInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a post is not in the database.
     *
     * @example
     * ```php
     * // Asserts a post with title 'Test' is not in the database.
     * $I->dontSeePostInDatabase(['post_title' => 'Test']);
     * // Asserts a post with title 'Test' and content 'Test content' is not in the database.
     * $I->dontSeePostInDatabase(['post_title' => 'Test', 'post_content' => 'Test content']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeePostInDatabase()
     */
    public function dontSeePostInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeePostInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a post is not in the database.
     *
     * @example
     * ```php
     * // Asserts a post with title 'Test' is not in the database.
     * $I->dontSeePostInDatabase(['post_title' => 'Test']);
     * // Asserts a post with title 'Test' and content 'Test content' is not in the database.
     * $I->dontSeePostInDatabase(['post_title' => 'Test', 'post_content' => 'Test content']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeePostInDatabase()
     */
    public function cantSeePostInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeePostInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a comment in the database.
     *
     * Will look up the "comments" table.
     *
     * @example
     * ```php
     * $I->seeCommentInDatabase(['comment_ID' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeCommentInDatabase()
     */
    public function seeCommentInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeCommentInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a comment in the database.
     *
     * Will look up the "comments" table.
     *
     * @example
     * ```php
     * $I->seeCommentInDatabase(['comment_ID' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeCommentInDatabase()
     */
    public function canSeeCommentInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeCommentInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a comment is not in the database.
     *
     * Will look up the "comments" table.
     *
     * @example
     * ```php
     * // Checks for one comment.
     * $I->dontSeeCommentInDatabase(['comment_ID' => 23]);
     * // Checks for comments from a user.
     * $I->dontSeeCommentInDatabase(['user_id' => 89]);
     * ```
     *
     * @param  array $criteria The serach criteria.
     * @see \Codeception\Module\WPDb::dontSeeCommentInDatabase()
     */
    public function dontSeeCommentInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeCommentInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a comment is not in the database.
     *
     * Will look up the "comments" table.
     *
     * @example
     * ```php
     * // Checks for one comment.
     * $I->dontSeeCommentInDatabase(['comment_ID' => 23]);
     * // Checks for comments from a user.
     * $I->dontSeeCommentInDatabase(['user_id' => 89]);
     * ```
     *
     * @param  array $criteria The serach criteria.
     * @see \Codeception\Module\WPDb::dontSeeCommentInDatabase()
     */
    public function cantSeeCommentInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeCommentInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a comment meta value is in the database.
     * Will look up the "commentmeta" table.
     *
     * @example
     * ```php
     * // Assert a specifid meta for a comment exists.
     * $I->seeCommentMetaInDatabase(['comment_ID' => $commentId, 'meta_key' => 'karma', 'meta_value' => 23]);
     * // Assert the comment has at least one meta set.
     * $I->seeCommentMetaInDatabase(['comment_ID' => $commentId]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeCommentMetaInDatabase()
     */
    public function seeCommentMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeCommentMetaInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a comment meta value is in the database.
     * Will look up the "commentmeta" table.
     *
     * @example
     * ```php
     * // Assert a specifid meta for a comment exists.
     * $I->seeCommentMetaInDatabase(['comment_ID' => $commentId, 'meta_key' => 'karma', 'meta_value' => 23]);
     * // Assert the comment has at least one meta set.
     * $I->seeCommentMetaInDatabase(['comment_ID' => $commentId]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeCommentMetaInDatabase()
     */
    public function canSeeCommentMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeCommentMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a comment meta value is not in the database.
     *
     * Will look up the "commentmeta" table.
     *
     * @example
     * ```php
     * // Delete a comment `karma` meta.
     * $I->dontSeeCommentMetaInDatabase(['comment_id' => 23, 'meta_key' => 'karma']);
     * // Delete all meta for a comment.
     * $I->dontSeeCommentMetaInDatabase(['comment_id' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeCommentMetaInDatabase()
     */
    public function dontSeeCommentMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeCommentMetaInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a comment meta value is not in the database.
     *
     * Will look up the "commentmeta" table.
     *
     * @example
     * ```php
     * // Delete a comment `karma` meta.
     * $I->dontSeeCommentMetaInDatabase(['comment_id' => 23, 'meta_key' => 'karma']);
     * // Delete all meta for a comment.
     * $I->dontSeeCommentMetaInDatabase(['comment_id' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeCommentMetaInDatabase()
     */
    public function cantSeeCommentMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeCommentMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a user meta value in the database.
     *
     * @example
     * ```php
     * $I->seeUserMetaInDatabase(['user_id' => 23, 'meta_key' => 'karma']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeUserMetaInDatabase()
     */
    public function seeUserMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeUserMetaInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a user meta value in the database.
     *
     * @example
     * ```php
     * $I->seeUserMetaInDatabase(['user_id' => 23, 'meta_key' => 'karma']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeUserMetaInDatabase()
     */
    public function canSeeUserMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeUserMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Check that a user meta value is not in the database.
     *
     * @example
     * ```php
     * // Asserts a user does not have a 'karma' meta assigned.
     * $I->dontSeeUserMetaInDatabase(['user_id' => 23, 'meta_key' => 'karma']);
     * // Asserts no user has any 'karma' meta assigned.
     * $I->dontSeeUserMetaInDatabase(['meta_key' => 'karma']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeUserMetaInDatabase()
     */
    public function dontSeeUserMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeUserMetaInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Check that a user meta value is not in the database.
     *
     * @example
     * ```php
     * // Asserts a user does not have a 'karma' meta assigned.
     * $I->dontSeeUserMetaInDatabase(['user_id' => 23, 'meta_key' => 'karma']);
     * // Asserts no user has any 'karma' meta assigned.
     * $I->dontSeeUserMetaInDatabase(['meta_key' => 'karma']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeUserMetaInDatabase()
     */
    public function cantSeeUserMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeUserMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a link from the database.
     *
     * @example
     * ```php
     * $I->dontHaveLinkInDatabase(['link_url' => 'http://example.com']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontHaveLinkInDatabase()
     */
    public function dontHaveLinkInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveLinkInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Deletes a database entry.
     *
     * @example
     * ```php
     * $I->dontHaveInDatabase('custom_table', ['book_ID' => 23, 'book_genre' => 'fiction']);
     * ```
     *
     * @param  string $table    The table name.
     * @param  array  $criteria An associative array of the column names and values to use as deletion criteria.
     * @see \Codeception\Module\WPDb::dontHaveInDatabase()
     */
    public function dontHaveInDatabase($table, $criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes an entry from the term_relationships table.
     *
     * @example
     * ```php
     * // Remove the relation between a post and a category.
     * $I->dontHaveTermRelationshipInDatabase(['object_id' => $postId, 'term_taxonomy_id' => $ttaxId]);
     * // Remove all terms for a post.
     * $I->dontHaveTermMetaInDatabase(['object_id' => $postId]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontHaveTermRelationshipInDatabase()
     */
    public function dontHaveTermRelationshipInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveTermRelationshipInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes an entry from the `term_taxonomy` table.
     *
     * @example
     * ```php
     * // Remove a specific term from the genre taxonomy.
     * $I->dontHaveTermTaxonomyInDatabase(['term_id' => $postId, 'taxonomy' => 'genre']);
     * // Remove all terms for a taxonomy.
     * $I->dontHaveTermTaxonomyInDatabase(['taxonomy' => 'genre']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontHaveTermTaxonomyInDatabase()
     */
    public function dontHaveTermTaxonomyInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveTermTaxonomyInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes an entry from the usermeta table.
     *
     * @example
     * ```php
     * // Remove the `karma` user meta for a user.
     * $I->dontHaveUserMetaInDatabase(['user_id' => 23, 'meta_key' => 'karma']);
     * // Remove all the user meta for a user.
     * $I->dontHaveUserMetaInDatabase(['user_id' => 23]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontHaveUserMetaInDatabase()
     */
    public function dontHaveUserMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveUserMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets a user meta from the database.
     *
     * @example
     * ```php
     * // Returns a user 'karma' value.
     * $I->grabUserMetaFromDatabase($userId, 'karma');
     * // Returns an array, the unserialized version of the value stored in the database.
     * $I->grabUserMetaFromDatabase($userId, 'api_data');
     * ```
     *
     * @param int    $userId The ID of th user to get the meta for.
     * @param string $meta_key The meta key to fetch the value for.
     *
     * @return array An associative array of meta key/values.
     *
     * @throws \Exception If the search criteria is incoherent.
     * @see \Codeception\Module\WPDb::grabUserMetaFromDatabase()
     */
    public function grabUserMetaFromDatabase($userId, $meta_key) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabUserMetaFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns all entries matching a criteria from the database.
     *
     * @example
     * ```php
     * $books = $I->grabPrefixedTableNameFor('books');
     * $I->grabAllFromDatabase($books, 'title', ['genre' => 'fiction']);
     * ```
     *
     * @param string $table The table to grab the values from.
     * @param string $column The column to fetch.
     * @param array  $criteria The search criteria.
     *
     * @return array An array of results.
     *
     * @throws \Exception If the criteria is inconsistent.
     * @see \Codeception\Module\WPDb::grabAllFromDatabase()
     */
    public function grabAllFromDatabase($table, $column, $criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabAllFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a transient in the database.
     *
     * If the value is an array or an object then the value will be serialized.
     * Since the transients are set in the context of tests it's not possible to
     * set an expiration directly.
     *
     * @example
     * ```php
     * // Store an array in the `tweets` transient.
     * $I->haveTransientInDatabase('tweets', $tweets);
     * ```
     *
     * @param string $transient The transient name.
     * @param mixed  $value The transient value.
     *
     * @return int The inserted option `option_id`.
     * @see \Codeception\Module\WPDb::haveTransientInDatabase()
     */
    public function haveTransientInDatabase($transient, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveTransientInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts an option in the database.
     *
     * @example
     * ```php
     * $I->haveOptionInDatabase('posts_per_page', 23);
     * $I->haveOptionInDatabase('my_plugin_options', ['key_one' => 'value_one', 'key_two' => 89]);
     * ```
     *
     * If the option value is an object or an array then the value will be serialized.
     *
     * @param  string $option_name The option name.
     * @param  mixed  $option_value The option value; if an array or object it will be serialized.
     * @param string  $autoload Wether the option should be autoloaded by WordPress or not.
     *
     * @return int The inserted option `option_id`
     * @see \Codeception\Module\WPDb::haveOptionInDatabase()
     */
    public function haveOptionInDatabase($option_name, $option_value, $autoload = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveOptionInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a transient from the database.
     *
     * @example
     * ```php
     * // Removes the `tweets` transient from the database, if set.
     * $I->dontHaveTransientInDatabase('tweets');
     * ```
     *
     * @param string $transient The name of the transient to delete.
     * @see \Codeception\Module\WPDb::dontHaveTransientInDatabase()
     */
    public function dontHaveTransientInDatabase($transient) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveTransientInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes an entry from the options table.
     *
     * @example
     * ```php
     * // Remove the `foo` option.
     * $I->dontHaveOptionInDatabase('foo');
     * // Remove the 'bar' option only if it has the `baz` value.
     * $I->dontHaveOptionInDatabase('bar', 'baz');
     * ```
     *
     * @param string     $key   The option name.
     * @param null|mixed $value If set the option will only be removed if its value matches the passed one.
     * @see \Codeception\Module\WPDb::dontHaveOptionInDatabase()
     */
    public function dontHaveOptionInDatabase($key, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveOptionInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a site option in the database.
     *
     * If the value is an array or an object then the value will be serialized.
     *
     * @example
     * ```php
     * $fooCountOptionId = $I->haveSiteOptionInDatabase('foo_count','23');
     * ```
     *
     * @param string $key The name of the option to insert.
     * @param mixed  $value The value ot insert for the option.
     *
     * @return int The inserted option `option_id`.
     * @see \Codeception\Module\WPDb::haveSiteOptionInDatabase()
     */
    public function haveSiteOptionInDatabase($key, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveSiteOptionInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sets the current blog to the main one (`blog_id` 1).
     *
     * @example
     * ```php
     * // Switch to the blog with ID 23.
     * $I->useBlog(23);
     * // Switch back to the main blog.
     * $I->useMainBlog();
     * ```
     * @see \Codeception\Module\WPDb::useMainBlog()
     */
    public function useMainBlog() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('useMainBlog', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sets the blog to be used.
     *
     * This has nothing to do with WordPress `switch_to_blog` function, this code will affect the table prefixes used.
     *
     * @example
     * ```php
     * // Switch to the blog with ID 23.
     * $I->useBlog(23);
     * // Switch back to the main blog.
     * $I->useMainBlog();
     * ```
     *
     * @param int $blogId The ID of the blog to use.
     * @see \Codeception\Module\WPDb::useBlog()
     */
    public function useBlog($blogId = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('useBlog', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a site option from the database.
     *
     * @example
     * ```php
     * // Remove the `foo_count` option.
     * $I->dontHaveSiteOptionInDatabase('foo_count');
     * // Remove the `foo_count` option only if its value is `23`.
     * $I->dontHaveSiteOptionInDatabase('foo_count', 23);
     * ```
     *
     * @param string $key The option name.
     * @param null|mixed $value If set the option will only be removed it its value matches the specified one.
     * @see \Codeception\Module\WPDb::dontHaveSiteOptionInDatabase()
     */
    public function dontHaveSiteOptionInDatabase($key, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveSiteOptionInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a site transient in the database.
     * If the value is an array or an object then the value will be serialized.
     *
     * @example
     * ```php
     * $I->haveSiteTransientInDatabase('total_comments_count', 23);
     * // This value will be serialized.
     * $I->haveSiteTransientInDatabase('api_data', ['user' => 'luca', 'token' => '11ae3ijns-j83']);
     * ```
     *
     * @param string $key The key of the site transient to insert, w/o the `_site_transient_` prefix.
     * @param mixed $value The value to insert; if serializable the value will be serialized.
     *
     * @return int The inserted transient `option_id`
     * @see \Codeception\Module\WPDb::haveSiteTransientInDatabase()
     */
    public function haveSiteTransientInDatabase($key, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveSiteTransientInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a site transient from the database.
     *
     * @example
     * ```php
     * $I->dontHaveSiteTransientInDatabase(['my_plugin_site_buffer']);
     * ```
     *
     * @param string $key The name of the transient to delete.
     * @see \Codeception\Module\WPDb::dontHaveSiteTransientInDatabase()
     */
    public function dontHaveSiteTransientInDatabase($key) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveSiteTransientInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets a site option from the database.
     *
     * @example
     * ```php
     * $fooCountOptionId = $I->haveSiteOptionInDatabase('foo_count','23');
     * ```
     *
     * @param string $key The name of the option to read from the database.
     *
     * @return mixed|string The value of the option stored in the database, unserialized if serialized.
     * @see \Codeception\Module\WPDb::grabSiteOptionFromDatabase()
     */
    public function grabSiteOptionFromDatabase($key) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabSiteOptionFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets an option value from the database.
     *
     * @example
     * ```php
     * $count = $I->grabOptionFromDatabase('foo_count');
     * ```
     *
     * @param string $option_name The name of the option to grab from the database.
     *
     * @return mixed|string The option value. If the value is serialized it will be unserialized.
     * @see \Codeception\Module\WPDb::grabOptionFromDatabase()
     */
    public function grabOptionFromDatabase($option_name) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabOptionFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets a site transient from the database.
     *
     * @example
     * ```php
     * $I->grabSiteTransientFromDatabase('total_comments');
     * $I->grabSiteTransientFromDatabase('api_data');
     * ```
     *
     * @param string $key The site transient to fetch the value for, w/o the `_site_transient_` prefix.
     *
     * @return mixed|string The value of the site transient. If the value is serialized it will be unserialized.
     * @see \Codeception\Module\WPDb::grabSiteTransientFromDatabase()
     */
    public function grabSiteTransientFromDatabase($key) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabSiteTransientFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a site option is in the database.
     *
     * @example
     * ```php
     * // Check a transient exists.
     * $I->seeSiteSiteTransientInDatabase('total_counts');
     * // Check a transient exists and has a specific value.
     * $I->seeSiteSiteTransientInDatabase('total_counts', 23);
     * ```
     *
     * @param string     $key The name of the transient to check for, w/o the `_site_transient_` prefix.
     * @param mixed|null $value If provided then the assertion will include the value.
     * @see \Codeception\Module\WPDb::seeSiteSiteTransientInDatabase()
     */
    public function seeSiteSiteTransientInDatabase($key, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeSiteSiteTransientInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a site option is in the database.
     *
     * @example
     * ```php
     * // Check a transient exists.
     * $I->seeSiteSiteTransientInDatabase('total_counts');
     * // Check a transient exists and has a specific value.
     * $I->seeSiteSiteTransientInDatabase('total_counts', 23);
     * ```
     *
     * @param string     $key The name of the transient to check for, w/o the `_site_transient_` prefix.
     * @param mixed|null $value If provided then the assertion will include the value.
     * @see \Codeception\Module\WPDb::seeSiteSiteTransientInDatabase()
     */
    public function canSeeSiteSiteTransientInDatabase($key, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeSiteSiteTransientInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if an option is in the database for the current blog.
     * If checking for an array or an object then the serialized version will be checked for.
     *
     * @example
     * ```php
     * // Checks an option is in the database.
     * $I->seeOptionInDatabase('tables_version');
     * // Checks an option is in the database and has a specific value.
     * $I->seeOptionInDatabase('tables_version', '1.0');
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeOptionInDatabase()
     */
    public function seeOptionInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeOptionInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks if an option is in the database for the current blog.
     * If checking for an array or an object then the serialized version will be checked for.
     *
     * @example
     * ```php
     * // Checks an option is in the database.
     * $I->seeOptionInDatabase('tables_version');
     * // Checks an option is in the database and has a specific value.
     * $I->seeOptionInDatabase('tables_version', '1.0');
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeOptionInDatabase()
     */
    public function canSeeOptionInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeOptionInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a site option is in the database.
     *
     * @example
     * ```php
     * // Check that the option is set in the database.
     * $I->seeSiteOptionInDatabase('foo_count');
     * // Check that the option is set and has a specific value.
     * $I->seeSiteOptionInDatabase('foo_count', 23);
     * ```
     *
     * @param string     $key The name of the otpion to check.
     * @param mixed|null $value If set the assertion will also check the option value.
     * @see \Codeception\Module\WPDb::seeSiteOptionInDatabase()
     */
    public function seeSiteOptionInDatabase($key, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeSiteOptionInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a site option is in the database.
     *
     * @example
     * ```php
     * // Check that the option is set in the database.
     * $I->seeSiteOptionInDatabase('foo_count');
     * // Check that the option is set and has a specific value.
     * $I->seeSiteOptionInDatabase('foo_count', 23);
     * ```
     *
     * @param string     $key The name of the otpion to check.
     * @param mixed|null $value If set the assertion will also check the option value.
     * @see \Codeception\Module\WPDb::seeSiteOptionInDatabase()
     */
    public function canSeeSiteOptionInDatabase($key, $value = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeSiteOptionInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts many posts in the database returning their IDs.
     *
     * @param int   $count     The number of posts to insert.
     * @param array $overrides {
     *                         An array of values to override the defaults.
     *                         The `{{n}}` placeholder can be used to have the post count inserted in its place;
     *                         e.g. `Post Title - {{n}}` will be set to `Post Title - 0` for the first post,
     *                         `Post Title - 1` for the second one and so on.
     *                         The same applies to meta values as well.
     *
     * @type array  $meta      An associative array of meta key/values to be set for the post, shorthand for the
     *       `havePostmetaInDatabase` method. e.g. `['one' => 'foo', 'two' => 'bar']`; to have an array value inserted
     *       in a single row serialize it e.g.
     *                    `['serialized_field` => serialize(['one','two','three'])]` otherwise a distinct row will be
     *                    added for each entry. See `havePostmetaInDatabase` method.
     * }
     *
     * @example
     * ```php
     * // Insert 3 random posts.
     * $I->haveManyPostsInDatabase(3);
     * // Insert 3 posts with generated titles.
     * $I->haveManyPostsInDatabase(3, ['post_title' => 'Test post {{n}}']);
     * ```
     *
     * @return array An array of the inserted post IDs.
     * @see \Codeception\Module\WPDb::haveManyPostsInDatabase()
     */
    public function haveManyPostsInDatabase($count, $overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveManyPostsInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a term in the database.
     * Looks up the `terms` and `term_taxonomy` prefixed tables.
     *
     * @example
     * ```php
     * $I->seeTermInDatabase(['slug' => 'genre--fiction']);
     * $I->seeTermInDatabase(['name' => 'Fiction', 'slug' => 'genre--fiction']);
     * ```
     *
     * @param array $criteria An array of criteria to search for the term, can be columns from the `terms` and the
     *                        `term_taxonomy` tables.
     * @see \Codeception\Module\WPDb::seeTermInDatabase()
     */
    public function seeTermInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeTermInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a term in the database.
     * Looks up the `terms` and `term_taxonomy` prefixed tables.
     *
     * @example
     * ```php
     * $I->seeTermInDatabase(['slug' => 'genre--fiction']);
     * $I->seeTermInDatabase(['name' => 'Fiction', 'slug' => 'genre--fiction']);
     * ```
     *
     * @param array $criteria An array of criteria to search for the term, can be columns from the `terms` and the
     *                        `term_taxonomy` tables.
     * @see \Codeception\Module\WPDb::seeTermInDatabase()
     */
    public function canSeeTermInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeTermInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a term from the database.
     *
     * @example
     * ```php
     * $I->dontHaveTermInDatabase(['name' => 'romance']);
     * $I->dontHaveTermInDatabase(['slug' => 'genre--romance']);
     * ```
     *
     * @param array $criteria  An array of search criteria.
     * @param bool  $purgeMeta Whether the terms meta should be purged along side with the meta or not.
     * @see \Codeception\Module\WPDb::dontHaveTermInDatabase()
     */
    public function dontHaveTermInDatabase($criteria, $purgeMeta = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveTermInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a term meta from the database.
     *
     * @example
     * ```php
     * // Remove the "karma" key.
     * $I->dontHaveTermMetaInDatabase(['term_id' => $termId, 'meta_key' => 'karma']);
     * // Remove all meta for the term.
     * $I->dontHaveTermMetaInDatabase(['term_id' => $termId]);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontHaveTermMetaInDatabase()
     */
    public function dontHaveTermMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveTermMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Makes sure a term is not in the database.
     *
     * Looks up both the `terms` table and the `term_taxonomy` tables.
     *
     * @example
     * ```php
     * // Asserts a 'fiction' term is not in the database.
     * $I->dontSeeTermInDatabase(['name' => 'fiction']);
     * // Asserts a 'fiction' term with slug 'genre--fiction' is not in the database.
     * $I->dontSeeTermInDatabase(['name' => 'fiction', 'slug' => 'genre--fiction']);
     * ```
     *
     * @param array $criteria An array of criteria to search for the term, can be columns from the `terms` and the
     *                        `term_taxonomy` tables.
     * @see \Codeception\Module\WPDb::dontSeeTermInDatabase()
     */
    public function dontSeeTermInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeTermInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Makes sure a term is not in the database.
     *
     * Looks up both the `terms` table and the `term_taxonomy` tables.
     *
     * @example
     * ```php
     * // Asserts a 'fiction' term is not in the database.
     * $I->dontSeeTermInDatabase(['name' => 'fiction']);
     * // Asserts a 'fiction' term with slug 'genre--fiction' is not in the database.
     * $I->dontSeeTermInDatabase(['name' => 'fiction', 'slug' => 'genre--fiction']);
     * ```
     *
     * @param array $criteria An array of criteria to search for the term, can be columns from the `terms` and the
     *                        `term_taxonomy` tables.
     * @see \Codeception\Module\WPDb::dontSeeTermInDatabase()
     */
    public function cantSeeTermInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeTermInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts many comments in the database.
     *
     *
     * @example
     * ```php
     * // Insert 3 random comments for a post.
     * $I->haveManyCommentsInDatabase(3, $postId);
     * // Insert 3 random comments for a post.
     * $I->haveManyCommentsInDatabase(3, $postId, ['comment_content' => 'Comment {{n}}']);
     * ```
     *
     * @param int   $count           The number of comments to insert.
     * @param   int $comment_post_ID The comment parent post ID.
     * @param array $overrides       An associative array to override the defaults.
     *
     * @return int[] An array containing the inserted comments IDs.
     * @see \Codeception\Module\WPDb::haveManyCommentsInDatabase()
     */
    public function haveManyCommentsInDatabase($count, $comment_post_ID, $overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveManyCommentsInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a comment in the database.
     *
     * @example
     * ```php
     * $I->haveCommentInDatabase($postId, ['comment_content' => 'Test Comment', 'comment_karma' => 23]);
     * ```
     *
     * @param  int   $comment_post_ID The id of the post the comment refers to.
     * @param  array $data            The comment data overriding default and random generated values.
     *
     * @return int The inserted comment `comment_id`.
     * @see \Codeception\Module\WPDb::haveCommentInDatabase()
     */
    public function haveCommentInDatabase($comment_post_ID, $data = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveCommentInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a comment meta field in the database.
     * Array and object meta values will be serialized.
     *
     * @example
     * ```php
     * $I->haveCommentMetaInDatabase($commentId, 'api_ID', 23);
     * // The value will be serialized.
     * $apiData = ['ID' => 23, 'user' => 89, 'origin' => 'twitter'];
     * $I->haveCommentMetaInDatabase($commentId, 'api_data', $apiData);
     * ```
     *
     * @param int    $comment_id The ID of the comment to insert the meta for.
     * @param string $meta_key The key of the comment meta to insert.
     * @param mixed  $meta_value The value of the meta to insert, if serializable it will be serialized.
     *
     * @return int The inserted comment meta ID.
     * @see \Codeception\Module\WPDb::haveCommentMetaInDatabase()
     */
    public function haveCommentMetaInDatabase($comment_id, $meta_key, $meta_value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveCommentMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the prefixed comment meta table name.
     *
     * @example
     * ```php
     * // Get all the values of 'karma' for all comments.
     * $commentMeta = $I->grabCommentmetaTableName();
     * $I->grabAllFromDatabase($commentMeta, 'meta_value', ['meta_key' => 'karma']);
     * ```
     *
     * @return string The complete name of the comment meta table name, including the table prefix.
     * @see \Codeception\Module\WPDb::grabCommentmetaTableName()
     */
    public function grabCommentmetaTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabCommentmetaTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the number of table rows matching a criteria.
     *
     * @example
     * ```php
     * $I->haveManyPostsInDatabase(3, ['post_status' => 'draft' ]);
     * $I->haveManyPostsInDatabase(3, ['post_status' => 'private' ]);
     * // Make sure there are now the expected number of draft posts.
     * $postsTable = $I->grabPostsTableName();
     * $draftsCount = $I->countRowsInDatabase($postsTable, ['post_status' => 'draft']);
     * ```
     *
     * @param string $table    The table to count the rows in.
     * @param array  $criteria Search criteria, if empty all table rows will be counted.
     *
     * @return int The number of table rows matching the search criteria.
     * @see \Codeception\Module\WPDb::countRowsInDatabase()
     */
    public function countRowsInDatabase($table, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('countRowsInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes an entry from the comments table.
     *
     * @example
     * ```php
     * $I->dontHaveCommentInDatabase(['comment_post_ID' => 23, 'comment_url' => 'http://example.copm']);
     * ```
     *
     * @param  array $criteria  An array of search criteria.
     * @param bool $purgeMeta If set to `true` then the meta for the comment will be purged too.
     *
     * @throws \Exception In case of incoherent query criteria.
     * @see \Codeception\Module\WPDb::dontHaveCommentInDatabase()
     */
    public function dontHaveCommentInDatabase($criteria, $purgeMeta = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveCommentInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the comments table name.
     *
     * @example
     * ```php
     * // Will be `wp_comments`.
     * $comments = $I->grabCommentsTableName();
     * // Will be `wp_23_comments`.
     * $I->useBlog(23);
     * $comments = $I->grabCommentsTableName();
     * ```
     *
     * @return string The prefixed table name, e.g. `wp_comments`.
     * @see \Codeception\Module\WPDb::grabCommentsTableName()
     */
    public function grabCommentsTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabCommentsTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a post comment meta from the database
     *
     * @example
     * ```php
     * // Remove all meta for the comment with an ID of 23.
     * $I->dontHaveCommentMetaInDatabase(['comment_id' => 23]);
     * // Remove the `count` comment meta for the comment with an ID of 23.
     * $I->dontHaveCommentMetaInDatabase(['comment_id' => 23, 'meta_key' => 'count']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontHaveCommentMetaInDatabase()
     */
    public function dontHaveCommentMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveCommentMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts many links in the database `links` table.
     *
     * @example
     * ```php
     * // Insert 3 randomly generated links in the database.
     * $linkIds = $I->haveManyLinksInDatabase(3);
     * // Inserts links in the database replacing the `n` placeholder.
     * $linkIds = $I->haveManyLinksInDatabase(3, ['link_url' => 'http://example.org/test-{{n}}']);
     * ```
     *
     * @param int $count The number of links to insert.
     * @param array $overrides Overrides for the default arguments.
     *
     * @return array An array of inserted `link_id`s.
     * @see \Codeception\Module\WPDb::haveManyLinksInDatabase()
     */
    public function haveManyLinksInDatabase($count, $overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveManyLinksInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a link in the database.
     *
     * @example
     * ```php
     * $linkId = $I->haveLinkInDatabase(['link_url' => 'http://example.org']);
     * ```
     *
     * @param  array $overrides The data to insert.
     *
     * @return int The inserted link `link_id`.
     * @see \Codeception\Module\WPDb::haveLinkInDatabase()
     */
    public function haveLinkInDatabase($overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveLinkInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the prefixed links table name.
     *
     * @example
     * ```php
     * // Given a `wp_` table prefix returns `wp_links`.
     * $linksTable = $I->grabLinksTableName();
     * // Given a `wp_` table prefix returns `wp_23_links`.
     * $I->useBlog(23);
     * $linksTable = $I->grabLinksTableName();
     * ```
     *
     * @return string The links table including the blog-aware table prefix.
     * @see \Codeception\Module\WPDb::grabLinksTableName()
     */
    public function grabLinksTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabLinksTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts many users in the database.
     *
     * @example
     * ```php
     * $subscribers = $I->haveManyUsersInDatabase(5, 'user-{{n}}');
     * $editors = $I->haveManyUsersInDatabase(
     *      5,
     *      'user-{{n}}',
     *      'editor',
     *      ['user_email' => 'user-{{n}}@example.org']
     * );
     * ```
     *
     * @param int    $count      The number of users to insert.
     * @param string $user_login The user login name.
     * @param string $role       The user role.
     * @param array  $overrides  An array of values to override the default ones.
     *
     * @return array An array of user IDs.
     * @see \Codeception\Module\WPDb::haveManyUsersInDatabase()
     */
    public function haveManyUsersInDatabase($count, $user_login, $role = null, $overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveManyUsersInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a user and its meta in the database.
     *
     * @example
     * ```php
     * $userId = $I->haveUserInDatabase('luca', 'editor', ['user_email' => '<EMAIL>']);
     * $subscriberId = $I->haveUserInDatabase('test');
     * $userWithMeta = $I->haveUserInDatabase('luca', 'editor', [
     *     'user_email' => '<EMAIL>'
     *     'meta' => ['a meta_key' => 'a_meta_value']
     * ]);
     * ```
     *
     * @param  string $user_login The user login name.
     * @param  string $role       The user role slug, e.g. "administrator"; defaults to "subscriber".
     * @param  array  $overrides  An associative array of column names and values overridind defaults in the "users"
     *                            and "usermeta" table.
     *
     * @return int The inserted user ID.
     * @see \Codeception\Module\WPDb::haveUserInDatabase()
     */
    public function haveUserInDatabase($user_login, $role = null, $overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveUserInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the prefixed users table name.
     *
     * @example
     * ```php
     * // Given a `wp_` table prefix returns `wp_users`.
     * $usersTable = $I->getUsersTableName();
     * // Given a `wp_` table prefix returns `wp_users`.
     * $I->useBlog(23);
     * $usersTable = $I->getUsersTableName();
     * ```
     *
     * @return string The users table including the table prefix.
     * @deprecated Use `grabUsersTableName`.
     * @see \Codeception\Module\WPDb::getUsersTableName()
     */
    public function getUsersTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('getUsersTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the prefixed users table name.
     *
     * @example
     * ```php
     * // Given a `wp_` table prefix returns `wp_users`.
     * $usersTable = $I->grabUsersTableName();
     * // Given a `wp_` table prefix returns `wp_users`.
     * $I->useBlog(23);
     * $usersTable = $I->grabUsersTableName();
     * ```
     *
     * @return string The users table including the table prefix.
     * @see \Codeception\Module\WPDb::grabUsersTableName()
     */
    public function grabUsersTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabUsersTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sets a user capabilities in the database.
     *
     * @example
     * ```php
     * $blogId = $this->haveBlogInDatabase('test');
     * $editor = $I->haveUserInDatabase('luca', 'editor');
     * $capsIds = $I->haveUserCapabilitiesInDatabase($editor, [$blogId => 'editor']);
     * ```
     *
     * @param int          $userId The ID of the user to set the capabilities of.
     * @param string|array $role Either a role string (e.g. `administrator`) or an associative array of blog IDs/roles
     *                           for a multisite installation (e.g. `[1 => 'administrator`, 2 => 'subscriber']`).
     *
     * @return array An array of inserted `meta_id`.
     * @see \Codeception\Module\WPDb::haveUserCapabilitiesInDatabase()
     */
    public function haveUserCapabilitiesInDatabase($userId, $role) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveUserCapabilitiesInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sets a user meta in the database.
     *
     * @example
     * ```php
     * $userId = $I->haveUserInDatabase('luca', 'editor');
     * $I->haveUserMetaInDatabase($userId, 'karma', 23);
     * ```
     *
     * @param int    $userId The user ID.
     * @param string $meta_key The meta key to set the value for.
     * @param mixed  $meta_value Either a single value or an array of values; objects will be serialized while array of
     *                           values will trigger the insertion of multiple rows.
     *
     * @return array An array of inserted `umeta_id`s.
     * @see \Codeception\Module\WPDb::haveUserMetaInDatabase()
     */
    public function haveUserMetaInDatabase($userId, $meta_key, $meta_value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveUserMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the prefixed users meta table name.
     *
     * @example
     * ```php
     * // Given a `wp_` table prefix returns `wp_usermeta`.
     * $usermetaTable = $I->grabUsermetaTableName();
     * // Given a `wp_` table prefix returns `wp_usermeta`.
     * $I->useBlog(23);
     * $usermetaTable = $I->grabUsermetaTableName();
     * ```
     *
     * @return string The user meta table name.
     * @see \Codeception\Module\WPDb::grabUsermetaTableName()
     */
    public function grabUsermetaTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabUsermetaTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sets the user access level meta in the database for a user.
     *
     * @example
     * ```php
     * $userId = $I->haveUserInDatabase('luca', 'editor');
     * $moreThanAnEditorLessThanAnAdmin = 8;
     * $I->haveUserLevelsInDatabase($userId, $moreThanAnEditorLessThanAnAdmin);
     * ```
     *
     * @param int          $userId The ID of the user to set the level for.
     * @param string|array $role Either a role string (e.g. `administrator`) or an array of blog IDs/roles for a
     *                           multisite installation (e.g. `[1 => 'administrator`, 2 => 'subscriber']`).
     *
     * @return array An array of inserted `meta_id`.
     * @see \Codeception\Module\WPDb::haveUserLevelsInDatabase()
     */
    public function haveUserLevelsInDatabase($userId, $role) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveUserLevelsInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts many terms in the database.
     *
     * @example
     * ```php
     * $terms = $I->haveManyTermsInDatabase(3, 'genre-{{n}}', 'genre');
     * $termIds = array_column($terms, 0);
     * $termTaxonomyIds = array_column($terms, 1);
     * ```
     *
     * @param       int    $count     The number of terms to insert.
     * @param       string $name      The term name template, can include the `{{n}}` placeholder.
     * @param       string $taxonomy  The taxonomy to insert the terms for.
     * @param array        $overrides An associative array of default overrides.
     *
     * @return array An array of arrays containing `term_id` and `term_taxonomy_id` of the inserted terms.
     * @see \Codeception\Module\WPDb::haveManyTermsInDatabase()
     */
    public function haveManyTermsInDatabase($count, $name, $taxonomy, $overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveManyTermsInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a taxonomy taxonomy in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxonomyId) = $I->haveTermInDatabase('fiction', 'genre');
     * $I->seeTermTaxonomyInDatabase(['term_id' => $termId, 'taxonomy' => 'genre']);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeTermTaxonomyInDatabase()
     */
    public function seeTermTaxonomyInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeTermTaxonomyInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a taxonomy taxonomy in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxonomyId) = $I->haveTermInDatabase('fiction', 'genre');
     * $I->seeTermTaxonomyInDatabase(['term_id' => $termId, 'taxonomy' => 'genre']);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeTermTaxonomyInDatabase()
     */
    public function canSeeTermTaxonomyInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeTermTaxonomyInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a term taxonomy is not in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxonomyId) = $I->haveTermInDatabase('fiction', 'genre');
     * $I->dontSeeTermTaxonomyInDatabase(['term_id' => $termId, 'taxonomy' => 'country']);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeTermTaxonomyInDatabase()
     */
    public function dontSeeTermTaxonomyInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeTermTaxonomyInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a term taxonomy is not in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxonomyId) = $I->haveTermInDatabase('fiction', 'genre');
     * $I->dontSeeTermTaxonomyInDatabase(['term_id' => $termId, 'taxonomy' => 'country']);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeTermTaxonomyInDatabase()
     */
    public function cantSeeTermTaxonomyInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeTermTaxonomyInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a term meta in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxonomyId) = $I->haveTermInDatabase('fiction', 'genre');
     * $I->haveTermMetaInDatabase($termId, 'rating', 4);
     * $I->seeTermMetaInDatabase(['term_id' => $termId,'meta_key' => 'rating', 'meta_value' => 4]);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeTermMetaInDatabase()
     */
    public function seeTermMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeTermMetaInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a term meta in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxonomyId) = $I->haveTermInDatabase('fiction', 'genre');
     * $I->haveTermMetaInDatabase($termId, 'rating', 4);
     * $I->seeTermMetaInDatabase(['term_id' => $termId,'meta_key' => 'rating', 'meta_value' => 4]);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeTermMetaInDatabase()
     */
    public function canSeeTermMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeTermMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a term meta is not in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxonomyId) = $I->haveTermInDatabase('fiction', 'genre');
     * $I->haveTermMetaInDatabase($termId, 'rating', 4);
     * $I->dontSeeTermMetaInDatabase(['term_id' => $termId,'meta_key' => 'average_review']);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeTermMetaInDatabase()
     */
    public function dontSeeTermMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeTermMetaInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a term meta is not in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxonomyId) = $I->haveTermInDatabase('fiction', 'genre');
     * $I->haveTermMetaInDatabase($termId, 'rating', 4);
     * $I->dontSeeTermMetaInDatabase(['term_id' => $termId,'meta_key' => 'average_review']);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeTermMetaInDatabase()
     */
    public function cantSeeTermMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeTermMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a table is in the database.
     *
     * @example
     * ```php
     * $options = $I->grabPrefixedTableNameFor('options');
     * $I->seeTableInDatabase($options);
     * ```
     *
     * @param string $table The full table name, including the table prefix.
     * @see \Codeception\Module\WPDb::seeTableInDatabase()
     */
    public function seeTableInDatabase($table) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeTableInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a table is in the database.
     *
     * @example
     * ```php
     * $options = $I->grabPrefixedTableNameFor('options');
     * $I->seeTableInDatabase($options);
     * ```
     *
     * @param string $table The full table name, including the table prefix.
     * @see \Codeception\Module\WPDb::seeTableInDatabase()
     */
    public function canSeeTableInDatabase($table) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeTableInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed `blog_versions` table name.
     *
     * @example
     * ```php
     * // Assuming a `wp_` table prefix it will return `wp_blog_versions`.
     * $blogVersionsTable = $I->grabBlogVersionsTableName();
     * $I->useBlog(23);
     * // Assuming a `wp_` table prefix it will return `wp_blog_versions`.
     * $blogVersionsTable = $I->grabBlogVersionsTableName();
     * ```
     *
     * @return string The blogs versions table name including the table prefix.
     * @see \Codeception\Module\WPDb::grabBlogVersionsTableName()
     */
    public function grabBlogVersionsTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabBlogVersionsTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed `sitemeta` table name.
     *
     * @example
     * ```php
     * // Assuming a `wp_` table prefix it will return `wp_sitemeta`.
     * $blogVersionsTable = $I->grabSiteMetaTableName();
     * $I->useBlog(23);
     * // Assuming a `wp_` table prefix it will return `wp_sitemeta`.
     * $blogVersionsTable = $I->grabSiteMetaTableName();
     * ```
     *
     * @return string The site meta table name including the table prefix.
     * @see \Codeception\Module\WPDb::grabSiteMetaTableName()
     */
    public function grabSiteMetaTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabSiteMetaTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed `signups` table name.
     *
     * @example
     * ```php
     * // Assuming a `wp_` table prefix it will return `wp_signups`.
     * $blogVersionsTable = $I->grabSignupsTableName();
     * $I->useBlog(23);
     * // Assuming a `wp_` table prefix it will return `wp_signups`.
     * $blogVersionsTable = $I->grabSignupsTableName();
     * ```
     *
     * @return string The signups table name including the table prefix.
     * @see \Codeception\Module\WPDb::grabSignupsTableName()
     */
    public function grabSignupsTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabSignupsTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed `registration_log` table name.
     *
     * @example
     * ```php
     * // Assuming a `wp_` table prefix it will return `wp_registration_log`.
     * $blogVersionsTable = $I->grabRegistrationLogTableName();
     * $I->useBlog(23);
     * // Assuming a `wp_` table prefix it will return `wp_registration_log`.
     * $blogVersionsTable = $I->grabRegistrationLogTableName();
     * ```
     *
     * @return string The registration log table name including the table prefix.
     * @see \Codeception\Module\WPDb::grabRegistrationLogTableName()
     */
    public function grabRegistrationLogTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabRegistrationLogTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed `site` table name.
     *
     * @example
     * ```php
     * // Assuming a `wp_` table prefix it will return `wp_site`.
     * $blogVersionsTable = $I->grabSiteTableName();
     * $I->useBlog(23);
     * // Assuming a `wp_` table prefix it will return `wp_site`.
     * $blogVersionsTable = $I->grabSiteTableName();
     * ```
     *
     * @return string The site table name including the table prefix.
     * @see \Codeception\Module\WPDb::grabSiteTableName()
     */
    public function grabSiteTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabSiteTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a blog in the `blogs` table.
     *
     * @example
     * ```php
     * // Search for a blog by `blog_id`.
     * $I->seeBlogInDatabase(['blog_id' => 23]);
     * // Search for all blogs on a path.
     * $I->seeBlogInDatabase(['path' => '/sub-path/']);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeBlogInDatabase()
     */
    public function seeBlogInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeBlogInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a blog in the `blogs` table.
     *
     * @example
     * ```php
     * // Search for a blog by `blog_id`.
     * $I->seeBlogInDatabase(['blog_id' => 23]);
     * // Search for all blogs on a path.
     * $I->seeBlogInDatabase(['path' => '/sub-path/']);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeBlogInDatabase()
     */
    public function canSeeBlogInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeBlogInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the prefixed `blogs` table name.
     *
     * @example
     * ```php
     * // Assuming a `wp_` table prefix it will return `wp_blogs`.
     * $blogVersionsTable = $I->grabBlogsTableName();
     * $I->useBlog(23);
     * // Assuming a `wp_` table prefix it will return `wp_blogs`.
     * $blogVersionsTable = $I->grabBlogsTableName();
     * ```
     *
     * @return string The blogs table name including the table prefix.
     * @see \Codeception\Module\WPDb::grabBlogsTableName()
     */
    public function grabBlogsTableName() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabBlogsTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts many blogs in the database.
     *
     * @example
     * ```php
     * $blogIds = $I->haveManyBlogsInDatabase(3, ['domain' =>'test-{{n}}']);
     * foreach($blogIds as $blogId){
     *      $I->useBlog($blogId);
     *      $I->haveManuPostsInDatabase(3);
     * }
     * ```
     *
     * @param int   $count     The number of blogs to create.
     * @param array $overrides An array of values to override the default ones; `{{n}}` will be replaced by the count.
     * @param bool  $subdomain Whether the new blogs should be created as a subdomain or subfolder.
     *
     * @return array An array of inserted blogs `blog_id`s.
     * @see \Codeception\Module\WPDb::haveManyBlogsInDatabase()
     */
    public function haveManyBlogsInDatabase($count, $overrides = null, $subdomain = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveManyBlogsInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts a blog in the `blogs` table.
     *
     * @example
     * ```php
     * // Create the `test` subdomain blog.
     * $blogId = $I->haveBlogInDatabase('test', ['administrator' => $userId]);
     * // Create the `/test` subfolder blog.
     * $blogId = $I->haveBlogInDatabase('test', ['administrator' => $userId], false);
     * ```
     *
     * @param  string $domainOrPath     The subdomain or the path to the be used for the blog.
     * @param array   $overrides        An array of values to override the defaults.
     * @param bool    $subdomain        Whether the new blog should be created as a subdomain (`true`)
     *                                  or subfolder (`true`)
     *
     * @return int The inserted blog `blog_id`.
     * @see \Codeception\Module\WPDb::haveBlogInDatabase()
     */
    public function haveBlogInDatabase($domainOrPath, $overrides = null, $subdomain = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveBlogInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the site domain inferred from the `url` set in the config.
     *
     * @example
     * ```php
     * $domain = $I->getSiteDomain();
     * // We should be redirected to the HTTPS version when visiting the HTTP version.
     * $I->amOnPage('http://' . $domain);
     * $I->seeCurrentUrlEquals('https://' . $domain);
     * ```
     *
     * @return string The site domain, e.g. `worpdress.localhost` or `localhost:8080`.
     * @see \Codeception\Module\WPDb::getSiteDomain()
     */
    public function getSiteDomain() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('getSiteDomain', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes one ore more blogs frome the database.
     *
     * @example
     * ```php
     * // Remove the blog, all its tables and files.
     * $I->dontHaveBlogInDatabase(['path' => 'test/one']);
     * // Remove the blog entry, not the tables though.
     * $I->dontHaveBlogInDatabase(['blog_id' => $blogId]);
     * // Remove multiple blogs.
     * $I->dontHaveBlogInDatabase(['domain' => 'test']);
     * ```
     *
     * @param array $criteria An array of search criteria to find the blog rows in the blogs table.
     * @param bool $removeTables Remove the blog tables.
     * @param bool $removeUploads Remove the blog uploads; requires the `WPFilesystem` module.
     * @throws \Exception
     * @see \Codeception\Module\WPDb::dontHaveBlogInDatabase()
     */
    public function dontHaveBlogInDatabase($criteria, $removeTables = null, $removeUploads = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveBlogInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns a list of tables for a blog ID.
     *
     * @example
     * ```php
     * $blogId = $I->haveBlogInDatabase('test');
     * $tables = $I->grabBlogTableNames($blogId);
     * $options = array_filter($tables, function($tableName){
     *      return str_pos($tableName, 'options') !== false;
     * });
     * ```
     *
     * @param int $blogId The ID of the blog to fetch the tables for.
     *
     * @return array An array of tables for the blog, it does not include the tables common to all blogs; an empty array
     *               if the tables for the blog do not exist.
     *
     * @throws \Exception If there is any error while preparing the query.
     * @see \Codeception\Module\WPDb::grabBlogTableNames()
     */
    public function grabBlogTableNames($blogId) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabBlogTableNames', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a table from the database.
     * The case where a table does not exist is handled without raising an error.
     *
     * @example
     * ```php
     * $ordersTable = $I->grabPrefixedTableNameFor('orders');
     * $I->dontHaveTableInDatabase($ordersTable);
     * ```
     *
     * @param string $fullTableName The full table name, including the table prefix.
     *
     * @throws \Exception If there is an error while dropping the table.
     * @see \Codeception\Module\WPDb::dontHaveTableInDatabase()
     */
    public function dontHaveTableInDatabase($fullTableName) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveTableInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a row is not present in the `blogs` table.
     *
     * @example
     * ```php
     * $I->haveManyBlogsInDatabase(2, ['path' => 'test-{{n}}'], false)
     * $I->dontSeeBlogInDatabase(['path' => '/test-3/'])
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeBlogInDatabase()
     */
    public function dontSeeBlogInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeBlogInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a row is not present in the `blogs` table.
     *
     * @example
     * ```php
     * $I->haveManyBlogsInDatabase(2, ['path' => 'test-{{n}}'], false)
     * $I->dontSeeBlogInDatabase(['path' => '/test-3/'])
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeBlogInDatabase()
     */
    public function cantSeeBlogInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeBlogInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sets the current theme options.
     *
     * @example
     * ```php
     * $I->useTheme('twentyseventeen');
     * $I->useTheme('child-of-twentyseventeen', 'twentyseventeen');
     * $I->useTheme('acme', 'acme', 'Acme Theme');
     * ```
     *
     * @param string      $stylesheet The theme stylesheet slug, e.g. `twentysixteen`.
     * @param string|null $template   The theme template slug, e.g. `twentysixteen`, defaults to `$stylesheet`.
     *
     * @param string|null $themeName The theme name, e.g. `Acme`, defaults to the "title" version of
     *                                     `$stylesheet`.
     * @see \Codeception\Module\WPDb::useTheme()
     */
    public function useTheme($stylesheet, $template = null, $themeName = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('useTheme', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Creates and adds a menu to a theme location in the database.
     *
     * @example
     * ```php
     * list($termId, $termTaxId) = $I->haveMenuInDatabase('test', 'sidebar');
     * ```
     *
     * @param string $slug      The menu slug.
     * @param string $location  The theme menu location the menu will be assigned to.
     * @param array  $overrides An array of values to override the defaults.
     *
     * @return array An array containing the created menu `term_id` and `term_taxonomy_id`.
     * @see \Codeception\Module\WPDb::haveMenuInDatabase()
     */
    public function haveMenuInDatabase($slug, $location, $overrides = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveMenuInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Adds a menu element to a menu for the current theme.
     *
     * @example
     * ```php
     * $I->haveMenuInDatabase('test', 'sidebar');
     * $I->haveMenuItemInDatabase('test', 'Test one', 0);
     * $I->haveMenuItemInDatabase('test', 'Test two', 1);
     * ```
     *
     * @param string $menuSlug The menu slug the item should be added to.
     * @param string $title The menu item title.
     * @param int|null $menuOrder An optional menu order, `1` based.
     * @param array $meta An associative array that will be prefixed with `_menu_item_` for the item post meta.
     *
     * @return int The menu item post `ID`
     * @see \Codeception\Module\WPDb::haveMenuItemInDatabase()
     */
    public function haveMenuItemInDatabase($menuSlug, $title, $menuOrder = null, $meta = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveMenuItemInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for a term relationship in the database.
     *
     * @example
     * ```php
     * $postId = $I->havePostInDatabase(['tax_input' => ['category' => 'one']]);
     * $I->seeTermRelationshipInDatabase(['object_id' => $postId, 'term_taxonomy_id' => $oneTermTaxId]);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeTermRelationshipInDatabase()
     */
    public function seeTermRelationshipInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeTermRelationshipInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for a term relationship in the database.
     *
     * @example
     * ```php
     * $postId = $I->havePostInDatabase(['tax_input' => ['category' => 'one']]);
     * $I->seeTermRelationshipInDatabase(['object_id' => $postId, 'term_taxonomy_id' => $oneTermTaxId]);
     * ```
     *
     * @param array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeTermRelationshipInDatabase()
     */
    public function canSeeTermRelationshipInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeTermRelationshipInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Creates the database entries representing an attachment and moves the attachment file to the right location.
     *
     * @example
     * ```php
     * $file = codecept_data_dir('images/test.png');
     * $attachmentId = $I->haveAttachmentInDatabase($file);
     * $image = codecept_data_dir('images/test-2.png');
     * $lastWeekAttachment = $I->haveAttachmentInDatabase($image, '-1 week');
     * ```
     *
     * Requires the WPFilesystem module.
     *
     * @param string $file The absolute path to the attachment file.
     * @param string|int $date Either a string supported by the `strtotime` function or a UNIX timestamp that
     *                               should be used to build the "year/time" uploads sub-folder structure.
     * @param array $overrides An associative array of values overriding the default ones.
     * @param array $imageSizes An associative array in the format [ <size> => [<width>,<height>]] to override the
     *                               image sizes created by default.
     *
     * @return int The post ID of the inserted attachment.
     *
     * @throws ModuleException If the WPFilesystem module is not loaded in the suite or the file to attach is not
     *                         readable
     * @throws \Gumlet\ImageResizeException If the image resize operation fails while trying to create the image sizes.
     * @throws ModuleRequireException If the `WPFileSystem` module is not loaded in the suite.
     * @see \Codeception\Module\WPDb::haveAttachmentInDatabase()
     */
    public function haveAttachmentInDatabase($file, $date = null, $overrides = null, $imageSizes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveAttachmentInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the current site URL as specified in the module configuration.
     *
     * @example
     * ```php
     * $shopPath = $I->grabSiteUrl('/shop');
     * ```
     *
     * @param string $path A path that should be appended to the site URL.
     *
     * @return string The current site URL
     * @see \Codeception\Module\WPDb::grabSiteUrl()
     */
    public function grabSiteUrl($path = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabSiteUrl', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks for an attachment in the database.
     *
     * @example
     * ```php
     * $url = 'https://example.org/images/foo.png';
     * $I->seeAttachmentInDatabase(['guid' => $url]);
     * ```
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeAttachmentInDatabase()
     */
    public function seeAttachmentInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeAttachmentInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks for an attachment in the database.
     *
     * @example
     * ```php
     * $url = 'https://example.org/images/foo.png';
     * $I->seeAttachmentInDatabase(['guid' => $url]);
     * ```
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::seeAttachmentInDatabase()
     */
    public function canSeeAttachmentInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeAttachmentInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that an attachment is not in the database.
     *
     * @example
     * ```php
     * $url = 'https://example.org/images/foo.png';
     * $I->dontSeeAttachmentInDatabase(['guid' => $url]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeAttachmentInDatabase()
     */
    public function dontSeeAttachmentInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeAttachmentInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that an attachment is not in the database.
     *
     * @example
     * ```php
     * $url = 'https://example.org/images/foo.png';
     * $I->dontSeeAttachmentInDatabase(['guid' => $url]);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontSeeAttachmentInDatabase()
     */
    public function cantSeeAttachmentInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeAttachmentInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes an attachment from the posts table.
     *
     * @example
     * ```
     * $postmeta = $I->grabpostmetatablename();
     * $thumbnailId = $I->grabFromDatabase($postmeta, 'meta_value', [
     *      'post_id' => $id,
     *      'meta_key'=>'thumbnail_id'
     * ]);
     * // Remove only the database entry (including postmeta) but not the files.
     * $I->dontHaveAttachmentInDatabase($thumbnailId);
     * // Remove the database entry (including postmeta) and the files.
     * $I->dontHaveAttachmentInDatabase($thumbnailId, true, true);
     * ```
     *
     * @param  array $criteria    An array of search criteria to find the attachment post in the posts table.
     * @param bool   $purgeMeta   If set to `true` then the meta for the attachment will be purged too.
     * @param bool   $removeFiles Remove all files too, requires the `WPFilesystem` module to be loaded in the suite.
     *
     * @throws ModuleException If the WPFilesystem module is not loaded in the suite
     *                                                and the `$removeFiles` argument is `true`.
     * @see \Codeception\Module\WPDb::dontHaveAttachmentInDatabase()
     */
    public function dontHaveAttachmentInDatabase($criteria, $purgeMeta = null, $removeFiles = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveAttachmentInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes all the files attached with an attachment post, it will not remove the database entries.
     * Requires the `WPFilesystem` module to be loaded in the suite.
     *
     * @example
     * ```php
     * $posts = $I->grabPostsTableName();
     * $attachmentIds = $I->grabColumnFromDatabase($posts, 'ID', ['post_type' => 'attachment']);
     * // This will only remove the files, not the database entries.
     * $I->dontHaveAttachmentFilesInDatabase($attachmentIds);
     * ```
     *
     * @param array|int $attachmentIds An attachment post ID or an array of attachment post IDs.
     *
     * @throws ModuleRequireException If the `WPFilesystem` module is not loaded in the suite.
     * @see \Codeception\Module\WPDb::dontHaveAttachmentFilesInDatabase()
     */
    public function dontHaveAttachmentFilesInDatabase($attachmentIds) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveAttachmentFilesInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the path, as stored in the database, of an attachment `_wp_attached_file` meta.
     * The attached file is, usually, an attachment origal file.
     *
     * @example
     * ```php
     * $file = $I->grabAttachmentAttachedFile($attachmentId);
     * $fileInfo = new SplFileInfo($file);
     * $I->assertEquals('jpg', $fileInfo->getExtension());
     * ```
     *
     * @param int $attachmentPostId The attachment post ID.
     *
     * @return string The attachment attached file path or an empt string if not set.
     * @see \Codeception\Module\WPDb::grabAttachmentAttachedFile()
     */
    public function grabAttachmentAttachedFile($attachmentPostId) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabAttachmentAttachedFile', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the metadata array for an attachment post.
     * This is the value of the `_wp_attachment_metadata` meta.
     *
     * @example
     * ```php
     * $metadata = $I->grabAttachmentMetadata($attachmentId);
     * $I->assertEquals(['thumbnail', 'medium', 'medium_large'], array_keys($metadata['sizes']);
     * ```
     *
     * @param int $attachmentPostId The attachment post ID.
     *
     * @return array The unserialized contents of the attachment `_wp_attachment_metadata` meta or an empty array.
     * @see \Codeception\Module\WPDb::grabAttachmentMetadata()
     */
    public function grabAttachmentMetadata($attachmentPostId) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabAttachmentMetadata', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes an entry from the posts table.
     *
     * @example
     * ```php
     * $posts = $I->haveManyPostsInDatabase(3, ['post_title' => 'Test {{n}}']);
     * $I->dontHavePostInDatabase(['post_title' => 'Test 2']);
     * ```
     *
     * @param  array $criteria  An array of search criteria.
     * @param bool   $purgeMeta If set to `true` then the meta for the post will be purged too.
     * @see \Codeception\Module\WPDb::dontHavePostInDatabase()
     */
    public function dontHavePostInDatabase($criteria, $purgeMeta = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHavePostInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes an entry from the postmeta table.
     *
     * @example
     * ```php
     * $postId = $I->havePostInDatabase(['meta_input' => ['rating' => 23]]);
     * $I->dontHavePostMetaInDatabase(['post_id' => $postId, 'meta_key' => 'rating']);
     * ```
     *
     * @param  array $criteria An array of search criteria.
     * @see \Codeception\Module\WPDb::dontHavePostMetaInDatabase()
     */
    public function dontHavePostMetaInDatabase($criteria) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHavePostMetaInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a user(s) from the database using the user email address.
     *
     * @example
     * ```php
     * $luca = $I->haveUserInDatabase('luca', 'editor', ['user_email' => '<EMAIL>']);
     * $I->dontHaveUserInDatabaseWithEmail('<EMAIL>');
     * ```
     *
     * @param string $userEmail The email of the user to remove.
     * @param bool   $purgeMeta Whether the user meta should be purged alongside the user or not.
     *
     * @return array An array of the deleted user(s) ID(s)
     * @see \Codeception\Module\WPDb::dontHaveUserInDatabaseWithEmail()
     */
    public function dontHaveUserInDatabaseWithEmail($userEmail, $purgeMeta = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveUserInDatabaseWithEmail', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the table prefix, namespaced for secondary blogs if selected.
     *
     * @example
     * ```php
     * // Assuming a table prefix of `wp_` it will return `wp_`;
     * $tablePrefix = $I->grabTablePrefix();
     * $I->useBlog(23);
     * // Assuming a table prefix of `wp_` it will return `wp_23_`;
     * $tablePrefix = $I->grabTablePrefix();
     * ```
     *
     * @return string The blog aware table prefix.
     * @see \Codeception\Module\WPDb::grabTablePrefix()
     */
    public function grabTablePrefix() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTablePrefix', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Removes a user from the database.
     *
     * @example
     * ```php
     * $bob = $I->haveUserInDatabase('bob');
     * $alice = $I->haveUserInDatabase('alice');
     * // Remove Bob's user and meta.
     * $I->dontHaveUserInDatabase('bob');
     * // Remove Alice's user but not meta.
     * $I->dontHaveUserInDatabase($alice);
     * ```
     *
     * @param int|string $userIdOrLogin The user ID or login name.
     * @param bool       $purgeMeta Whether the user meta should be purged alongside the user or not.
     * @see \Codeception\Module\WPDb::dontHaveUserInDatabase()
     */
    public function dontHaveUserInDatabase($userIdOrLogin, $purgeMeta = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontHaveUserInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the a user ID from the database using the user login.
     *
     * @example
     * ```php
     * $userId = $I->grabUserIdFromDatabase('luca');
     * ```
     *
     * @param string $userLogin The user login name.
     *
     * @return int The user ID
     * @see \Codeception\Module\WPDb::grabUserIdFromDatabase()
     */
    public function grabUserIdFromDatabase($userLogin) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabUserIdFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Gets the value of one or more post meta values from the database.
     *
     * @example
     * ```php
     * $thumbnail_id = $I->grabPostMetaFromDatabase($postId, '_thumbnail_id', true);
     * ```
     *
     * @param int    $postId  The post ID.
     * @param string $metaKey The key of the meta to retrieve.
     * @param bool   $single  Whether to return a single meta value or an arrya of all available meta values.
     *
     * @return mixed|array Either a single meta value or an array of all the available meta values.
     * @see \Codeception\Module\WPDb::grabPostMetaFromDatabase()
     */
    public function grabPostMetaFromDatabase($postId, $metaKey, $single = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabPostMetaFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the full name of a table for a blog from a multisite installation database.
     *
     * @example
     * ```php
     * $blogOptionTable = $I->grabBlogTableName($blogId, 'option');
     * ```
     *
     * @param int    $blogId The blog ID.
     * @param string $table  The table name, without table prefix.
     *
     * @return string The full blog table name, including the table prefix or an empty string
     *                if the table does not exist.
     *
     * @throws ModuleException If no tables are found for the blog.
     * @see \Codeception\Module\WPDb::grabBlogTableName()
     */
    public function grabBlogTableName($blogId, $table) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabBlogTableName', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a table is not in the database.
     *
     * @example
     * ```php
     * $options = $I->grabPrefixedTableNameFor('options');
     * $I->dontHaveTableInDatabase($options)
     * $I->dontSeeTableInDatabase($options);
     * ```
     *
     * @param string $table The full table name, including the table prefix.
     * @see \Codeception\Module\WPDb::dontSeeTableInDatabase()
     */
    public function dontSeeTableInDatabase($table) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeTableInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a table is not in the database.
     *
     * @example
     * ```php
     * $options = $I->grabPrefixedTableNameFor('options');
     * $I->dontHaveTableInDatabase($options)
     * $I->dontSeeTableInDatabase($options);
     * ```
     *
     * @param string $table The full table name, including the table prefix.
     * @see \Codeception\Module\WPDb::dontSeeTableInDatabase()
     */
    public function cantSeeTableInDatabase($table) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeTableInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the table prefix for a blog.
     *
     * @example
     * ```php
     * $blogId = $I->haveBlogInDatabase('test');
     * $blogTablePrefix = $I->getBlogTablePrefix($blogId);
     * $blogOrders = $I->blogTablePrefix . 'orders';
     * ```
     *
     * @param int $blogId The blog ID.
     *
     * @return string The table prefix for the blog.
     * @see \Codeception\Module\WPDb::grabBlogTablePrefix()
     */
    public function grabBlogTablePrefix($blogId) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabBlogTablePrefix', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns a blog domain given its ID.
     *
     * @example
     * ```php
     * $blogIds = $I->haveManyBlogsInDatabase(3);
     * $domains = array_map(function($blogId){
     *      return $I->grabBlogDomain($blogId);
     * }, $blogIds);
     * ```
     *
     * @param int $blogId The blog ID.
     *
     * @return string The blog domain.
     * @see \Codeception\Module\WPDb::grabBlogDomain()
     */
    public function grabBlogDomain($blogId) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabBlogDomain', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Grabs a blog domain from the blogs table.
     *
     * @example
     * ```php
     * $blogId = $I->haveBlogInDatabase('test');
     * $path = $I->grabBlogDomain($blogId);
     * $I->amOnSubdomain($path);
     * $I->amOnPage('/');
     * ```
     *
     * @param int $blogId The blog ID.
     *
     * @return string The blog domain, if set in the database.
     * @see \Codeception\Module\WPDb::grabBlogPath()
     */
    public function grabBlogPath($blogId) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabBlogPath', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a post to term relation does not exist in the database.
     *
     * The method will check the "term_relationships" table.
     *
     * @example
     * ```php
     * $fiction = $I->haveTermInDatabase('fiction', 'genre');
     * $nonFiction = $I->haveTermInDatabase('non-fiction', 'genre');
     * $postId = $I->havePostInDatabase(['tax_input' => ['genre' => ['fiction']]]);
     * $I->dontSeePostWithTermInDatabase($postId, $nonFiction['term_taxonomy_id], );
     * ```
     *
     * @param  int          $post_id           The post ID.
     * @param  int          $term_taxonomy_id  The term `term_id` or `term_taxonomy_id`; if the `$taxonomy` argument is
     *                                         passed this parameter will be interpreted as a `term_id`, else as a
     *                                         `term_taxonomy_id`.
     * @param  int|null     $term_order        The order the term applies to the post, defaults to `null` to not use
     *                                         the
     *                                         term order.
     * @param  string|null  $taxonomy          The taxonomy the `term_id` is for; if passed this parameter will be used
     *                                         to build a `taxonomy_term_id` from the `term_id`.
     * @throws ModuleException If a `term_id` is specified but it cannot be matched to the `taxonomy`.
     * @see \Codeception\Module\WPDb::dontSeePostWithTermInDatabase()
     */
    public function dontSeePostWithTermInDatabase($post_id, $term_taxonomy_id, $term_order = null, $taxonomy = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeePostWithTermInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a post to term relation does not exist in the database.
     *
     * The method will check the "term_relationships" table.
     *
     * @example
     * ```php
     * $fiction = $I->haveTermInDatabase('fiction', 'genre');
     * $nonFiction = $I->haveTermInDatabase('non-fiction', 'genre');
     * $postId = $I->havePostInDatabase(['tax_input' => ['genre' => ['fiction']]]);
     * $I->dontSeePostWithTermInDatabase($postId, $nonFiction['term_taxonomy_id], );
     * ```
     *
     * @param  int          $post_id           The post ID.
     * @param  int          $term_taxonomy_id  The term `term_id` or `term_taxonomy_id`; if the `$taxonomy` argument is
     *                                         passed this parameter will be interpreted as a `term_id`, else as a
     *                                         `term_taxonomy_id`.
     * @param  int|null     $term_order        The order the term applies to the post, defaults to `null` to not use
     *                                         the
     *                                         term order.
     * @param  string|null  $taxonomy          The taxonomy the `term_id` is for; if passed this parameter will be used
     *                                         to build a `taxonomy_term_id` from the `term_id`.
     * @throws ModuleException If a `term_id` is specified but it cannot be matched to the `taxonomy`.
     * @see \Codeception\Module\WPDb::dontSeePostWithTermInDatabase()
     */
    public function cantSeePostWithTermInDatabase($post_id, $term_taxonomy_id, $term_order = null, $taxonomy = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeePostWithTermInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Make sure you are connected to the right database.
     *
     * ```php
     * <?php
     * $I->seeNumRecords(2, 'users');   //executed on default database
     * $I->amConnectedToDatabase('db_books');
     * $I->seeNumRecords(30, 'books');  //executed on db_books database
     * //All the next queries will be on db_books
     * ```
     * @param $databaseKey
     * @throws ModuleConfigException
     * @see \Codeception\Module\Db::amConnectedToDatabase()
     */
    public function amConnectedToDatabase($databaseKey) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amConnectedToDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Can be used with a callback if you don't want to change the current database in your test.
     *
     * ```php
     * <?php
     * $I->seeNumRecords(2, 'users');   //executed on default database
     * $I->performInDatabase('db_books', function($I) {
     *     $I->seeNumRecords(30, 'books');  //executed on db_books database
     * });
     * $I->seeNumRecords(2, 'users');  //executed on default database
     * ```
     * List of actions can be pragmatically built using `Codeception\Util\ActionSequence`:
     *
     * ```php
     * <?php
     * $I->performInDatabase('db_books', ActionSequence::build()
     *     ->seeNumRecords(30, 'books')
     * );
     * ```
     * Alternatively an array can be used:
     *
     * ```php
     * $I->performInDatabase('db_books', ['seeNumRecords' => [30, 'books']]);
     * ```
     *
     * Choose the syntax you like the most and use it,
     *
     * Actions executed from array or ActionSequence will print debug output for actions, and adds an action name to
     * exception on failure.
     *
     * @param $databaseKey
     * @param \Codeception\Util\ActionSequence|array|callable $actions
     * @throws ModuleConfigException
     * @see \Codeception\Module\Db::performInDatabase()
     */
    public function performInDatabase($databaseKey, $actions) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('performInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Inserts an SQL record into a database. This record will be erased after the test.
     *
     * ```php
     * <?php
     * $I->haveInDatabase('users', array('name' => 'miles', 'email' => '<EMAIL>'));
     * ?>
     * ```
     *
     * @param string $table
     * @param array $data
     *
     * @return integer $id
     * @see \Codeception\Module\Db::haveInDatabase()
     */
    public function haveInDatabase($table, $data) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('haveInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Asserts that a row with the given column values exists.
     * Provide table name and column values.
     *
     * ```php
     * <?php
     * $I->seeInDatabase('users', ['name' => 'Davert', 'email' => '<EMAIL>']);
     * ```
     * Fails if no such user found.
     *
     * Comparison expressions can be used as well:
     *
     * ```php
     * <?php
     * $I->seeInDatabase('posts', ['num_comments >=' => '0']);
     * $I->seeInDatabase('users', ['email like' => '<EMAIL>']);
     * ```
     *
     * Supported operators: `<`, `>`, `>=`, `<=`, `!=`, `like`.
     *
     *
     * @param string $table
     * @param array $criteria
     * @see \Codeception\Module\Db::seeInDatabase()
     */
    public function seeInDatabase($table, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Asserts that a row with the given column values exists.
     * Provide table name and column values.
     *
     * ```php
     * <?php
     * $I->seeInDatabase('users', ['name' => 'Davert', 'email' => '<EMAIL>']);
     * ```
     * Fails if no such user found.
     *
     * Comparison expressions can be used as well:
     *
     * ```php
     * <?php
     * $I->seeInDatabase('posts', ['num_comments >=' => '0']);
     * $I->seeInDatabase('users', ['email like' => '<EMAIL>']);
     * ```
     *
     * Supported operators: `<`, `>`, `>=`, `<=`, `!=`, `like`.
     *
     *
     * @param string $table
     * @param array $criteria
     * @see \Codeception\Module\Db::seeInDatabase()
     */
    public function canSeeInDatabase($table, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Asserts that the given number of records were found in the database.
     *
     * ```php
     * <?php
     * $I->seeNumRecords(1, 'users', ['name' => 'davert'])
     * ?>
     * ```
     *
     * @param int $expectedNumber Expected number
     * @param string $table Table name
     * @param array $criteria Search criteria [Optional]
     * @see \Codeception\Module\Db::seeNumRecords()
     */
    public function seeNumRecords($expectedNumber, $table, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeNumRecords', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Asserts that the given number of records were found in the database.
     *
     * ```php
     * <?php
     * $I->seeNumRecords(1, 'users', ['name' => 'davert'])
     * ?>
     * ```
     *
     * @param int $expectedNumber Expected number
     * @param string $table Table name
     * @param array $criteria Search criteria [Optional]
     * @see \Codeception\Module\Db::seeNumRecords()
     */
    public function canSeeNumRecords($expectedNumber, $table, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeNumRecords', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Effect is opposite to ->seeInDatabase
     *
     * Asserts that there is no record with the given column values in a database.
     * Provide table name and column values.
     *
     * ``` php
     * <?php
     * $I->dontSeeInDatabase('users', ['name' => 'Davert', 'email' => '<EMAIL>']);
     * ```
     * Fails if such user was found.
     *
     * Comparison expressions can be used as well:
     *
     * ```php
     * <?php
     * $I->dontSeeInDatabase('posts', ['num_comments >=' => '0']);
     * $I->dontSeeInDatabase('users', ['email like' => 'miles%']);
     * ```
     *
     * Supported operators: `<`, `>`, `>=`, `<=`, `!=`, `like`.
     *
     * @param string $table
     * @param array $criteria
     * @see \Codeception\Module\Db::dontSeeInDatabase()
     */
    public function dontSeeInDatabase($table, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeInDatabase', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Effect is opposite to ->seeInDatabase
     *
     * Asserts that there is no record with the given column values in a database.
     * Provide table name and column values.
     *
     * ``` php
     * <?php
     * $I->dontSeeInDatabase('users', ['name' => 'Davert', 'email' => '<EMAIL>']);
     * ```
     * Fails if such user was found.
     *
     * Comparison expressions can be used as well:
     *
     * ```php
     * <?php
     * $I->dontSeeInDatabase('posts', ['num_comments >=' => '0']);
     * $I->dontSeeInDatabase('users', ['email like' => 'miles%']);
     * ```
     *
     * Supported operators: `<`, `>`, `>=`, `<=`, `!=`, `like`.
     *
     * @param string $table
     * @param array $criteria
     * @see \Codeception\Module\Db::dontSeeInDatabase()
     */
    public function cantSeeInDatabase($table, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Fetches all values from the column in database.
     * Provide table name, desired column and criteria.
     *
     * ``` php
     * <?php
     * $mails = $I->grabColumnFromDatabase('users', 'email', array('name' => 'RebOOter'));
     * ```
     *
     * @param string $table
     * @param string $column
     * @param array $criteria
     *
     * @return array
     * @see \Codeception\Module\Db::grabColumnFromDatabase()
     */
    public function grabColumnFromDatabase($table, $column, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabColumnFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Fetches a single column value from a database.
     * Provide table name, desired column and criteria.
     *
     * ``` php
     * <?php
     * $mail = $I->grabFromDatabase('users', 'email', array('name' => 'Davert'));
     * ```
     * Comparison expressions can be used as well:
     *
     * ```php
     * <?php
     * $post = $I->grabFromDatabase('posts', ['num_comments >=' => 100]);
     * $user = $I->grabFromDatabase('users', ['email like' => 'miles%']);
     * ```
     *
     * Supported operators: `<`, `>`, `>=`, `<=`, `!=`, `like`.
     *
     * @param string $table
     * @param string $column
     * @param array $criteria
     *
     * @return mixed Returns a single column value or false
     * @see \Codeception\Module\Db::grabFromDatabase()
     */
    public function grabFromDatabase($table, $column, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabFromDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns the number of rows in a database
     *
     * @param string $table    Table name
     * @param array  $criteria Search criteria [Optional]
     *
     * @return int
     * @see \Codeception\Module\Db::grabNumRecords()
     */
    public function grabNumRecords($table, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabNumRecords', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Update an SQL record into a database.
     *
     * ```php
     * <?php
     * $I->updateInDatabase('users', array('isAdmin' => true), array('email' => '<EMAIL>'));
     * ?>
     * ```
     *
     * @param string $table
     * @param array $data
     * @param array $criteria
     * @see \Codeception\Module\Db::updateInDatabase()
     */
    public function updateInDatabase($table, $data, $criteria = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('updateInDatabase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns all the cookies whose name matches a regex pattern.
     *
     * @example
     * ```php
     * $I->loginAs('customer','password');
     * $I->amOnPage('/shop');
     * $cartCookies = $I->grabCookiesWithPattern("#^shop_cart\\.*#");
     * ```
     *
     * @param string $cookiePattern The regular expression pattern to use for the matching.
     *
     * @return array|null An array of cookies matching the pattern.
     * @see \Codeception\Module\WPBrowser::grabCookiesWithPattern()
     */
    public function grabCookiesWithPattern($cookiePattern) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabCookiesWithPattern', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * In the plugin administration screen activates a plugin clicking the "Activate" link.
     *
     * The method will **not** handle authentication to the admin area.
     *
     * @example
     * ```php
     * // Activate a plugin.
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->activatePlugin('hello-dolly');
     * // Activate a list of plugins.
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->activatePlugin(['hello-dolly','another-plugin']);
     * ```
     *
     * @param  string|array $pluginSlug The plugin slug, like "hello-dolly" or a list of plugin slugs.
     * @see \Codeception\Module\WPBrowser::activatePlugin()
     */
    public function activatePlugin($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('activatePlugin', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * In the plugin administration screen deactivate a plugin clicking the "Deactivate" link.
     *
     * The method will **not** handle authentication and navigation to the plugins administration page.
     *
     * @example
     * ```php
     * // Deactivate one plugin.
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->deactivatePlugin('hello-dolly');
     * // Deactivate a list of plugins.
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->deactivatePlugin(['hello-dolly', 'my-plugin']);
     * ```
     *
     * @param  string|array $pluginSlug The plugin slug, like "hello-dolly", or a list of plugin slugs.
     * @see \Codeception\Module\WPBrowser::deactivatePlugin()
     */
    public function deactivatePlugin($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('deactivatePlugin', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Alias to `haveHttpHeader`
     *
     * @param $name
     * @param $value
     * @see \Codeception\Module\PhpBrowser::setHeader()
     */
    public function setHeader($name, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('setHeader', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Open web page at the given absolute URL and sets its hostname as the base host.
     *
     * ``` php
     * <?php
     * $I->amOnUrl('http://codeception.com');
     * $I->amOnPage('/quickstart'); // moves to http://codeception.com/quickstart
     * ?>
     * ```
     * @see \Codeception\Module\PhpBrowser::amOnUrl()
     */
    public function amOnUrl($url) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amOnUrl', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Changes the subdomain for the 'url' configuration parameter.
     * Does not open a page; use `amOnPage` for that.
     *
     * ``` php
     * <?php
     * // If config is: 'http://mysite.com'
     * // or config is: 'http://www.mysite.com'
     * // or config is: 'http://company.mysite.com'
     *
     * $I->amOnSubdomain('user');
     * $I->amOnPage('/');
     * // moves to http://user.mysite.com/
     * ?>
     * ```
     *
     * @param $subdomain
     *
     * @return mixed
     * @see \Codeception\Module\PhpBrowser::amOnSubdomain()
     */
    public function amOnSubdomain($subdomain) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amOnSubdomain', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Low-level API method.
     * If Codeception commands are not enough, use [Guzzle HTTP Client](http://guzzlephp.org/) methods directly
     *
     * Example:
     *
     * ``` php
     * <?php
     * $I->executeInGuzzle(function (\GuzzleHttp\Client $client) {
     *      $client->get('/get', ['query' => ['foo' => 'bar']]);
     * });
     * ?>
     * ```
     *
     * It is not recommended to use this command on a regular basis.
     * If Codeception lacks important Guzzle Client methods, implement them and submit patches.
     *
     * @param callable $function
     * @see \Codeception\Module\PhpBrowser::executeInGuzzle()
     */
    public function executeInGuzzle($function) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('executeInGuzzle', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Opens the page for the given relative URI.
     *
     * ``` php
     * <?php
     * // opens front page
     * $I->amOnPage('/');
     * // opens /register page
     * $I->amOnPage('/register');
     * ```
     *
     * @param string $page
     * @see \Codeception\Lib\InnerBrowser::amOnPage()
     */
    public function amOnPage($page) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amOnPage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Perform a click on a link or a button, given by a locator.
     * If a fuzzy locator is given, the page will be searched for a button, link, or image matching the locator string.
     * For buttons, the "value" attribute, "name" attribute, and inner text are searched.
     * For links, the link text is searched.
     * For images, the "alt" attribute and inner text of any parent links are searched.
     *
     * The second parameter is a context (CSS or XPath locator) to narrow the search.
     *
     * Note that if the locator matches a button of type `submit`, the form will be submitted.
     *
     * ``` php
     * <?php
     * // simple link
     * $I->click('Logout');
     * // button of form
     * $I->click('Submit');
     * // CSS button
     * $I->click('#form input[type=submit]');
     * // XPath
     * $I->click('//form/*[@type="submit"]');
     * // link in context
     * $I->click('Logout', '#nav');
     * // using strict locator
     * $I->click(['link' => 'Login']);
     * ?>
     * ```
     *
     * @param $link
     * @param $context
     * @see \Codeception\Lib\InnerBrowser::click()
     */
    public function click($link, $context = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('click', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current page contains the given string (case insensitive).
     *
     * You can specify a specific HTML element (via CSS or XPath) as the second
     * parameter to only search within that element.
     *
     * ``` php
     * <?php
     * $I->see('Logout');                        // I can suppose user is logged in
     * $I->see('Sign Up', 'h1');                 // I can suppose it's a signup page
     * $I->see('Sign Up', '//body/h1');          // with XPath
     * $I->see('Sign Up', ['css' => 'body h1']); // with strict CSS locator
     * ```
     *
     * Note that the search is done after stripping all HTML tags from the body,
     * so `$I->see('strong')` will return true for strings like:
     *
     *   - `<p>I am Stronger than thou</p>`
     *   - `<script>document.createElement('strong');</script>`
     *
     * But will *not* be true for strings like:
     *
     *   - `<strong>Home</strong>`
     *   - `<div class="strong">Home</strong>`
     *   - `<!-- strong -->`
     *
     * For checking the raw source code, use `seeInSource()`.
     *
     * @param string $text
     * @param array|string $selector optional
     * @see \Codeception\Lib\InnerBrowser::see()
     */
    public function see($text, $selector = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('see', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current page contains the given string (case insensitive).
     *
     * You can specify a specific HTML element (via CSS or XPath) as the second
     * parameter to only search within that element.
     *
     * ``` php
     * <?php
     * $I->see('Logout');                        // I can suppose user is logged in
     * $I->see('Sign Up', 'h1');                 // I can suppose it's a signup page
     * $I->see('Sign Up', '//body/h1');          // with XPath
     * $I->see('Sign Up', ['css' => 'body h1']); // with strict CSS locator
     * ```
     *
     * Note that the search is done after stripping all HTML tags from the body,
     * so `$I->see('strong')` will return true for strings like:
     *
     *   - `<p>I am Stronger than thou</p>`
     *   - `<script>document.createElement('strong');</script>`
     *
     * But will *not* be true for strings like:
     *
     *   - `<strong>Home</strong>`
     *   - `<div class="strong">Home</strong>`
     *   - `<!-- strong -->`
     *
     * For checking the raw source code, use `seeInSource()`.
     *
     * @param string $text
     * @param array|string $selector optional
     * @see \Codeception\Lib\InnerBrowser::see()
     */
    public function canSee($text, $selector = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('see', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current page doesn't contain the text specified (case insensitive).
     * Give a locator as the second parameter to match a specific region.
     *
     * ```php
     * <?php
     * $I->dontSee('Login');                         // I can suppose user is already logged in
     * $I->dontSee('Sign Up','h1');                  // I can suppose it's not a signup page
     * $I->dontSee('Sign Up','//body/h1');           // with XPath
     * $I->dontSee('Sign Up', ['css' => 'body h1']); // with strict CSS locator
     * ```
     *
     * Note that the search is done after stripping all HTML tags from the body,
     * so `$I->dontSee('strong')` will fail on strings like:
     *
     *   - `<p>I am Stronger than thou</p>`
     *   - `<script>document.createElement('strong');</script>`
     *
     * But will ignore strings like:
     *
     *   - `<strong>Home</strong>`
     *   - `<div class="strong">Home</strong>`
     *   - `<!-- strong -->`
     *
     * For checking the raw source code, use `seeInSource()`.
     *
     * @param string $text
     * @param array|string $selector optional
     * @see \Codeception\Lib\InnerBrowser::dontSee()
     */
    public function dontSee($text, $selector = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSee', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current page doesn't contain the text specified (case insensitive).
     * Give a locator as the second parameter to match a specific region.
     *
     * ```php
     * <?php
     * $I->dontSee('Login');                         // I can suppose user is already logged in
     * $I->dontSee('Sign Up','h1');                  // I can suppose it's not a signup page
     * $I->dontSee('Sign Up','//body/h1');           // with XPath
     * $I->dontSee('Sign Up', ['css' => 'body h1']); // with strict CSS locator
     * ```
     *
     * Note that the search is done after stripping all HTML tags from the body,
     * so `$I->dontSee('strong')` will fail on strings like:
     *
     *   - `<p>I am Stronger than thou</p>`
     *   - `<script>document.createElement('strong');</script>`
     *
     * But will ignore strings like:
     *
     *   - `<strong>Home</strong>`
     *   - `<div class="strong">Home</strong>`
     *   - `<!-- strong -->`
     *
     * For checking the raw source code, use `seeInSource()`.
     *
     * @param string $text
     * @param array|string $selector optional
     * @see \Codeception\Lib\InnerBrowser::dontSee()
     */
    public function cantSee($text, $selector = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSee', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current page contains the given string in its
     * raw source code.
     *
     * ``` php
     * <?php
     * $I->seeInSource('<h1>Green eggs &amp; ham</h1>');
     * ```
     *
     * @param      $raw
     * @see \Codeception\Lib\InnerBrowser::seeInSource()
     */
    public function seeInSource($raw) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeInSource', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current page contains the given string in its
     * raw source code.
     *
     * ``` php
     * <?php
     * $I->seeInSource('<h1>Green eggs &amp; ham</h1>');
     * ```
     *
     * @param      $raw
     * @see \Codeception\Lib\InnerBrowser::seeInSource()
     */
    public function canSeeInSource($raw) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeInSource', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current page contains the given string in its
     * raw source code.
     *
     * ```php
     * <?php
     * $I->dontSeeInSource('<h1>Green eggs &amp; ham</h1>');
     * ```
     *
     * @param      $raw
     * @see \Codeception\Lib\InnerBrowser::dontSeeInSource()
     */
    public function dontSeeInSource($raw) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeInSource', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current page contains the given string in its
     * raw source code.
     *
     * ```php
     * <?php
     * $I->dontSeeInSource('<h1>Green eggs &amp; ham</h1>');
     * ```
     *
     * @param      $raw
     * @see \Codeception\Lib\InnerBrowser::dontSeeInSource()
     */
    public function cantSeeInSource($raw) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeInSource', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that there's a link with the specified text.
     * Give a full URL as the second parameter to match links with that exact URL.
     *
     * ``` php
     * <?php
     * $I->seeLink('Logout'); // matches <a href="#">Logout</a>
     * $I->seeLink('Logout','/logout'); // matches <a href="/logout">Logout</a>
     * ?>
     * ```
     *
     * @param string $text
     * @param string $url optional
     * @see \Codeception\Lib\InnerBrowser::seeLink()
     */
    public function seeLink($text, $url = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeLink', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that there's a link with the specified text.
     * Give a full URL as the second parameter to match links with that exact URL.
     *
     * ``` php
     * <?php
     * $I->seeLink('Logout'); // matches <a href="#">Logout</a>
     * $I->seeLink('Logout','/logout'); // matches <a href="/logout">Logout</a>
     * ?>
     * ```
     *
     * @param string $text
     * @param string $url optional
     * @see \Codeception\Lib\InnerBrowser::seeLink()
     */
    public function canSeeLink($text, $url = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeLink', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the page doesn't contain a link with the given string.
     * If the second parameter is given, only links with a matching "href" attribute will be checked.
     *
     * ``` php
     * <?php
     * $I->dontSeeLink('Logout'); // I suppose user is not logged in
     * $I->dontSeeLink('Checkout now', '/store/cart.php');
     * ?>
     * ```
     *
     * @param string $text
     * @param string $url optional
     * @see \Codeception\Lib\InnerBrowser::dontSeeLink()
     */
    public function dontSeeLink($text, $url = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeLink', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the page doesn't contain a link with the given string.
     * If the second parameter is given, only links with a matching "href" attribute will be checked.
     *
     * ``` php
     * <?php
     * $I->dontSeeLink('Logout'); // I suppose user is not logged in
     * $I->dontSeeLink('Checkout now', '/store/cart.php');
     * ?>
     * ```
     *
     * @param string $text
     * @param string $url optional
     * @see \Codeception\Lib\InnerBrowser::dontSeeLink()
     */
    public function cantSeeLink($text, $url = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeLink', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that current URI contains the given string.
     *
     * ``` php
     * <?php
     * // to match: /home/<USER>
     * $I->seeInCurrentUrl('home');
     * // to match: /users/1
     * $I->seeInCurrentUrl('/users/');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::seeInCurrentUrl()
     */
    public function seeInCurrentUrl($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeInCurrentUrl', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that current URI contains the given string.
     *
     * ``` php
     * <?php
     * // to match: /home/<USER>
     * $I->seeInCurrentUrl('home');
     * // to match: /users/1
     * $I->seeInCurrentUrl('/users/');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::seeInCurrentUrl()
     */
    public function canSeeInCurrentUrl($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeInCurrentUrl', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current URI doesn't contain the given string.
     *
     * ``` php
     * <?php
     * $I->dontSeeInCurrentUrl('/users/');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::dontSeeInCurrentUrl()
     */
    public function dontSeeInCurrentUrl($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeInCurrentUrl', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current URI doesn't contain the given string.
     *
     * ``` php
     * <?php
     * $I->dontSeeInCurrentUrl('/users/');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::dontSeeInCurrentUrl()
     */
    public function cantSeeInCurrentUrl($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeInCurrentUrl', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current URL is equal to the given string.
     * Unlike `seeInCurrentUrl`, this only matches the full URL.
     *
     * ``` php
     * <?php
     * // to match root url
     * $I->seeCurrentUrlEquals('/');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::seeCurrentUrlEquals()
     */
    public function seeCurrentUrlEquals($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeCurrentUrlEquals', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current URL is equal to the given string.
     * Unlike `seeInCurrentUrl`, this only matches the full URL.
     *
     * ``` php
     * <?php
     * // to match root url
     * $I->seeCurrentUrlEquals('/');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::seeCurrentUrlEquals()
     */
    public function canSeeCurrentUrlEquals($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeCurrentUrlEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current URL doesn't equal the given string.
     * Unlike `dontSeeInCurrentUrl`, this only matches the full URL.
     *
     * ``` php
     * <?php
     * // current url is not root
     * $I->dontSeeCurrentUrlEquals('/');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::dontSeeCurrentUrlEquals()
     */
    public function dontSeeCurrentUrlEquals($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeCurrentUrlEquals', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current URL doesn't equal the given string.
     * Unlike `dontSeeInCurrentUrl`, this only matches the full URL.
     *
     * ``` php
     * <?php
     * // current url is not root
     * $I->dontSeeCurrentUrlEquals('/');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::dontSeeCurrentUrlEquals()
     */
    public function cantSeeCurrentUrlEquals($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeCurrentUrlEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current URL matches the given regular expression.
     *
     * ``` php
     * <?php
     * // to match root url
     * $I->seeCurrentUrlMatches('~^/users/(\d+)~');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::seeCurrentUrlMatches()
     */
    public function seeCurrentUrlMatches($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeCurrentUrlMatches', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current URL matches the given regular expression.
     *
     * ``` php
     * <?php
     * // to match root url
     * $I->seeCurrentUrlMatches('~^/users/(\d+)~');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::seeCurrentUrlMatches()
     */
    public function canSeeCurrentUrlMatches($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeCurrentUrlMatches', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that current url doesn't match the given regular expression.
     *
     * ``` php
     * <?php
     * // to match root url
     * $I->dontSeeCurrentUrlMatches('~^/users/(\d+)~');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::dontSeeCurrentUrlMatches()
     */
    public function dontSeeCurrentUrlMatches($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeCurrentUrlMatches', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that current url doesn't match the given regular expression.
     *
     * ``` php
     * <?php
     * // to match root url
     * $I->dontSeeCurrentUrlMatches('~^/users/(\d+)~');
     * ?>
     * ```
     *
     * @param string $uri
     * @see \Codeception\Lib\InnerBrowser::dontSeeCurrentUrlMatches()
     */
    public function cantSeeCurrentUrlMatches($uri) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeCurrentUrlMatches', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Executes the given regular expression against the current URI and returns the first capturing group.
     * If no parameters are provided, the full URI is returned.
     *
     * ``` php
     * <?php
     * $user_id = $I->grabFromCurrentUrl('~^/user/(\d+)/~');
     * $uri = $I->grabFromCurrentUrl();
     * ?>
     * ```
     *
     * @param string $uri optional
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::grabFromCurrentUrl()
     */
    public function grabFromCurrentUrl($uri = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabFromCurrentUrl', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the specified checkbox is checked.
     *
     * ``` php
     * <?php
     * $I->seeCheckboxIsChecked('#agree'); // I suppose user agreed to terms
     * $I->seeCheckboxIsChecked('#signup_form input[type=checkbox]'); // I suppose user agreed to terms, If there is only one checkbox in form.
     * $I->seeCheckboxIsChecked('//form/input[@type=checkbox and @name=agree]');
     * ?>
     * ```
     *
     * @param $checkbox
     * @see \Codeception\Lib\InnerBrowser::seeCheckboxIsChecked()
     */
    public function seeCheckboxIsChecked($checkbox) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeCheckboxIsChecked', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the specified checkbox is checked.
     *
     * ``` php
     * <?php
     * $I->seeCheckboxIsChecked('#agree'); // I suppose user agreed to terms
     * $I->seeCheckboxIsChecked('#signup_form input[type=checkbox]'); // I suppose user agreed to terms, If there is only one checkbox in form.
     * $I->seeCheckboxIsChecked('//form/input[@type=checkbox and @name=agree]');
     * ?>
     * ```
     *
     * @param $checkbox
     * @see \Codeception\Lib\InnerBrowser::seeCheckboxIsChecked()
     */
    public function canSeeCheckboxIsChecked($checkbox) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeCheckboxIsChecked', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Check that the specified checkbox is unchecked.
     *
     * ``` php
     * <?php
     * $I->dontSeeCheckboxIsChecked('#agree'); // I suppose user didn't agree to terms
     * $I->seeCheckboxIsChecked('#signup_form input[type=checkbox]'); // I suppose user didn't check the first checkbox in form.
     * ?>
     * ```
     *
     * @param $checkbox
     * @see \Codeception\Lib\InnerBrowser::dontSeeCheckboxIsChecked()
     */
    public function dontSeeCheckboxIsChecked($checkbox) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeCheckboxIsChecked', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Check that the specified checkbox is unchecked.
     *
     * ``` php
     * <?php
     * $I->dontSeeCheckboxIsChecked('#agree'); // I suppose user didn't agree to terms
     * $I->seeCheckboxIsChecked('#signup_form input[type=checkbox]'); // I suppose user didn't check the first checkbox in form.
     * ?>
     * ```
     *
     * @param $checkbox
     * @see \Codeception\Lib\InnerBrowser::dontSeeCheckboxIsChecked()
     */
    public function cantSeeCheckboxIsChecked($checkbox) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeCheckboxIsChecked', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the given input field or textarea *equals* (i.e. not just contains) the given value.
     * Fields are matched by label text, the "name" attribute, CSS, or XPath.
     *
     * ``` php
     * <?php
     * $I->seeInField('Body','Type your comment here');
     * $I->seeInField('form textarea[name=body]','Type your comment here');
     * $I->seeInField('form input[type=hidden]','hidden_value');
     * $I->seeInField('#searchform input','Search');
     * $I->seeInField('//form/*[@name=search]','Search');
     * $I->seeInField(['name' => 'search'], 'Search');
     * ?>
     * ```
     *
     * @param $field
     * @param $value
     * @see \Codeception\Lib\InnerBrowser::seeInField()
     */
    public function seeInField($field, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeInField', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the given input field or textarea *equals* (i.e. not just contains) the given value.
     * Fields are matched by label text, the "name" attribute, CSS, or XPath.
     *
     * ``` php
     * <?php
     * $I->seeInField('Body','Type your comment here');
     * $I->seeInField('form textarea[name=body]','Type your comment here');
     * $I->seeInField('form input[type=hidden]','hidden_value');
     * $I->seeInField('#searchform input','Search');
     * $I->seeInField('//form/*[@name=search]','Search');
     * $I->seeInField(['name' => 'search'], 'Search');
     * ?>
     * ```
     *
     * @param $field
     * @param $value
     * @see \Codeception\Lib\InnerBrowser::seeInField()
     */
    public function canSeeInField($field, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeInField', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that an input field or textarea doesn't contain the given value.
     * For fuzzy locators, the field is matched by label text, CSS and XPath.
     *
     * ``` php
     * <?php
     * $I->dontSeeInField('Body','Type your comment here');
     * $I->dontSeeInField('form textarea[name=body]','Type your comment here');
     * $I->dontSeeInField('form input[type=hidden]','hidden_value');
     * $I->dontSeeInField('#searchform input','Search');
     * $I->dontSeeInField('//form/*[@name=search]','Search');
     * $I->dontSeeInField(['name' => 'search'], 'Search');
     * ?>
     * ```
     *
     * @param $field
     * @param $value
     * @see \Codeception\Lib\InnerBrowser::dontSeeInField()
     */
    public function dontSeeInField($field, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeInField', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that an input field or textarea doesn't contain the given value.
     * For fuzzy locators, the field is matched by label text, CSS and XPath.
     *
     * ``` php
     * <?php
     * $I->dontSeeInField('Body','Type your comment here');
     * $I->dontSeeInField('form textarea[name=body]','Type your comment here');
     * $I->dontSeeInField('form input[type=hidden]','hidden_value');
     * $I->dontSeeInField('#searchform input','Search');
     * $I->dontSeeInField('//form/*[@name=search]','Search');
     * $I->dontSeeInField(['name' => 'search'], 'Search');
     * ?>
     * ```
     *
     * @param $field
     * @param $value
     * @see \Codeception\Lib\InnerBrowser::dontSeeInField()
     */
    public function cantSeeInField($field, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeInField', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if the array of form parameters (name => value) are set on the form matched with the
     * passed selector.
     *
     * ``` php
     * <?php
     * $I->seeInFormFields('form[name=myform]', [
     *      'input1' => 'value',
     *      'input2' => 'other value',
     * ]);
     * ?>
     * ```
     *
     * For multi-select elements, or to check values of multiple elements with the same name, an
     * array may be passed:
     *
     * ``` php
     * <?php
     * $I->seeInFormFields('.form-class', [
     *      'multiselect' => [
     *          'value1',
     *          'value2',
     *      ],
     *      'checkbox[]' => [
     *          'a checked value',
     *          'another checked value',
     *      ],
     * ]);
     * ?>
     * ```
     *
     * Additionally, checkbox values can be checked with a boolean.
     *
     * ``` php
     * <?php
     * $I->seeInFormFields('#form-id', [
     *      'checkbox1' => true,        // passes if checked
     *      'checkbox2' => false,       // passes if unchecked
     * ]);
     * ?>
     * ```
     *
     * Pair this with submitForm for quick testing magic.
     *
     * ``` php
     * <?php
     * $form = [
     *      'field1' => 'value',
     *      'field2' => 'another value',
     *      'checkbox1' => true,
     *      // ...
     * ];
     * $I->submitForm('//form[@id=my-form]', $form, 'submitButton');
     * // $I->amOnPage('/path/to/form-page') may be needed
     * $I->seeInFormFields('//form[@id=my-form]', $form);
     * ?>
     * ```
     *
     * @param $formSelector
     * @param $params
     * @see \Codeception\Lib\InnerBrowser::seeInFormFields()
     */
    public function seeInFormFields($formSelector, $params) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeInFormFields', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks if the array of form parameters (name => value) are set on the form matched with the
     * passed selector.
     *
     * ``` php
     * <?php
     * $I->seeInFormFields('form[name=myform]', [
     *      'input1' => 'value',
     *      'input2' => 'other value',
     * ]);
     * ?>
     * ```
     *
     * For multi-select elements, or to check values of multiple elements with the same name, an
     * array may be passed:
     *
     * ``` php
     * <?php
     * $I->seeInFormFields('.form-class', [
     *      'multiselect' => [
     *          'value1',
     *          'value2',
     *      ],
     *      'checkbox[]' => [
     *          'a checked value',
     *          'another checked value',
     *      ],
     * ]);
     * ?>
     * ```
     *
     * Additionally, checkbox values can be checked with a boolean.
     *
     * ``` php
     * <?php
     * $I->seeInFormFields('#form-id', [
     *      'checkbox1' => true,        // passes if checked
     *      'checkbox2' => false,       // passes if unchecked
     * ]);
     * ?>
     * ```
     *
     * Pair this with submitForm for quick testing magic.
     *
     * ``` php
     * <?php
     * $form = [
     *      'field1' => 'value',
     *      'field2' => 'another value',
     *      'checkbox1' => true,
     *      // ...
     * ];
     * $I->submitForm('//form[@id=my-form]', $form, 'submitButton');
     * // $I->amOnPage('/path/to/form-page') may be needed
     * $I->seeInFormFields('//form[@id=my-form]', $form);
     * ?>
     * ```
     *
     * @param $formSelector
     * @param $params
     * @see \Codeception\Lib\InnerBrowser::seeInFormFields()
     */
    public function canSeeInFormFields($formSelector, $params) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeInFormFields', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if the array of form parameters (name => value) are not set on the form matched with
     * the passed selector.
     *
     * ``` php
     * <?php
     * $I->dontSeeInFormFields('form[name=myform]', [
     *      'input1' => 'non-existent value',
     *      'input2' => 'other non-existent value',
     * ]);
     * ?>
     * ```
     *
     * To check that an element hasn't been assigned any one of many values, an array can be passed
     * as the value:
     *
     * ``` php
     * <?php
     * $I->dontSeeInFormFields('.form-class', [
     *      'fieldName' => [
     *          'This value shouldn\'t be set',
     *          'And this value shouldn\'t be set',
     *      ],
     * ]);
     * ?>
     * ```
     *
     * Additionally, checkbox values can be checked with a boolean.
     *
     * ``` php
     * <?php
     * $I->dontSeeInFormFields('#form-id', [
     *      'checkbox1' => true,        // fails if checked
     *      'checkbox2' => false,       // fails if unchecked
     * ]);
     * ?>
     * ```
     *
     * @param $formSelector
     * @param $params
     * @see \Codeception\Lib\InnerBrowser::dontSeeInFormFields()
     */
    public function dontSeeInFormFields($formSelector, $params) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeInFormFields', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks if the array of form parameters (name => value) are not set on the form matched with
     * the passed selector.
     *
     * ``` php
     * <?php
     * $I->dontSeeInFormFields('form[name=myform]', [
     *      'input1' => 'non-existent value',
     *      'input2' => 'other non-existent value',
     * ]);
     * ?>
     * ```
     *
     * To check that an element hasn't been assigned any one of many values, an array can be passed
     * as the value:
     *
     * ``` php
     * <?php
     * $I->dontSeeInFormFields('.form-class', [
     *      'fieldName' => [
     *          'This value shouldn\'t be set',
     *          'And this value shouldn\'t be set',
     *      ],
     * ]);
     * ?>
     * ```
     *
     * Additionally, checkbox values can be checked with a boolean.
     *
     * ``` php
     * <?php
     * $I->dontSeeInFormFields('#form-id', [
     *      'checkbox1' => true,        // fails if checked
     *      'checkbox2' => false,       // fails if unchecked
     * ]);
     * ?>
     * ```
     *
     * @param $formSelector
     * @param $params
     * @see \Codeception\Lib\InnerBrowser::dontSeeInFormFields()
     */
    public function cantSeeInFormFields($formSelector, $params) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeInFormFields', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Submits the given form on the page, with the given form
     * values.  Pass the form field's values as an array in the second
     * parameter.
     *
     * Although this function can be used as a short-hand version of
     * `fillField()`, `selectOption()`, `click()` etc. it has some important
     * differences:
     *
     *  * Only field *names* may be used, not CSS/XPath selectors nor field labels
     *  * If a field is sent to this function that does *not* exist on the page,
     *    it will silently be added to the HTTP request.  This is helpful for testing
     *    some types of forms, but be aware that you will *not* get an exception
     *    like you would if you called `fillField()` or `selectOption()` with
     *    a missing field.
     *
     * Fields that are not provided will be filled by their values from the page,
     * or from any previous calls to `fillField()`, `selectOption()` etc.
     * You don't need to click the 'Submit' button afterwards.
     * This command itself triggers the request to form's action.
     *
     * You can optionally specify which button's value to include
     * in the request with the last parameter (as an alternative to
     * explicitly setting its value in the second parameter), as
     * button values are not otherwise included in the request.
     *
     * Examples:
     *
     * ``` php
     * <?php
     * $I->submitForm('#login', [
     *     'login' => 'davert',
     *     'password' => '123456'
     * ]);
     * // or
     * $I->submitForm('#login', [
     *     'login' => 'davert',
     *     'password' => '123456'
     * ], 'submitButtonName');
     *
     * ```
     *
     * For example, given this sample "Sign Up" form:
     *
     * ``` html
     * <form action="/sign_up">
     *     Login:
     *     <input type="text" name="user[login]" /><br/>
     *     Password:
     *     <input type="password" name="user[password]" /><br/>
     *     Do you agree to our terms?
     *     <input type="checkbox" name="user[agree]" /><br/>
     *     Select pricing plan:
     *     <select name="plan">
     *         <option value="1">Free</option>
     *         <option value="2" selected="selected">Paid</option>
     *     </select>
     *     <input type="submit" name="submitButton" value="Submit" />
     * </form>
     * ```
     *
     * You could write the following to submit it:
     *
     * ``` php
     * <?php
     * $I->submitForm(
     *     '#userForm',
     *     [
     *         'user' => [
     *             'login' => 'Davert',
     *             'password' => '123456',
     *             'agree' => true
     *         ]
     *     ],
     *     'submitButton'
     * );
     * ```
     * Note that "2" will be the submitted value for the "plan" field, as it is
     * the selected option.
     *
     * You can also emulate a JavaScript submission by not specifying any
     * buttons in the third parameter to submitForm.
     *
     * ```php
     * <?php
     * $I->submitForm(
     *     '#userForm',
     *     [
     *         'user' => [
     *             'login' => 'Davert',
     *             'password' => '123456',
     *             'agree' => true
     *         ]
     *     ]
     * );
     * ```
     *
     * This function works well when paired with `seeInFormFields()`
     * for quickly testing CRUD interfaces and form validation logic.
     *
     * ``` php
     * <?php
     * $form = [
     *      'field1' => 'value',
     *      'field2' => 'another value',
     *      'checkbox1' => true,
     *      // ...
     * ];
     * $I->submitForm('#my-form', $form, 'submitButton');
     * // $I->amOnPage('/path/to/form-page') may be needed
     * $I->seeInFormFields('#my-form', $form);
     * ```
     *
     * Parameter values can be set to arrays for multiple input fields
     * of the same name, or multi-select combo boxes.  For checkboxes,
     * you can use either the string value or boolean `true`/`false` which will
     * be replaced by the checkbox's value in the DOM.
     *
     * ``` php
     * <?php
     * $I->submitForm('#my-form', [
     *      'field1' => 'value',
     *      'checkbox' => [
     *          'value of first checkbox',
     *          'value of second checkbox',
     *      ],
     *      'otherCheckboxes' => [
     *          true,
     *          false,
     *          false
     *      ],
     *      'multiselect' => [
     *          'first option value',
     *          'second option value'
     *      ]
     * ]);
     * ```
     *
     * Mixing string and boolean values for a checkbox's value is not supported
     * and may produce unexpected results.
     *
     * Field names ending in `[]` must be passed without the trailing square
     * bracket characters, and must contain an array for its value.  This allows
     * submitting multiple values with the same name, consider:
     *
     * ```php
     * <?php
     * // This will NOT work correctly
     * $I->submitForm('#my-form', [
     *     'field[]' => 'value',
     *     'field[]' => 'another value',  // 'field[]' is already a defined key
     * ]);
     * ```
     *
     * The solution is to pass an array value:
     *
     * ```php
     * <?php
     * // This way both values are submitted
     * $I->submitForm('#my-form', [
     *     'field' => [
     *         'value',
     *         'another value',
     *     ]
     * ]);
     * ```
     *
     * @param $selector
     * @param $params
     * @param $button
     * @see \Codeception\Lib\InnerBrowser::submitForm()
     */
    public function submitForm($selector, $params, $button = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('submitForm', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Fills a text field or textarea with the given string.
     *
     * ``` php
     * <?php
     * $I->fillField("//input[@type='text']", "Hello World!");
     * $I->fillField(['name' => 'email'], '<EMAIL>');
     * ?>
     * ```
     *
     * @param $field
     * @param $value
     * @see \Codeception\Lib\InnerBrowser::fillField()
     */
    public function fillField($field, $value) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('fillField', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Selects an option in a select tag or in radio button group.
     *
     * ``` php
     * <?php
     * $I->selectOption('form select[name=account]', 'Premium');
     * $I->selectOption('form input[name=payment]', 'Monthly');
     * $I->selectOption('//form/select[@name=account]', 'Monthly');
     * ?>
     * ```
     *
     * Provide an array for the second argument to select multiple options:
     *
     * ``` php
     * <?php
     * $I->selectOption('Which OS do you use?', array('Windows','Linux'));
     * ?>
     * ```
     *
     * Or provide an associative array for the second argument to specifically define which selection method should be used:
     *
     * ``` php
     * <?php
     * $I->selectOption('Which OS do you use?', array('text' => 'Windows')); // Only search by text 'Windows'
     * $I->selectOption('Which OS do you use?', array('value' => 'windows')); // Only search by value 'windows'
     * ?>
     * ```
     *
     * @param $select
     * @param $option
     * @see \Codeception\Lib\InnerBrowser::selectOption()
     */
    public function selectOption($select, $option) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('selectOption', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Ticks a checkbox. For radio buttons, use the `selectOption` method instead.
     *
     * ``` php
     * <?php
     * $I->checkOption('#agree');
     * ?>
     * ```
     *
     * @param $option
     * @see \Codeception\Lib\InnerBrowser::checkOption()
     */
    public function checkOption($option) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('checkOption', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Unticks a checkbox.
     *
     * ``` php
     * <?php
     * $I->uncheckOption('#notify');
     * ?>
     * ```
     *
     * @param $option
     * @see \Codeception\Lib\InnerBrowser::uncheckOption()
     */
    public function uncheckOption($option) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('uncheckOption', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Attaches a file relative to the Codeception `_data` directory to the given file upload field.
     *
     * ``` php
     * <?php
     * // file is stored in 'tests/_data/prices.xls'
     * $I->attachFile('input[@type="file"]', 'prices.xls');
     * ?>
     * ```
     *
     * @param $field
     * @param $filename
     * @see \Codeception\Lib\InnerBrowser::attachFile()
     */
    public function attachFile($field, $filename) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('attachFile', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * If your page triggers an ajax request, you can perform it manually.
     * This action sends a GET ajax request with specified params.
     *
     * See ->sendAjaxPostRequest for examples.
     *
     * @param $uri
     * @param $params
     * @see \Codeception\Lib\InnerBrowser::sendAjaxGetRequest()
     */
    public function sendAjaxGetRequest($uri, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendAjaxGetRequest', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * If your page triggers an ajax request, you can perform it manually.
     * This action sends a POST ajax request with specified params.
     * Additional params can be passed as array.
     *
     * Example:
     *
     * Imagine that by clicking checkbox you trigger ajax request which updates user settings.
     * We emulate that click by running this ajax request manually.
     *
     * ``` php
     * <?php
     * $I->sendAjaxPostRequest('/updateSettings', array('notifications' => true)); // POST
     * $I->sendAjaxGetRequest('/updateSettings', array('notifications' => true)); // GET
     *
     * ```
     *
     * @param $uri
     * @param $params
     * @see \Codeception\Lib\InnerBrowser::sendAjaxPostRequest()
     */
    public function sendAjaxPostRequest($uri, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendAjaxPostRequest', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * If your page triggers an ajax request, you can perform it manually.
     * This action sends an ajax request with specified method and params.
     *
     * Example:
     *
     * You need to perform an ajax request specifying the HTTP method.
     *
     * ``` php
     * <?php
     * $I->sendAjaxRequest('PUT', '/posts/7', array('title' => 'new title'));
     *
     * ```
     *
     * @param $method
     * @param $uri
     * @param $params
     * @see \Codeception\Lib\InnerBrowser::sendAjaxRequest()
     */
    public function sendAjaxRequest($method, $uri, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('sendAjaxRequest', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Saves current page's HTML into a temprary file.
     * Use this method in debug mode within an interactive pause to get a source code of current page.
     *
     * ```php
     * <?php
     * $I->makeHtmlSnapshot('edit_page');
     * // saved to: tests/_output/debug/edit_page.html
     * $I->makeHtmlSnapshot();
     * // saved to: tests/_output/debug/2017-05-26_14-24-11_4b3403665fea6.html
     * ```
     *
     * @param null $name
     * @see \Codeception\Lib\InnerBrowser::makeHtmlSnapshot()
     */
    public function makeHtmlSnapshot($name = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('makeHtmlSnapshot', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Finds and returns the text contents of the given element.
     * If a fuzzy locator is used, the element is found using CSS, XPath,
     * and by matching the full page source by regular expression.
     *
     * ``` php
     * <?php
     * $heading = $I->grabTextFrom('h1');
     * $heading = $I->grabTextFrom('descendant-or-self::h1');
     * $value = $I->grabTextFrom('~<input value=(.*?)]~sgi'); // match with a regex
     * ?>
     * ```
     *
     * @param $cssOrXPathOrRegex
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::grabTextFrom()
     */
    public function grabTextFrom($cssOrXPathOrRegex) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabTextFrom', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Grabs the value of the given attribute value from the given element.
     * Fails if element is not found.
     *
     * ``` php
     * <?php
     * $I->grabAttributeFrom('#tooltip', 'title');
     * ?>
     * ```
     *
     *
     * @param $cssOrXpath
     * @param $attribute
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::grabAttributeFrom()
     */
    public function grabAttributeFrom($cssOrXpath, $attribute) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabAttributeFrom', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Grabs either the text content, or attribute values, of nodes
     * matched by $cssOrXpath and returns them as an array.
     *
     * ```html
     * <a href="#first">First</a>
     * <a href="#second">Second</a>
     * <a href="#third">Third</a>
     * ```
     *
     * ```php
     * <?php
     * // would return ['First', 'Second', 'Third']
     * $aLinkText = $I->grabMultiple('a');
     *
     * // would return ['#first', '#second', '#third']
     * $aLinks = $I->grabMultiple('a', 'href');
     * ?>
     * ```
     *
     * @param $cssOrXpath
     * @param $attribute
     * @return string[]
     * @see \Codeception\Lib\InnerBrowser::grabMultiple()
     */
    public function grabMultiple($cssOrXpath, $attribute = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabMultiple', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $field
     *
     * @return array|mixed|null|string
     * @see \Codeception\Lib\InnerBrowser::grabValueFrom()
     */
    public function grabValueFrom($field) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabValueFrom', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Sets a cookie with the given name and value.
     * You can set additional cookie params like `domain`, `path`, `expires`, `secure` in array passed as last argument.
     *
     * ``` php
     * <?php
     * $I->setCookie('PHPSESSID', 'el4ukv0kqbvoirg7nkp4dncpk3');
     * ?>
     * ```
     *
     * @param $name
     * @param $val
     * @param array $params
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::setCookie()
     */
    public function setCookie($name, $val, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('setCookie', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Grabs a cookie value.
     * You can set additional cookie params like `domain`, `path` in array passed as last argument.
     *
     * @param $cookie
     *
     * @param array $params
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::grabCookie()
     */
    public function grabCookie($cookie, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabCookie', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Grabs current page source code.
     *
     * @throws ModuleException if no page was opened.
     *
     * @return string Current page source code.
     * @see \Codeception\Lib\InnerBrowser::grabPageSource()
     */
    public function grabPageSource() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabPageSource', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a cookie with the given name is set.
     * You can set additional cookie params like `domain`, `path` as array passed in last argument.
     *
     * ``` php
     * <?php
     * $I->seeCookie('PHPSESSID');
     * ?>
     * ```
     *
     * @param $cookie
     * @param array $params
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::seeCookie()
     */
    public function seeCookie($cookie, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeCookie', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that a cookie with the given name is set.
     * You can set additional cookie params like `domain`, `path` as array passed in last argument.
     *
     * ``` php
     * <?php
     * $I->seeCookie('PHPSESSID');
     * ?>
     * ```
     *
     * @param $cookie
     * @param array $params
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::seeCookie()
     */
    public function canSeeCookie($cookie, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeCookie', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that there isn't a cookie with the given name.
     * You can set additional cookie params like `domain`, `path` as array passed in last argument.
     *
     * @param $cookie
     *
     * @param array $params
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::dontSeeCookie()
     */
    public function dontSeeCookie($cookie, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeCookie', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that there isn't a cookie with the given name.
     * You can set additional cookie params like `domain`, `path` as array passed in last argument.
     *
     * @param $cookie
     *
     * @param array $params
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::dontSeeCookie()
     */
    public function cantSeeCookie($cookie, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeCookie', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Unsets cookie with the given name.
     * You can set additional cookie params like `domain`, `path` in array passed as last argument.
     *
     * @param $cookie
     *
     * @param array $params
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::resetCookie()
     */
    public function resetCookie($name, $params = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('resetCookie', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the given element exists on the page and is visible.
     * You can also specify expected attributes of this element.
     *
     * ``` php
     * <?php
     * $I->seeElement('.error');
     * $I->seeElement('//form/input[1]');
     * $I->seeElement('input', ['name' => 'login']);
     * $I->seeElement('input', ['value' => '123456']);
     *
     * // strict locator in first arg, attributes in second
     * $I->seeElement(['css' => 'form input'], ['name' => 'login']);
     * ?>
     * ```
     *
     * @param $selector
     * @param array $attributes
     * @return
     * @see \Codeception\Lib\InnerBrowser::seeElement()
     */
    public function seeElement($selector, $attributes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeElement', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the given element exists on the page and is visible.
     * You can also specify expected attributes of this element.
     *
     * ``` php
     * <?php
     * $I->seeElement('.error');
     * $I->seeElement('//form/input[1]');
     * $I->seeElement('input', ['name' => 'login']);
     * $I->seeElement('input', ['value' => '123456']);
     *
     * // strict locator in first arg, attributes in second
     * $I->seeElement(['css' => 'form input'], ['name' => 'login']);
     * ?>
     * ```
     *
     * @param $selector
     * @param array $attributes
     * @return
     * @see \Codeception\Lib\InnerBrowser::seeElement()
     */
    public function canSeeElement($selector, $attributes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeElement', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the given element is invisible or not present on the page.
     * You can also specify expected attributes of this element.
     *
     * ``` php
     * <?php
     * $I->dontSeeElement('.error');
     * $I->dontSeeElement('//form/input[1]');
     * $I->dontSeeElement('input', ['name' => 'login']);
     * $I->dontSeeElement('input', ['value' => '123456']);
     * ?>
     * ```
     *
     * @param $selector
     * @param array $attributes
     * @see \Codeception\Lib\InnerBrowser::dontSeeElement()
     */
    public function dontSeeElement($selector, $attributes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeElement', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the given element is invisible or not present on the page.
     * You can also specify expected attributes of this element.
     *
     * ``` php
     * <?php
     * $I->dontSeeElement('.error');
     * $I->dontSeeElement('//form/input[1]');
     * $I->dontSeeElement('input', ['name' => 'login']);
     * $I->dontSeeElement('input', ['value' => '123456']);
     * ?>
     * ```
     *
     * @param $selector
     * @param array $attributes
     * @see \Codeception\Lib\InnerBrowser::dontSeeElement()
     */
    public function cantSeeElement($selector, $attributes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeElement', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that there are a certain number of elements matched by the given locator on the page.
     *
     * ``` php
     * <?php
     * $I->seeNumberOfElements('tr', 10);
     * $I->seeNumberOfElements('tr', [0,10]); // between 0 and 10 elements
     * ?>
     * ```
     * @param $selector
     * @param mixed $expected int or int[]
     * @see \Codeception\Lib\InnerBrowser::seeNumberOfElements()
     */
    public function seeNumberOfElements($selector, $expected) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeNumberOfElements', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that there are a certain number of elements matched by the given locator on the page.
     *
     * ``` php
     * <?php
     * $I->seeNumberOfElements('tr', 10);
     * $I->seeNumberOfElements('tr', [0,10]); // between 0 and 10 elements
     * ?>
     * ```
     * @param $selector
     * @param mixed $expected int or int[]
     * @see \Codeception\Lib\InnerBrowser::seeNumberOfElements()
     */
    public function canSeeNumberOfElements($selector, $expected) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeNumberOfElements', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the given option is selected.
     *
     * ``` php
     * <?php
     * $I->seeOptionIsSelected('#form input[name=payment]', 'Visa');
     * ?>
     * ```
     *
     * @param $selector
     * @param $optionText
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::seeOptionIsSelected()
     */
    public function seeOptionIsSelected($selector, $optionText) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeOptionIsSelected', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the given option is selected.
     *
     * ``` php
     * <?php
     * $I->seeOptionIsSelected('#form input[name=payment]', 'Visa');
     * ?>
     * ```
     *
     * @param $selector
     * @param $optionText
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::seeOptionIsSelected()
     */
    public function canSeeOptionIsSelected($selector, $optionText) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeOptionIsSelected', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the given option is not selected.
     *
     * ``` php
     * <?php
     * $I->dontSeeOptionIsSelected('#form input[name=payment]', 'Visa');
     * ?>
     * ```
     *
     * @param $selector
     * @param $optionText
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::dontSeeOptionIsSelected()
     */
    public function dontSeeOptionIsSelected($selector, $optionText) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeOptionIsSelected', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the given option is not selected.
     *
     * ``` php
     * <?php
     * $I->dontSeeOptionIsSelected('#form input[name=payment]', 'Visa');
     * ?>
     * ```
     *
     * @param $selector
     * @param $optionText
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::dontSeeOptionIsSelected()
     */
    public function cantSeeOptionIsSelected($selector, $optionText) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeOptionIsSelected', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Asserts that current page has 404 response status code.
     * @see \Codeception\Lib\InnerBrowser::seePageNotFound()
     */
    public function seePageNotFound() {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seePageNotFound', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Asserts that current page has 404 response status code.
     * @see \Codeception\Lib\InnerBrowser::seePageNotFound()
     */
    public function canSeePageNotFound() {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seePageNotFound', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that response code is between a certain range. Between actually means [from <= CODE <= to]
     *
     * @param $from
     * @param $to
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsBetween()
     */
    public function seeResponseCodeIsBetween($from, $to) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeResponseCodeIsBetween', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that response code is between a certain range. Between actually means [from <= CODE <= to]
     *
     * @param $from
     * @param $to
     * @see \Codeception\Lib\InnerBrowser::seeResponseCodeIsBetween()
     */
    public function canSeeResponseCodeIsBetween($from, $to) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeResponseCodeIsBetween', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the page title contains the given string.
     *
     * ``` php
     * <?php
     * $I->seeInTitle('Blog - Post #1');
     * ?>
     * ```
     *
     * @param $title
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::seeInTitle()
     */
    public function seeInTitle($title) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeInTitle', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the page title contains the given string.
     *
     * ``` php
     * <?php
     * $I->seeInTitle('Blog - Post #1');
     * ?>
     * ```
     *
     * @param $title
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::seeInTitle()
     */
    public function canSeeInTitle($title) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeInTitle', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the page title does not contain the given string.
     *
     * @param $title
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::dontSeeInTitle()
     */
    public function dontSeeInTitle($title) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeeInTitle', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the page title does not contain the given string.
     *
     * @param $title
     *
     * @return mixed
     * @see \Codeception\Lib\InnerBrowser::dontSeeInTitle()
     */
    public function cantSeeInTitle($title) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeeInTitle', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Switch to iframe or frame on the page.
     *
     * Example:
     * ``` html
     * <iframe name="another_frame" src="http://example.com">
     * ```
     *
     * ``` php
     * <?php
     * # switch to iframe
     * $I->switchToIframe("another_frame");
     * ```
     *
     * @param string $name
     * @see \Codeception\Lib\InnerBrowser::switchToIframe()
     */
    public function switchToIframe($name) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('switchToIframe', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Moves back in history.
     *
     * @param int $numberOfSteps (default value 1)
     * @see \Codeception\Lib\InnerBrowser::moveBack()
     */
    public function moveBack($numberOfSteps = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('moveBack', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Login as the administrator user using the credentials specified in the module configuration.
     *
     * The method will **not** follow redirection, after the login, to any page.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnAdminPage('/');
     * $I->see('Dashboard');
     * ```
     * @see \Codeception\Module\WPBrowser::loginAsAdmin()
     */
    public function loginAsAdmin() {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('loginAsAdmin', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Login as the specified user.
     *
     * The method will **not** follow redirection, after the login, to any page.
     *
     * @example
     * ```php
     * $I->loginAs('user', 'password');
     * $I->amOnAdminPage('/');
     * $I->see('Dashboard');
     * ```
     *
     * @param string $username The user login name.
     * @param string $password The user password in plain text.
     * @see \Codeception\Module\WPBrowser::loginAs()
     */
    public function loginAs($username, $password) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('loginAs', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Go to the plugins administration screen.
     *
     *  The method will **not** handle authentication.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->activatePlugin('hello-dolly');
     * ```
     * @see \Codeception\Module\WPBrowser::amOnPluginsPage()
     */
    public function amOnPluginsPage() {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amOnPluginsPage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Go the "Pages" administration screen.
     *
     * The method will **not** handle authentication.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPagesPage();
     * $I->see('Add New');
     * ```
     * @see \Codeception\Module\WPBrowser::amOnPagesPage()
     */
    public function amOnPagesPage() {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amOnPagesPage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Assert a plugin is not activated in the plugins administration screen.
     *
     * The method will **not** handle authentication and navigation to the plugin administration screen.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->seePluginDeactivated('my-plugin');
     * ```
     *
     * @param string $pluginSlug The plugin slug, like "hello-dolly".
     * @see \Codeception\Module\WPBrowser::seePluginDeactivated()
     */
    public function seePluginDeactivated($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seePluginDeactivated', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Assert a plugin is not activated in the plugins administration screen.
     *
     * The method will **not** handle authentication and navigation to the plugin administration screen.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->seePluginDeactivated('my-plugin');
     * ```
     *
     * @param string $pluginSlug The plugin slug, like "hello-dolly".
     * @see \Codeception\Module\WPBrowser::seePluginDeactivated()
     */
    public function canSeePluginDeactivated($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seePluginDeactivated', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Assert a plugin is installed, no matter its activation status, in the plugin adminstration screen.
     *
     * The method will **not** handle authentication and navigation to the plugin administration screen.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->seePluginInstalled('my-plugin');
     * ```
     *
     * @param string $pluginSlug The plugin slug, like "hello-dolly".
     * @see \Codeception\Module\WPBrowser::seePluginInstalled()
     */
    public function seePluginInstalled($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seePluginInstalled', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Assert a plugin is installed, no matter its activation status, in the plugin adminstration screen.
     *
     * The method will **not** handle authentication and navigation to the plugin administration screen.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->seePluginInstalled('my-plugin');
     * ```
     *
     * @param string $pluginSlug The plugin slug, like "hello-dolly".
     * @see \Codeception\Module\WPBrowser::seePluginInstalled()
     */
    public function canSeePluginInstalled($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seePluginInstalled', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Assert a plugin is activated in the plugin administration screen.
     *
     * The method will **not** handle authentication and navigation to the plugin administration screen.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->seePluginActivated('my-plugin');
     * ```
     *
     * @param string $pluginSlug The plugin slug, like "hello-dolly".
     * @see \Codeception\Module\WPBrowser::seePluginActivated()
     */
    public function seePluginActivated($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seePluginActivated', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Assert a plugin is activated in the plugin administration screen.
     *
     * The method will **not** handle authentication and navigation to the plugin administration screen.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->seePluginActivated('my-plugin');
     * ```
     *
     * @param string $pluginSlug The plugin slug, like "hello-dolly".
     * @see \Codeception\Module\WPBrowser::seePluginActivated()
     */
    public function canSeePluginActivated($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seePluginActivated', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Assert a plugin is not installed in the plugins administration screen.
     *
     * The method will **not** handle authentication and navigation to the plugin administration screen.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->dontSeePluginInstalled('my-plugin');
     * ```
     *
     * @param string $pluginSlug The plugin slug, like "hello-dolly".
     * @see \Codeception\Module\WPBrowser::dontSeePluginInstalled()
     */
    public function dontSeePluginInstalled($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('dontSeePluginInstalled', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Assert a plugin is not installed in the plugins administration screen.
     *
     * The method will **not** handle authentication and navigation to the plugin administration screen.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $I->amOnPluginsPage();
     * $I->dontSeePluginInstalled('my-plugin');
     * ```
     *
     * @param string $pluginSlug The plugin slug, like "hello-dolly".
     * @see \Codeception\Module\WPBrowser::dontSeePluginInstalled()
     */
    public function cantSeePluginInstalled($pluginSlug) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('dontSeePluginInstalled', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * In an administration screen look for an error admin notice.
     *
     * The check is class-based to decouple from internationalization.
     * The method will **not** handle authentication and navigation the administration area.
     *
     * @example
     * ```php
     * $I->loginAsAdmin()ja
     * $I->amOnAdminPage('/');
     * $I->seeErrorMessage('.my-plugin');
     * ```
     *
     * @param array|string $classes A list of classes the notice should have other than the `.notice.notice-error` ones.
     * @see \Codeception\Module\WPBrowser::seeErrorMessage()
     */
    public function seeErrorMessage($classes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeErrorMessage', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * In an administration screen look for an error admin notice.
     *
     * The check is class-based to decouple from internationalization.
     * The method will **not** handle authentication and navigation the administration area.
     *
     * @example
     * ```php
     * $I->loginAsAdmin()ja
     * $I->amOnAdminPage('/');
     * $I->seeErrorMessage('.my-plugin');
     * ```
     *
     * @param array|string $classes A list of classes the notice should have other than the `.notice.notice-error` ones.
     * @see \Codeception\Module\WPBrowser::seeErrorMessage()
     */
    public function canSeeErrorMessage($classes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeErrorMessage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the current page is one generated by the `wp_die` function.
     *
     * The method will try to identify the page based on the default WordPress die page HTML attributes.
     *
     * @example
     * ```php
     * $I->loginAs('user', 'password');
     * $I->amOnAdminPage('/forbidden');
     * $I->seeWpDiePage();
     * ```
     * @see \Codeception\Module\WPBrowser::seeWpDiePage()
     */
    public function seeWpDiePage() {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeWpDiePage', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * Checks that the current page is one generated by the `wp_die` function.
     *
     * The method will try to identify the page based on the default WordPress die page HTML attributes.
     *
     * @example
     * ```php
     * $I->loginAs('user', 'password');
     * $I->amOnAdminPage('/forbidden');
     * $I->seeWpDiePage();
     * ```
     * @see \Codeception\Module\WPBrowser::seeWpDiePage()
     */
    public function canSeeWpDiePage() {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeWpDiePage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * In an administration screen look for an admin notice.
     *
     * The check is class-based to decouple from internationalization.
     * The method will **not** handle authentication and navigation the administration area.
     *
     * @example
     * ```php
     * $I->loginAsAdmin()ja
     * $I->amOnAdminPage('/');
     * $I->seeMessage('.missing-api-token.my-plugin');
     * ```
     *
     * @param array|string $classes A list of classes the message should have in addition to the `.notice` one.
     * @see \Codeception\Module\WPBrowser::seeMessage()
     */
    public function seeMessage($classes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Assertion('seeMessage', func_get_args()));
    }
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * [!] Conditional Assertion: Test won't be stopped on fail
     * In an administration screen look for an admin notice.
     *
     * The check is class-based to decouple from internationalization.
     * The method will **not** handle authentication and navigation the administration area.
     *
     * @example
     * ```php
     * $I->loginAsAdmin()ja
     * $I->amOnAdminPage('/');
     * $I->seeMessage('.missing-api-token.my-plugin');
     * ```
     *
     * @param array|string $classes A list of classes the message should have in addition to the `.notice` one.
     * @see \Codeception\Module\WPBrowser::seeMessage()
     */
    public function canSeeMessage($classes = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\ConditionalAssertion('seeMessage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Returns WordPress default test cookie object if present.
     * @example
     * ```php
     * // Grab the default WordPress test cookie.
     * $wpTestCookie = $I->grabWordPressTestCookie();
     * // Grab a customized version of the test cookie.
     * $myTestCookie = $I->grabWordPressTestCookie('my_test_cookie');
     * ```
     *
     *
     * @param string $name Optional, overrides the default cookie name.
     *
     * @return mixed Either a cookie object or `null`.
     * @see \Codeception\Module\WPBrowser::grabWordPressTestCookie()
     */
    public function grabWordPressTestCookie($name = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('grabWordPressTestCookie', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Go to a page in the admininstration area of the site.
     *
     * This method will **not** handle authentication to the administration area.
     *
     * @example
     *
     * ```php
     * $I->loginAs('user', 'password');
     * // Go to the plugins management screen.
     * $I->amOnAdminPage('/plugins.php');
     * ```
     *
     * @param string $page The path, relative to the admin area URL, to the page.
     * @see \Codeception\Module\WPBrowser::amOnAdminPage()
     */
    public function amOnAdminPage($page) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amOnAdminPage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Go to the `admin-ajax.php` page to start a synchronous, and blocking, `GET` AJAX request.
     *
     * The method will **not** handle authentication, nonces or authorization.
     *
     * @example
     * ```php
     * $I->amOnAdminAjaxPage(['action' => 'my-action', 'data' => ['id' => 23], 'nonce' => $nonce]);
     * ```
     *
     * @param array|string $queryVars A string or array of query variables to append to the AJAX path.
     * @see \Codeception\Module\WPBrowser::amOnAdminAjaxPage()
     */
    public function amOnAdminAjaxPage($queryVars = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amOnAdminAjaxPage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Go to the cron page to start a synchronous, and blocking, `GET` request to the cron script.
     *
     * @example
     * ```php
     * // Triggers the cron job with an optional query argument.
     * $I->amOnCronPage('/?some-query-var=some-value');
     * ```
     *
     * @param array|string $queryVars A string or array of query variables to append to the AJAX path.
     * @see \Codeception\Module\WPBrowser::amOnCronPage()
     */
    public function amOnCronPage($queryVars = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amOnCronPage', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Go to the admin page to edit the post with the specified ID.
     *
     * The method will **not** handle authentication the admin area.
     *
     * @example
     * ```php
     * $I->loginAsAdmin();
     * $postId = $I->havePostInDatabase();
     * $I->amEditingPostWithId($postId);
     * $I->fillField('post_title', 'Post title');
     * ```
     *
     * @param int $id The post ID.
     * @see \Codeception\Module\WPBrowser::amEditingPostWithId()
     */
    public function amEditingPostWithId($id) {
        return $this->getScenario()->runStep(new \Codeception\Step\Condition('amEditingPostWithId', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Handles and checks exception called inside callback function.
     * Either exception class name or exception instance should be provided.
     *
     * ```php
     * <?php
     * $I->expectException(MyException::class, function() {
     *     $this->doSomethingBad();
     * });
     *
     * $I->expectException(new MyException(), function() {
     *     $this->doSomethingBad();
     * });
     * ```
     * If you want to check message or exception code, you can pass them with exception instance:
     * ```php
     * <?php
     * // will check that exception MyException is thrown with "Don't do bad things" message
     * $I->expectException(new MyException("Don't do bad things"), function() {
     *     $this->doSomethingBad();
     * });
     * ```
     *
     * @deprecated Use expectThrowable() instead
     * @param $exception string or \Exception
     * @param $callback
     * @see \Codeception\Module\Asserts::expectException()
     */
    public function expectException($exception, $callback) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('expectException', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Handles and checks throwables (Exceptions/Errors) called inside the callback function.
     * Either throwable class name or throwable instance should be provided.
     *
     * ```php
     * <?php
     * $I->expectThrowable(MyThrowable::class, function() {
     *     $this->doSomethingBad();
     * });
     *
     * $I->expectThrowable(new MyException(), function() {
     *     $this->doSomethingBad();
     * });
     * ```
     * If you want to check message or throwable code, you can pass them with throwable instance:
     * ```php
     * <?php
     * // will check that throwable MyError is thrown with "Don't do bad things" message
     * $I->expectThrowable(new MyError("Don't do bad things"), function() {
     *     $this->doSomethingBad();
     * });
     * ```
     *
     * @param $throwable string or \Throwable
     * @param $callback
     * @see \Codeception\Module\Asserts::expectThrowable()
     */
    public function expectThrowable($throwable, $callback) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('expectThrowable', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that two variables are equal.
     *
     * @param        $expected
     * @param        $actual
     * @param string $message
     * @param float  $delta
     * @see \Codeception\Module\Asserts::assertEquals()
     */
    public function assertEquals($expected, $actual, $message = null, $delta = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that two variables are not equal
     *
     * @param        $expected
     * @param        $actual
     * @param string $message
     * @param float  $delta
     * @see \Codeception\Module\Asserts::assertNotEquals()
     */
    public function assertNotEquals($expected, $actual, $message = null, $delta = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that two variables are same
     *
     * @param        $expected
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertSame()
     */
    public function assertSame($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertSame', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that two variables are not same
     *
     * @param        $expected
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertNotSame()
     */
    public function assertNotSame($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotSame', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that actual is greater than expected
     *
     * @param        $expected
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertGreaterThan()
     */
    public function assertGreaterThan($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertGreaterThan', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that actual is greater or equal than expected
     *
     * @param        $expected
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertGreaterThanOrEqual()
     */
    public function assertGreaterThanOrEqual($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertGreaterThanOrEqual', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that actual is less than expected
     *
     * @param        $expected
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertLessThan()
     */
    public function assertLessThan($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertLessThan', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that actual is less or equal than expected
     *
     * @param        $expected
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertLessThanOrEqual()
     */
    public function assertLessThanOrEqual($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertLessThanOrEqual', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that haystack contains needle
     *
     * @param        $needle
     * @param        $haystack
     * @param string $message
     * @see \Codeception\Module\Asserts::assertContains()
     */
    public function assertContains($needle, $haystack, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertContains', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that haystack doesn't contain needle.
     *
     * @param        $needle
     * @param        $haystack
     * @param string $message
     * @see \Codeception\Module\Asserts::assertNotContains()
     */
    public function assertNotContains($needle, $haystack, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotContains', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that string match with pattern
     *
     * @param string $pattern
     * @param string $string
     * @param string $message
     * @see \Codeception\Module\Asserts::assertRegExp()
     */
    public function assertRegExp($pattern, $string, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertRegExp', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that string not match with pattern
     *
     * @param string $pattern
     * @param string $string
     * @param string $message
     * @see \Codeception\Module\Asserts::assertNotRegExp()
     */
    public function assertNotRegExp($pattern, $string, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotRegExp', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a string starts with the given prefix.
     *
     * @param string $prefix
     * @param string $string
     * @param string $message
     * @see \Codeception\Module\Asserts::assertStringStartsWith()
     */
    public function assertStringStartsWith($prefix, $string, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertStringStartsWith', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that a string doesn't start with the given prefix.
     *
     * @param string $prefix
     * @param string $string
     * @param string $message
     * @see \Codeception\Module\Asserts::assertStringStartsNotWith()
     */
    public function assertStringStartsNotWith($prefix, $string, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertStringStartsNotWith', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that variable is empty.
     *
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertEmpty()
     */
    public function assertEmpty($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertEmpty', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that variable is not empty.
     *
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertNotEmpty()
     */
    public function assertNotEmpty($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotEmpty', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that variable is NULL
     *
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertNull()
     */
    public function assertNull($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNull', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that variable is not NULL
     *
     * @param        $actual
     * @param string $message
     * @see \Codeception\Module\Asserts::assertNotNull()
     */
    public function assertNotNull($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotNull', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that condition is positive.
     *
     * @param        $condition
     * @param string $message
     * @see \Codeception\Module\Asserts::assertTrue()
     */
    public function assertTrue($condition, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertTrue', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the condition is NOT true (everything but true)
     *
     * @param        $condition
     * @param string $message
     * @see \Codeception\Module\Asserts::assertNotTrue()
     */
    public function assertNotTrue($condition, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotTrue', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that condition is negative.
     *
     * @param        $condition
     * @param string $message
     * @see \Codeception\Module\Asserts::assertFalse()
     */
    public function assertFalse($condition, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertFalse', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks that the condition is NOT false (everything but false)
     *
     * @param        $condition
     * @param string $message
     * @see \Codeception\Module\Asserts::assertNotFalse()
     */
    public function assertNotFalse($condition, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotFalse', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if file exists
     *
     * @param string $filename
     * @param string $message
     * @see \Codeception\Module\Asserts::assertFileExists()
     */
    public function assertFileExists($filename, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertFileExists', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Checks if file doesn't exist
     *
     * @param string $filename
     * @param string $message
     * @see \Codeception\Module\Asserts::assertFileNotExists()
     */
    public function assertFileNotExists($filename, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertFileNotExists', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $expected
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertGreaterOrEquals()
     */
    public function assertGreaterOrEquals($expected, $actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertGreaterOrEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $expected
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertLessOrEquals()
     */
    public function assertLessOrEquals($expected, $actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertLessOrEquals', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertIsEmpty()
     */
    public function assertIsEmpty($actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsEmpty', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $key
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertArrayHasKey()
     */
    public function assertArrayHasKey($key, $actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertArrayHasKey', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $key
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertArrayNotHasKey()
     */
    public function assertArrayNotHasKey($key, $actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertArrayNotHasKey', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $expectedCount
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertCount()
     */
    public function assertCount($expectedCount, $actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertCount', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $class
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertInstanceOf()
     */
    public function assertInstanceOf($class, $actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertInstanceOf', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $class
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertNotInstanceOf()
     */
    public function assertNotInstanceOf($class, $actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotInstanceOf', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * @param $type
     * @param $actual
     * @param $description
     * @see \Codeception\Module\Asserts::assertInternalType()
     */
    public function assertInternalType($type, $actual, $description = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertInternalType', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     * Fails the test with message.
     *
     * @param $message
     * @see \Codeception\Module\Asserts::fail()
     */
    public function fail($message) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('fail', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertStringContainsString()
     */
    public function assertStringContainsString($needle, $haystack, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertStringContainsString', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertStringNotContainsString()
     */
    public function assertStringNotContainsString($needle, $haystack, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertStringNotContainsString', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertStringContainsStringIgnoringCase()
     */
    public function assertStringContainsStringIgnoringCase($needle, $haystack, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertStringContainsStringIgnoringCase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertStringNotContainsStringIgnoringCase()
     */
    public function assertStringNotContainsStringIgnoringCase($needle, $haystack, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertStringNotContainsStringIgnoringCase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsArray()
     */
    public function assertIsArray($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsArray', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsBool()
     */
    public function assertIsBool($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsBool', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsFloat()
     */
    public function assertIsFloat($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsFloat', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsInt()
     */
    public function assertIsInt($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsInt', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNumeric()
     */
    public function assertIsNumeric($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNumeric', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsObject()
     */
    public function assertIsObject($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsObject', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsResource()
     */
    public function assertIsResource($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsResource', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsString()
     */
    public function assertIsString($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsString', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsScalar()
     */
    public function assertIsScalar($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsScalar', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsCallable()
     */
    public function assertIsCallable($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsCallable', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotArray()
     */
    public function assertIsNotArray($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotArray', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotBool()
     */
    public function assertIsNotBool($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotBool', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotFloat()
     */
    public function assertIsNotFloat($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotFloat', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotInt()
     */
    public function assertIsNotInt($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotInt', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotNumeric()
     */
    public function assertIsNotNumeric($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotNumeric', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotObject()
     */
    public function assertIsNotObject($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotObject', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotResource()
     */
    public function assertIsNotResource($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotResource', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotString()
     */
    public function assertIsNotString($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotString', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotScalar()
     */
    public function assertIsNotScalar($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotScalar', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertIsNotCallable()
     */
    public function assertIsNotCallable($actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertIsNotCallable', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertEqualsCanonicalizing()
     */
    public function assertEqualsCanonicalizing($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertEqualsCanonicalizing', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertNotEqualsCanonicalizing()
     */
    public function assertNotEqualsCanonicalizing($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotEqualsCanonicalizing', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertEqualsIgnoringCase()
     */
    public function assertEqualsIgnoringCase($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertEqualsIgnoringCase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertNotEqualsIgnoringCase()
     */
    public function assertNotEqualsIgnoringCase($expected, $actual, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotEqualsIgnoringCase', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertEqualsWithDelta()
     */
    public function assertEqualsWithDelta($expected, $actual, $delta, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertEqualsWithDelta', func_get_args()));
    }

 
    /**
     * [!] Method is generated. Documentation taken from corresponding module.
     *
     *
     * @see \Codeception\Module\Asserts::assertNotEqualsWithDelta()
     */
    public function assertNotEqualsWithDelta($expected, $actual, $delta, $message = null) {
        return $this->getScenario()->runStep(new \Codeception\Step\Action('assertNotEqualsWithDelta', func_get_args()));
    }
}
