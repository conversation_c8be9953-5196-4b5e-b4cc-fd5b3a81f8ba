<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_61164b1a5c816',
	'title' => 'Options - General',
	'fields' => array(
		array(
			'key' => 'field_61979d856e36c',
			'label' => 'Pub Details',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_648c32f6dcb68',
			'label' => 'Brand',
			'name' => 'opt_brand',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 1,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '40',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'hwi' => 'Heartwood Pubs & Inns (master)',
				'wbc' => 'Heartwood Pubs & Inns (child)',
				'bb' => 'Brasserie Blanc',
				'hwc' => 'Heartwood Collection',
			),
			'default_value' => 'wbc',
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'ui' => 1,
			'ajax' => 0,
			'placeholder' => '',
			'create_options' => 0,
			'save_options' => 0,
		),
		array(
			'key' => 'field_61164b2753668',
			'label' => 'Wordmark',
			'name' => 'opt_logo_wordmark',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => 'Gutenberg: Wordmark',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'collection' => 'Heartwood Collection',
				'inns' => 'Heartwood Inns',
				'barley-mow' => 'The Barley Mow',
				'black-horse' => 'The Black Horse',
				'black-swan' => 'The Black Swan',
				'boot' => 'The Boot',
				'britannia' => 'The Britannia',
				'british-queen' => 'The British Queen',
				'cricketers' => 'The Cricketers',
				'hare' => 'The Hare',
				'highwayman' => 'The Highwayman',
				'jobbers-rest' => 'The Jobber\'s Rest',
				'jolly-farmer' => 'The Jolly Farmer',
				'kings-arms' => 'The King\'s Arms',
				'kings-head' => 'The King\'s Head',
				'march-hare' => 'The March Hare',
				'oaks' => 'The Oaks',
				'queens-head' => 'The Queen\'s Head',
				'red-deer' => 'The Red Deer',
				'sun-inn' => 'The Sun Inn',
				'white-bear' => 'The White Bear',
				'white-horse' => 'The White Horse',
				'plough-harrow' => 'The Plough & Harrow',
				'rope-anchor' => 'The Rope & Anchor',
				'coat-bear' => 'The Coat & Bear',
				'quill-scholar' => 'The Quill & Scholar',
				'rising-sun' => 'The Rising Sun',
				'white-hart' => 'The White Hart',
				'royal-forest' => 'The Royal Forest',
				'prince-of-wales' => 'The Prince of Wales',
				'ropemaker' => 'The Ropemaker',
				'old-crown' => 'The Old Crown',
				'ragged-robin' => 'The Ragged Robin',
				'woodman' => 'The Woodman',
				'red-lion' => 'The Red Lion',
				'george-and-dragon' => 'The George & Dragon',
				'potters-heron' => 'The Potters Heron',
			),
			'default_value' => false,
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 1,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
			'create_options' => 0,
			'save_options' => 0,
		),
		array(
			'key' => 'field_648c666f37d45',
			'label' => 'Swing sign',
			'name' => 'opt_logo_sign',
			'aria-label' => '',
			'type' => 'image',
			'instructions' => 'Gutenberg: Swing Sign',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'return_format' => 'array',
			'library' => 'all',
			'min_width' => '',
			'min_height' => '',
			'min_size' => '',
			'max_width' => '',
			'max_height' => '',
			'max_size' => '',
			'mime_types' => '',
			'preview_size' => 'medium',
		),
		array(
			'key' => 'field_648c31dbdcb63',
			'label' => 'Address',
			'name' => 'opt_address',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '{{address_single}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '40',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_648c346ab7638',
			'label' => 'Address (multiple line)',
			'name' => 'opt_address_multiline',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '{{address_multi}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'rows' => 4,
			'placeholder' => '',
			'new_lines' => 'br',
		),
		array(
			'key' => 'field_648c7591d44c8',
			'label' => 'Directions link',
			'name' => 'opt_directions',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => 'https://{{directions}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_648c3279dcb64',
			'label' => 'Town/City',
			'name' => 'opt_town',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '{{publocation}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_648c32a8dcb65',
			'label' => 'County',
			'name' => 'opt_county',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_648c32b9dcb66',
			'label' => 'Telephone',
			'name' => 'opt_phone',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '{{phone}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_648c32d2dcb67',
			'label' => 'Email',
			'name' => 'opt_email',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '{{email}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '35',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_66631b90b08b4',
			'label' => 'Email (Contact forms)',
			'name' => 'opt_email_forms',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '35',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_649ee23c0a2dc',
			'label' => 'Geolocation Latitude',
			'name' => 'opt_geo_lat',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_649ee2620a2dd',
			'label' => 'Geolocation Longitude',
			'name' => 'opt_geo_lng',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_648c3682ac868',
			'label' => 'Atreemo Pub ID',
			'name' => 'atreemo_pub_id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'allow_in_bindings' => 1,
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_67f797c3b2785',
			'label' => 'Atreemo API',
			'name' => 'atreemo_api',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'old' => 'OLD (/Contact/PostContact)',
				'new' => 'NEW (/Customer/Post)',
			),
			'default_value' => 'old',
			'return_format' => 'value',
			'multiple' => 0,
			'allow_null' => 0,
			'allow_in_bindings' => 0,
			'ui' => 0,
			'ajax' => 0,
			'placeholder' => '',
			'create_options' => 0,
			'save_options' => 0,
		),
		array(
			'key' => 'field_648c36b1ac869',
			'label' => 'Disable (global)',
			'name' => 'disable_signup',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '20',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'allow_in_bindings' => 1,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_688b69714e0f2',
			'label' => 'Disable (master site)',
			'name' => 'disable_signup_master',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '20',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'allow_in_bindings' => 1,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_61164b8b302bf',
			'label' => 'Social channels',
			'name' => 'opt_socials',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '{{socials-[facebook, twitter, instagram, linkedin ...]}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'table',
			'pagination' => 0,
			'min' => 0,
			'max' => 4,
			'collapsed' => '',
			'button_label' => 'Add social channel',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_61164b99302c0',
					'label' => 'Provider',
					'name' => 'provider',
					'aria-label' => '',
					'type' => 'select',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '34',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'choices' => array(
						'facebook' => 'Facebook',
						'twitter' => 'Twitter',
						'instagram' => 'Instagram',
						'linkedin' => 'LinkedIn',
					),
					'default_value' => false,
					'return_format' => 'value',
					'multiple' => 0,
					'allow_null' => 0,
					'ui' => 0,
					'ajax' => 0,
					'placeholder' => '',
					'parent_repeater' => 'field_61164b8b302bf',
					'create_options' => 0,
					'save_options' => 0,
				),
				array(
					'key' => 'field_61164c30302c1',
					'label' => 'Link',
					'name' => 'link',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'wpml_cf_preferences' => 1,
					'default_value' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'maxlength' => '',
					'parent_repeater' => 'field_61164b8b302bf',
				),
			),
		),
		array(
			'key' => 'field_64a45460d89e9',
			'label' => 'Pub Features',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_64a4549ad89ea',
			'label' => 'Features (location)',
			'name' => 'opt_features',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'rooms' => 'Accommodation available',
				'private-dining' => 'Private Dining',
				'garden' => 'Garden',
				'dog' => 'Dog friendly',
				'tent' => 'Tent',
				'parking' => 'Parking',
				'private-hire' => 'Private hire',
			),
			'default_value' => array(
			),
			'return_format' => 'array',
			'multiple' => 1,
			'allow_null' => 1,
			'allow_in_bindings' => 1,
			'ui' => 1,
			'ajax' => 0,
			'placeholder' => '',
			'create_options' => 0,
			'save_options' => 0,
		),
		array(
			'key' => 'field_66e43015fac5f',
			'label' => 'Features Carousel',
			'name' => 'opt_features_carousel',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'block',
			'sub_fields' => array(
				array(
					'key' => 'field_66e43041fac60',
					'label' => 'Enabled',
					'name' => 'enabled',
					'aria-label' => '',
					'type' => 'true_false',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'message' => '',
					'default_value' => 0,
					'ui_on_text' => '',
					'ui_off_text' => '',
					'ui' => 1,
				),
				array(
					'key' => 'field_66e457ed85291',
					'label' => 'Title',
					'name' => 'title',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => 'leave empty to default "What\'s on?"',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '70',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_66e43296fac65',
					'label' => 'Items',
					'name' => 'items',
					'aria-label' => '',
					'type' => 'repeater',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'layout' => 'table',
					'pagination' => 0,
					'min' => 0,
					'max' => 0,
					'collapsed' => '',
					'button_label' => 'Add Item',
					'rows_per_page' => 20,
					'sub_fields' => array(
						array(
							'key' => 'field_66e43079fac61',
							'label' => 'Icon',
							'name' => 'icon',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '20',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'choices' => array(
								'drinks' => 'Drinks',
								'dog-walks' => 'Dog walks',
								'child-entertainment' => 'Child\'s Entertainment',
								'evening-entertainment' => 'Evening Entertainment',
								'live-music' => 'Live music',
								'pre-theatre' => 'Pre Theatre',
								'quiz-night' => 'Quiz night',
								'roasts' => 'Roasts',
								'seasonal-menu' => 'Set seasonal menu',
								'steak-night' => 'Steak night',
							),
							'default_value' => false,
							'return_format' => 'value',
							'multiple' => 0,
							'allow_null' => 0,
							'ui' => 0,
							'ajax' => 0,
							'placeholder' => '',
							'create_options' => 0,
							'save_options' => 0,
							'parent_repeater' => 'field_66e43296fac65',
						),
						array(
							'key' => 'field_66e43236fac62',
							'label' => 'Label',
							'name' => 'label',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '20',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'default_value' => '',
							'maxlength' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'parent_repeater' => 'field_66e43296fac65',
						),
						array(
							'key' => 'field_66e43241fac63',
							'label' => 'Link',
							'name' => 'link',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '40',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'default_value' => '',
							'maxlength' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'parent_repeater' => 'field_66e43296fac65',
						),
						array(
							'key' => 'field_66e4326cfac64',
							'label' => 'New tab?',
							'name' => 'new_tab',
							'aria-label' => '',
							'type' => 'true_false',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '20',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'message' => '',
							'default_value' => 0,
							'ui_on_text' => '',
							'ui_off_text' => '',
							'ui' => 1,
							'parent_repeater' => 'field_66e43296fac65',
						),
					),
				),
			),
		),
		array(
			'key' => 'field_678e2138537e1',
			'label' => 'Parking Arrangements',
			'name' => 'opt_parking',
			'aria-label' => '',
			'type' => 'wysiwyg',
			'instructions' => '{{parking_info}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'allow_in_bindings' => 0,
			'tabs' => 'all',
			'toolbar' => 'full',
			'media_upload' => 0,
			'delay' => 0,
		),
		array(
			'key' => 'field_678e21ec537e2',
			'label' => 'Public Transport Links',
			'name' => 'opt_transport',
			'aria-label' => '',
			'type' => 'wysiwyg',
			'instructions' => '{{public_transport_info}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'allow_in_bindings' => 0,
			'tabs' => 'all',
			'toolbar' => 'full',
			'media_upload' => 0,
			'delay' => 0,
		),
		array(
			'key' => 'field_678e224a34467',
			'label' => 'Additional information',
			'name' => 'opt_additional_info',
			'aria-label' => '',
			'type' => 'wysiwyg',
			'instructions' => '{{additional_information}}',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'allow_in_bindings' => 0,
			'tabs' => 'all',
			'toolbar' => 'full',
			'media_upload' => 0,
			'delay' => 0,
		),
		array(
			'key' => 'field_649559ecfbae5',
			'label' => 'Opening Times',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_66cc4ceb4c2a4',
			'label' => 'Temporarily closed',
			'name' => 'opt_pub_closed',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_66cc4d434c2ac',
			'label' => 'Message',
			'name' => 'opt_pub_closed_text',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => 'this will be displayed instead of opening times',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_66cc4ceb4c2a4',
						'operator' => '==',
						'value' => '1',
					),
				),
			),
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'rows' => 4,
			'placeholder' => '',
			'new_lines' => '',
		),
		array(
			'key' => 'field_66cc4d864c2ad',
			'label' => 'Last Day',
			'name' => 'opt_pub_closed_date',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => 'this is the last day when the notice is replacing the opening times',
			'required' => 0,
			'conditional_logic' => array(
				array(
					array(
						'field' => 'field_66cc4ceb4c2a4',
						'operator' => '==',
						'value' => '1',
					),
				),
			),
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'display_format' => 'd/m/Y',
			'return_format' => 'Ymd',
			'first_day' => 1,
		),
		array(
			'key' => 'field_65672d402ed51',
			'label' => 'Opening Times',
			'name' => 'opt_times_new',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'block',
			'pagination' => 0,
			'min' => 0,
			'max' => 0,
			'collapsed' => '',
			'button_label' => 'Add Group',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_65672d402ed52',
					'label' => 'Name',
					'name' => 'name',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '40',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'allow_in_bindings' => 1,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'parent_repeater' => 'field_65672d402ed51',
				),
				array(
					'key' => 'field_6710fb9b64361',
					'label' => 'Hide status',
					'name' => 'hide_status',
					'aria-label' => '',
					'type' => 'true_false',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '20',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'message' => '',
					'default_value' => 0,
					'ui_on_text' => '',
					'ui_off_text' => '',
					'ui' => 1,
					'parent_repeater' => 'field_65672d402ed51',
				),
				array(
					'key' => 'field_6740ada37aa7e',
					'label' => 'Hide from Footer',
					'name' => 'hide_section',
					'aria-label' => '',
					'type' => 'true_false',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '20',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'message' => '',
					'default_value' => 0,
					'allow_in_bindings' => 1,
					'ui_on_text' => '',
					'ui_off_text' => '',
					'ui' => 1,
					'parent_repeater' => 'field_65672d402ed51',
				),
				array(
					'key' => 'field_67ab07914c0b4',
					'label' => 'Hide entirely',
					'name' => 'hide_section_entirely',
					'aria-label' => '',
					'type' => 'true_false',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '20',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'message' => '',
					'default_value' => 0,
					'allow_in_bindings' => 1,
					'ui_on_text' => '',
					'ui_off_text' => '',
					'ui' => 1,
					'parent_repeater' => 'field_65672d402ed51',
				),
				array(
					'key' => 'field_65672d402ed53',
					'label' => 'Time entries',
					'name' => 'entries',
					'aria-label' => '',
					'type' => 'repeater',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'layout' => 'table',
					'min' => 0,
					'max' => 0,
					'collapsed' => '',
					'button_label' => 'Add Time',
					'rows_per_page' => 20,
					'sub_fields' => array(
						array(
							'key' => 'field_65672d402ed54',
							'label' => 'Label',
							'name' => 'label',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'default_value' => '',
							'maxlength' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'parent_repeater' => 'field_65672d402ed53',
						),
						array(
							'key' => 'field_65672d402ed55',
							'label' => 'From',
							'name' => 'from',
							'aria-label' => '',
							'type' => 'time_picker',
							'instructions' => '',
							'required' => 1,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'display_format' => 'g:i a',
							'return_format' => 'H:i:s',
							'parent_repeater' => 'field_65672d402ed53',
						),
						array(
							'key' => 'field_65672d402ed56',
							'label' => 'To',
							'name' => 'to',
							'aria-label' => '',
							'type' => 'time_picker',
							'instructions' => '',
							'required' => 1,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'display_format' => 'g:i a',
							'return_format' => 'H:i:s',
							'parent_repeater' => 'field_65672d402ed53',
						),
						array(
							'key' => 'field_65672d412ed57',
							'label' => 'Closed?',
							'name' => 'closed',
							'aria-label' => '',
							'type' => 'true_false',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'message' => '',
							'default_value' => 0,
							'ui_on_text' => '',
							'ui_off_text' => '',
							'ui' => 1,
							'parent_repeater' => 'field_65672d402ed53',
						),
						array(
							'key' => 'field_65672d412ed58',
							'label' => 'Note',
							'name' => 'note',
							'aria-label' => '',
							'type' => 'text',
							'instructions' => '',
							'required' => 0,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'default_value' => '',
							'maxlength' => '',
							'placeholder' => '',
							'prepend' => '',
							'append' => '',
							'parent_repeater' => 'field_65672d402ed53',
						),
						array(
							'key' => 'field_65672d412ed59',
							'label' => 'Days',
							'name' => 'days',
							'aria-label' => '',
							'type' => 'select',
							'instructions' => '',
							'required' => 1,
							'conditional_logic' => 0,
							'wrapper' => array(
								'width' => '30',
								'class' => '',
								'id' => '',
							),
							'show_in_graphql' => 1,
							'choices' => array(
								1 => 'Monday',
								2 => 'Tuesday',
								3 => 'Wednesday',
								4 => 'Thursday',
								5 => 'Friday',
								6 => 'Saturday',
								0 => 'Sunday',
							),
							'default_value' => array(
							),
							'return_format' => 'value',
							'multiple' => 1,
							'allow_null' => 0,
							'ui' => 1,
							'ajax' => 0,
							'placeholder' => '',
							'create_options' => 0,
							'save_options' => 0,
							'parent_repeater' => 'field_65672d402ed53',
						),
					),
					'parent_repeater' => 'field_65672d402ed51',
				),
			),
		),
		array(
			'key' => 'field_6597e74a1b15c',
			'label' => 'Newsletter signup',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_650825b4a1c2d',
			'label' => 'Signup Button: Label',
			'name' => 'opt_footer_btn',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(leave empty to use default "Sign up to our newsletter")',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_652e667deb9da',
			'label' => 'Signup Button: Link',
			'name' => 'opt_footer_btn_link',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(leave empty to use default Signup page)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_652e66a2eb9db',
			'label' => 'Signup Button: New Tab?',
			'name' => 'opt_footer_btn_newtab',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '20',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_6597e7b003538',
			'label' => 'Floating CTA',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_64b6575f23952',
			'label' => 'Title',
			'name' => 'opt_signup_title',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '60',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_655723ad5296e',
			'label' => 'Disable CTA',
			'name' => 'opt_signup_disable',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '40',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_64b6576d23953',
			'label' => 'Content',
			'name' => 'opt_signup_content',
			'aria-label' => '',
			'type' => 'textarea',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'rows' => 4,
			'placeholder' => '',
			'new_lines' => 'br',
		),
		array(
			'key' => 'field_64b6578823954',
			'label' => 'Link',
			'name' => 'opt_signup_link',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '/signup/',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6543804e659f5',
			'label' => 'Label',
			'name' => 'opt_signup_label',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => 'Subscribe',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_64b657a523955',
			'label' => 'New Tab?',
			'name' => 'opt_signup_newtab',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '20',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_648c7f6199c13',
			'label' => 'Footer',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_648c7f7d99c14',
			'label' => 'Featured page',
			'name' => 'opt_featured_page',
			'aria-label' => '',
			'type' => 'relationship',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'post_type' => array(
				0 => 'page',
			),
			'post_status' => array(
				0 => 'publish',
			),
			'taxonomy' => '',
			'filters' => array(
				0 => 'search',
			),
			'return_format' => 'object',
			'min' => '',
			'max' => 1,
			'elements' => '',
			'bidirectional' => 0,
			'bidirectional_target' => array(
			),
		),
		array(
			'key' => 'field_6489935ec4301',
			'label' => 'Integrations',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_648997d62d6bc',
			'label' => 'Cookiebot ID',
			'name' => 'opt_cookiebot',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(example: ae13daba-0b29-4a9c-930b-6546bf0522bd)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_64899378c4302',
			'label' => 'Google Tag Manager',
			'name' => 'opt_gtm',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(only ID, example: GTM-P4FCHFZ)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_648993bcc4303',
			'label' => 'Facebook Pixel',
			'name' => 'opt_pixel',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(only ID, example: 829773804199314)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6736474a840ca',
			'label' => 'Bing verification',
			'name' => 'opt_bing',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '(only ID, taken from this snippet &lt;meta name="msvalidate.01" content="XXXXXXXXXXXXXX"&gt; )',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6502d6993c1a3',
			'label' => 'Coming soon',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_67b745f8f3a9d',
			'label' => 'Locations Listing',
			'name' => '',
			'aria-label' => '',
			'type' => 'message',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => 'heading',
				'id' => '',
			),
			'message' => '',
			'new_lines' => 'wpautop',
			'esc_html' => 0,
		),
		array(
			'key' => 'field_651eafc32bc60',
			'label' => 'Coming soon',
			'name' => 'opt_coming_soon',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '(Show this INN on <a href="//heartwoodinns.com/locations" target="_blank">locations listing</a> as coming soon)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_652434699f460',
			'label' => 'Custom Button',
			'name' => '',
			'aria-label' => '',
			'type' => 'message',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'message' => '',
			'new_lines' => 'wpautop',
			'esc_html' => 0,
		),
		array(
			'key' => 'field_6524341781f8a',
			'label' => 'Label',
			'name' => 'opt_cutom_button_label',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6524343b81f8b',
			'label' => 'Link',
			'name' => 'opt_cutom_button_link',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_6524344881f8c',
			'label' => 'New tab?',
			'name' => 'opt_cutom_button_newtab',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '20',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_67b746baf3a9e',
			'label' => 'Coming soon page (Maintenance view)',
			'name' => '',
			'aria-label' => '',
			'type' => 'message',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => 'heading',
				'id' => '',
			),
			'message' => '',
			'new_lines' => 'wpautop',
			'esc_html' => 0,
		),
		array(
			'key' => 'field_6502d77efc5f0',
			'label' => 'Body copy',
			'name' => 'opt_maintenance_body',
			'aria-label' => '',
			'type' => 'wysiwyg',
			'instructions' => '(If this field is empty, "We\'re having an online makeover. Please check back soon..." will be displayed.)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'tabs' => 'all',
			'toolbar' => 'full',
			'media_upload' => 0,
			'delay' => 1,
		),
		array(
			'key' => 'field_67b74c783b675',
			'label' => 'Block: Sign up',
			'name' => 'opt_splash_signup',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'block',
			'sub_fields' => array(
				array(
					'key' => 'field_67b74c8e3b676',
					'label' => 'Show this block',
					'name' => 'show',
					'aria-label' => '',
					'type' => 'true_false',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'message' => '',
					'default_value' => 0,
					'allow_in_bindings' => 0,
					'ui_on_text' => '',
					'ui_off_text' => '',
					'ui' => 1,
				),
				array(
					'key' => 'field_67b74cd13b677',
					'label' => 'Title',
					'name' => 'title',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '50',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => 'We\'re opening soon!',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_67b872c2ab925',
					'label' => 'Source ID',
					'name' => 'source',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '20',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_67b74cef3b678',
					'label' => 'Text',
					'name' => 'text',
					'aria-label' => '',
					'type' => 'textarea',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'rows' => 4,
					'placeholder' => '',
					'new_lines' => 'br',
				),
			),
		),
		array(
			'key' => 'field_67b74dd776111',
			'label' => 'Block: Recruitment',
			'name' => 'opt_splash_recruitment',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'block',
			'sub_fields' => array(
				array(
					'key' => 'field_67b74dd776112',
					'label' => 'Show this block',
					'name' => 'show',
					'aria-label' => '',
					'type' => 'true_false',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'message' => '',
					'default_value' => 0,
					'allow_in_bindings' => 0,
					'ui_on_text' => '',
					'ui_off_text' => '',
					'ui' => 1,
				),
				array(
					'key' => 'field_67b74dd776113',
					'label' => 'Title',
					'name' => 'title',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '70',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => 'We are looking for superstars!',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_67b74dd776114',
					'label' => 'Text',
					'name' => 'text',
					'aria-label' => '',
					'type' => 'textarea',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'rows' => 4,
					'placeholder' => '',
					'new_lines' => 'br',
				),
				array(
					'key' => 'field_67b74e5b76115',
					'label' => 'Button label',
					'name' => 'btn_label',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => 'View available positions',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_67b74e9376116',
					'label' => 'Button link',
					'name' => 'btn_link',
					'aria-label' => '',
					'type' => 'url',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '70',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'allow_in_bindings' => 1,
					'placeholder' => '',
				),
			),
		),
		array(
			'key' => 'field_67b750431e4ac',
			'label' => 'Block: Sample Menus',
			'name' => 'opt_splash_menus',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'block',
			'sub_fields' => array(
				array(
					'key' => 'field_67b750431e4ad',
					'label' => 'Show this block',
					'name' => 'show',
					'aria-label' => '',
					'type' => 'true_false',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'message' => '',
					'default_value' => 0,
					'allow_in_bindings' => 0,
					'ui_on_text' => '',
					'ui_off_text' => '',
					'ui' => 1,
				),
				array(
					'key' => 'field_67b750431e4ae',
					'label' => 'Title',
					'name' => 'title',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '70',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => 'Get a taste of Heartwood',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_67b750431e4af',
					'label' => 'Text',
					'name' => 'text',
					'aria-label' => '',
					'type' => 'textarea',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'rows' => 4,
					'placeholder' => '',
					'new_lines' => 'br',
				),
				array(
					'key' => 'field_67b750431e4b0',
					'label' => 'Button label',
					'name' => 'btn_label',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => 'View sample menus',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_67b750441e4b1',
					'label' => 'Button link',
					'name' => 'btn_link',
					'aria-label' => '',
					'type' => 'url',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '70',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'allow_in_bindings' => 1,
					'placeholder' => '',
				),
			),
		),
		array(
			'key' => 'field_67b750881e4b2',
			'label' => 'Block: Room bookings',
			'name' => 'opt_splash_room',
			'aria-label' => '',
			'type' => 'group',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'block',
			'sub_fields' => array(
				array(
					'key' => 'field_67b750881e4b3',
					'label' => 'Show this block',
					'name' => 'show',
					'aria-label' => '',
					'type' => 'true_false',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'message' => '',
					'default_value' => 0,
					'allow_in_bindings' => 0,
					'ui_on_text' => '',
					'ui_off_text' => '',
					'ui' => 1,
				),
				array(
					'key' => 'field_67b750881e4b4',
					'label' => 'Title',
					'name' => 'title',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '70',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_67b750881e4b5',
					'label' => 'Text',
					'name' => 'text',
					'aria-label' => '',
					'type' => 'textarea',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'rows' => 4,
					'placeholder' => '',
					'new_lines' => 'br',
				),
				array(
					'key' => 'field_67b750881e4b6',
					'label' => 'Button label',
					'name' => 'btn_label',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '30',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => 'View available rooms',
					'maxlength' => '',
					'allow_in_bindings' => 0,
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
				),
				array(
					'key' => 'field_67b750881e4b7',
					'label' => 'Button link',
					'name' => 'btn_link',
					'aria-label' => '',
					'type' => 'url',
					'instructions' => '',
					'required' => 0,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '70',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'allow_in_bindings' => 1,
					'placeholder' => '',
				),
			),
		),
		array(
			'key' => 'field_686bb8d4ec9a9',
			'label' => 'A/B Testing',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_686bb8f7ec9aa',
			'label' => 'Allowed flags',
			'name' => 'opt_allowed_flags',
			'aria-label' => '',
			'type' => 'select',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'choices' => array(
				'bookingWidgetV2' => 'Booking widget v2',
			),
			'default_value' => array(
			),
			'return_format' => 'value',
			'multiple' => 1,
			'allow_null' => 1,
			'allow_in_bindings' => 0,
			'ui' => 1,
			'ajax' => 0,
			'create_options' => 0,
			'placeholder' => '',
			'save_options' => 0,
		),
		array(
			'key' => 'field_6891dd1b64452',
			'label' => 'Loyalty',
			'name' => '',
			'aria-label' => '',
			'type' => 'tab',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'placement' => 'left',
			'endpoint' => 0,
			'selected' => 0,
		),
		array(
			'key' => 'field_6891dd2a64453',
			'label' => 'Loyalty button: show',
			'name' => 'opt_loyalty_btn',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => 'this will replace the sign up button in the header',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '40',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'allow_in_bindings' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_6891ddb964454',
			'label' => 'Loyalty button: link',
			'name' => 'opt_loyalty_btn_link',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '60',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
		),
		array(
			'key' => 'field_6891f199a00b3',
			'label' => 'App Store link',
			'name' => 'opt_loyalty_ios',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => 'use [loyalty_app_store] shortcode to display badge',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
		),
		array(
			'key' => 'field_6891f1d2a00b4',
			'label' => 'Play Store link',
			'name' => 'opt_loyalty_android',
			'aria-label' => '',
			'type' => 'url',
			'instructions' => 'use [loyalty_play_store] shortcode to display badge',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'allow_in_bindings' => 0,
			'placeholder' => '',
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'options_page',
				'operator' => '==',
				'value' => 'heartwood-settings',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 1,
	'graphql_field_name' => 'optGeneral',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );