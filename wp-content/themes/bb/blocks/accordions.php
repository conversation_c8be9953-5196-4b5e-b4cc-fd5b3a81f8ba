<?php
/**
 * Accordions - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'accordions-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-accordions'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// === Get Accordions
$accordions = get_field('accordions');
if( $accordions ):
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
<div class="accordion" data-bs-accordion="true">
    <?php
    $i=1;
    foreach( $accordions as $accordion ): ?>
        <div class="card" data-bs-accordion-item="true" data-key="<?= $i ?>">
            <div class="card-header mb-2 p-0" data-bs-accordion-header="true">
                <?= $accordion['title'] ?>
            </div>

            <div class="collapse" aria-labelledby="heading-<?= $i ?>" data-bs-accordion-body="true">
                <div class="card-body pt-0 pb-5">
                    <?= $accordion['content'] ?>
                </div>
            </div>
        </div>
    <?php
    $i++;
    endforeach; ?>
</div>
</div><!-- .block-accordions -->
<?php
else:
    echo '<p class="alert alert-info text-center">Please add some content</p>';
endif;
?>