[{"key": "group_60468a2b40d13", "title": "ACF Docs", "fields": [{"key": "field_60468a428ad20", "label": "Text", "name": "text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_60468c101f5bc", "label": "Text Area", "name": "text_area", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": ""}, {"key": "field_60468c261f5bd", "label": "Number", "name": "number", "type": "number", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "prepend": "", "append": "", "min": "", "max": "", "step": ""}, {"key": "field_60468c7d1f5be", "label": "Range", "name": "range", "type": "range", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "min": "", "max": "", "step": "", "prepend": "", "append": ""}, {"key": "field_60468d7ed5271", "label": "Email", "name": "email", "type": "email", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_60468db4a3624", "label": "Url", "name": "url", "type": "url", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": ""}, {"key": "field_60468dd9a7390", "label": "Password", "name": "password", "type": "password", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_60468e38c3039", "label": "Image", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_6046909b38734", "label": "File", "name": "file", "type": "file", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "library": "all", "min_size": "", "max_size": "", "mime_types": ""}, {"key": "field_604690bb38735", "label": "Wysiwyg", "name": "wysiwyg", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_604690cd38736", "label": "<PERSON><PERSON><PERSON>", "name": "oembed", "type": "oembed", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "width": "", "height": ""}, {"key": "field_6047cac3147ba", "label": "Gallery", "name": "gallery", "type": "gallery", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "preview_size": "medium", "insert": "append", "library": "all", "min": "", "max": "", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_604690da38737", "label": "Select", "name": "select", "type": "select", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"choice_1": "Choice 1", "choice_2": "Choice 2"}, "default_value": false, "allow_null": 0, "multiple": 0, "ui": 0, "return_format": "value", "ajax": 0, "placeholder": ""}, {"key": "field_60469107346a9", "label": "Checkbox", "name": "checkbox", "type": "checkbox", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"choice_1": "Choice 1", "choice_2": "Choice 2"}, "allow_custom": 0, "default_value": [], "layout": "vertical", "toggle": 0, "return_format": "value", "save_custom": 0}, {"key": "field_6046914753efc", "label": "Radio Button", "name": "radio_button", "type": "radio", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"choice_1": "Choice 1", "choice_2": "Choice 2"}, "allow_null": 0, "other_choice": 0, "default_value": "", "layout": "vertical", "return_format": "value", "save_other_choice": 0}, {"key": "field_6046917b53efd", "label": "Button Group", "name": "button_group", "type": "button_group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "choices": {"choice_1": "Choice 1", "choice_2": "Choice 2"}, "allow_null": 0, "default_value": "", "layout": "horizontal", "return_format": "value"}, {"key": "field_604691d753ce6", "label": "True False", "name": "true_false", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_6046928a53ce7", "label": "Link", "name": "link", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array"}, {"key": "field_604692a202533", "label": "Post Object", "name": "post_object", "type": "post_object", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "post_type": ["post", "page"], "taxonomy": "", "allow_null": 0, "multiple": 1, "return_format": "object", "ui": 1}, {"key": "field_60469560e9da6", "label": "Page Link", "name": "page_link", "type": "page_link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "post_type": ["post", "page"], "taxonomy": "", "allow_null": 0, "allow_archives": 1, "multiple": 0}, {"key": "field_60469ad3e9da7", "label": "Relationship", "name": "relationship", "type": "relationship", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "post_type": ["post", "page"], "taxonomy": "", "filters": ["search", "post_type", "taxonomy"], "elements": "", "min": "", "max": "", "return_format": "object"}, {"key": "field_60469bf265bd6", "label": "Taxonomy", "name": "taxonomy", "type": "taxonomy", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "taxonomy": "category", "field_type": "checkbox", "add_term": 1, "save_terms": 0, "load_terms": 0, "return_format": "id", "multiple": 0, "allow_null": 0}, {"key": "field_60469c1665bd7", "label": "User", "name": "user", "type": "user", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "role": "", "allow_null": 0, "multiple": 1, "return_format": "array"}, {"key": "field_60469c2065bd8", "label": "Google Map", "name": "google_map", "type": "google_map", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "center_lat": "", "center_lng": "", "zoom": "", "height": ""}, {"key": "field_60469d0dc197a", "label": "Date Picker", "name": "date_picker", "type": "date_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "display_format": "d/m/Y", "return_format": "d/m/Y", "first_day": 1}, {"key": "field_60469d19c197b", "label": "Date Time Picker", "name": "date_time_picker", "type": "date_time_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "display_format": "d/m/Y g:i a", "return_format": "d/m/Y g:i a", "first_day": 1}, {"key": "field_60469d27c197c", "label": "Time Picker", "name": "time_picker", "type": "time_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "display_format": "g:i a", "return_format": "g:i a"}, {"key": "field_60469d34c197d", "label": "Color Picker", "name": "color_picker", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": ""}, {"key": "field_60469d5933bce", "label": "Group", "name": "group", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "layout": "block", "sub_fields": [{"key": "field_6047ecb8e5cbc", "label": "Text Field In Group", "name": "text_field_in_group", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_6047eccce5cbd", "label": "Text Area Field In Group", "name": "text_area_field_in_group", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "maxlength": "", "rows": "", "new_lines": ""}]}, {"key": "field_6047cb430101c", "label": "<PERSON><PERSON><PERSON>", "name": "repeater", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "", "sub_fields": [{"key": "field_6047cb620101d", "label": "Text Field In Repeater", "name": "text_field_in_repeater", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_6047cb740101e", "label": "Image Field In Repeater", "name": "image_field_in_repeater", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}]}, {"key": "field_6047cb92951ce", "label": "Flexible Content", "name": "flexible_content", "type": "flexible_content", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "layouts": {"layout_6047cb980608a": {"key": "layout_6047cb980608a", "name": "layout_one", "label": "Layout One", "display": "block", "sub_fields": [{"key": "field_6047cc58951cf", "label": "Text", "name": "text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_6047cc9b951d0", "label": "Another Text Field", "name": "another_text_field", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}], "min": "", "max": ""}, "layout_6047eee191715": {"key": "layout_6047eee191715", "name": "layout_two", "label": "Layout Two", "display": "block", "sub_fields": [{"key": "field_6047eeeb91716", "label": "Image", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}], "min": "", "max": ""}, "layout_6047eefa91717": {"key": "layout_6047eefa91717", "name": "layout_three", "label": "Layout Three", "display": "block", "sub_fields": [{"key": "field_6047ef0291718", "label": "Gallery", "name": "gallery", "type": "gallery", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "return_format": "array", "preview_size": "medium", "insert": "append", "library": "all", "min": "", "max": "", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}], "min": "", "max": ""}}, "button_label": "Add Row", "min": "", "max": ""}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "ACF Documentation Examples", "show_in_graphql": 1, "graphql_field_name": "acfDocs"}]