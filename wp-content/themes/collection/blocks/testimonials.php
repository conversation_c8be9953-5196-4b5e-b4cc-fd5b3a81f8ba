<?php
/**
 * Testimonials - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'testimonials-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-testimonials my-150'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// === Get Testimonials
$posts_count = get_field('testi_count') ?: 3;
$posts = get_posts(array(
    'post_type' => 'testimonial',
    'posts_per_page' => is_admin() && $posts_count>3 ? 3 : $posts_count
));
if( !$posts ) return;
$settings = array(
    'dots' => true,
    'infinite' => true,
    'fade' => true,
    'arrows' => false,
    'autoplay' => true,
    'speed' => 1500,
    'autoplaySpeed' => 6000,
    'slidesToShow' => 1,
    'slidesToScroll' => 1,
    'dotsClass' => 'slick-dots col-12 col-lg-10 offset-lg-1 text-center text-lg-start'
);
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?> <?= $image_align ?>">

    <div class="testimonials-carousel <?= is_admin() ? 'row' : '' ?>" data-slick="true" data-settings="<?= htmlspecialchars(json_encode($settings), ENT_QUOTES, 'UTF-8') ?>">
        <?php foreach( $posts as $post ):
            $title = get_the_title( $post );
            $cite = get_field('cite', $post);
            $content = get_field('content', $post);
            ?>
            <div class="slide <?= is_admin() ? 'col-4' : '' ?>">
                <div class="card mb-30">
                    <div class="row">
                        <div class="<?= !is_admin() ? 'col-12 col-lg-10 offset-lg-1' : '' ?>">
                        <div class="card-body">

                                <figure class="text-center text-lg-start">
                                    <blockquote class="blockquote">
                                        <?= $content ?>
                                    </blockquote>
                                    <figcaption class="blockquote-footer">
                                        <em><?= $title ?> - <?= $cite ?></em>
                                    </figcaption>
                                </figure>

                        </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

</div><!-- .block-testimonials -->