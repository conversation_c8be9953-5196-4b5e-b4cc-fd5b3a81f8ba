<?php
/**
 * Responsive Image - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'responsive-embed-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-embed'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

$embed_url = get_field('embed');
$ratio = get_field('ratio') ?: '16x9';
if( !$embed_url ) {
    return;
}
?>
<?php if ( ! $is_preview ) { ?>
    <div
        <?php
        echo wp_kses_data(
            get_block_wrapper_attributes(
                array(
                    'id'    => esc_attr($id),
                    'class' => esc_attr( $class_name ),
                )
            )
        );
        ?>
    >
<?php } ?>

    <div class="ratio ratio-<?= $ratio ?>">
        <iframe src="<?= $embed_url ?>"></iframe>
    </div>

<?php if ( ! $is_preview ) { ?>
    </div><!-- .block-image -->
<?php } ?>