<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_6492f5e19eb38',
	'title' => 'Single Post - Details',
	'fields' => array(
		array(
			'key' => 'field_6492f5e27d9d1',
			'label' => 'Event date / Offer expiration date',
			'name' => 'expiry_date',
			'aria-label' => '',
			'type' => 'date_time_picker',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'display_format' => 'j F Y g:ia',
			'return_format' => 'j F Y \\a\\t g:ia',
			'first_day' => 1,
		),
		array(
			'key' => 'field_64a42292c956e',
			'label' => 'Terms & condition',
			'name' => 'terms',
			'aria-label' => '',
			'type' => 'wysiwyg',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'tabs' => 'all',
			'toolbar' => 'full',
			'media_upload' => 0,
			'delay' => 1,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'post_category',
				'operator' => '==',
				'value' => 'category:events',
			),
		),
		array(
			array(
				'param' => 'post_category',
				'operator' => '==',
				'value' => 'category:offers',
			),
		),
	),
	'menu_order' => 0,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 1,
	'graphql_field_name' => 'singlePost',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );
