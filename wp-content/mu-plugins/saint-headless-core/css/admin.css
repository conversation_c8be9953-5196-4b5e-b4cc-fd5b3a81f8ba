#wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  width: min-content;
  gap: 0;
  z-index: unset;
  padding: 0 0 6px;
}
#wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list li {
  z-index: unset;
}
#wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .hover {
  background: #7B8D96;
}
#wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .ab-sub-wrapper {
  width: 100%;
  box-shadow: none;
}
#wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .ab-sub-wrapper ul {
  background: #7B8D96;
}
#wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .wp-admin-bar-arrow,
#wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .blavatar {
  display: none;
}

.admin-color-fresh #wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .hover,
.admin-color-fresh #wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .ab-sub-wrapper ul {
  background: #1F2327;
}

.acf-fields .acf-field-message.heading {
  background-color: #e9e8e8;
}
.acf-fields .acf-field-message.heading .acf-label {
  margin-bottom: 0;
}

/*# sourceMappingURL=admin.css.map */
