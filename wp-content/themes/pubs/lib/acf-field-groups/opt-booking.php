<?php
add_action( 'acf/include_fields', function() {
	if ( ! function_exists( 'acf_add_local_field_group' ) ) {
		return;
	}

	acf_add_local_field_group( array(
	'key' => 'group_5c260d8ad0236',
	'title' => 'Options - Booking (Zonal)',
	'fields' => array(
		array(
			'key' => 'field_5a044ad19c7c4',
			'label' => 'Disable booking',
			'name' => 'disable_booking',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_5a04gd5t4grc4',
			'label' => 'Enable promo codes',
			'name' => 'use_promocodes',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => 'displays non facebook message and turns on promos codes conditions checkup.. the codes need to be supplied to saint',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '40',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'message' => '',
			'default_value' => 0,
			'ui_on_text' => '',
			'ui_off_text' => '',
			'ui' => 1,
		),
		array(
			'key' => 'field_66dae3be38cc9',
			'label' => 'MinDate',
			'name' => 'zonalevents_min_date',
			'aria-label' => '',
			'type' => 'date_picker',
			'instructions' => 'this is the first date we will allow for bookings',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '30',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'display_format' => 'd/m/Y',
			'return_format' => 'Y-m-d',
			'first_day' => 1,
		),
		array(
			'key' => 'field_6329c5104bf40',
			'label' => 'Zonal Events',
			'name' => '',
			'aria-label' => '',
			'type' => 'accordion',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'open' => 1,
			'multi_expand' => 0,
			'endpoint' => 0,
		),
		array(
			'key' => 'field_60c9f7c2261b5',
			'label' => 'Location (outlet) ID',
			'name' => 'zonalevents_rest_id',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60e41859fe4e7',
			'label' => 'Occasion - default ID',
			'name' => 'zonalevents_default_occasion',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_6576d90de114b',
			'label' => 'Occasion - Rooms Bridge (Resident Table Booking)',
			'name' => 'zonalevents_default_occasion_rooms',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'default_value' => '',
			'maxlength' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
		),
		array(
			'key' => 'field_64e613134b684',
			'label' => 'Excluded occasions (only Enquiry widget)',
			'name' => 'zonalevents_excluded_occasion',
			'aria-label' => '',
			'type' => 'repeater',
			'instructions' => '(this is global field, use only on HWI master site)',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'show_in_graphql' => 1,
			'layout' => 'table',
			'pagination' => 0,
			'min' => 0,
			'max' => 0,
			'collapsed' => '',
			'button_label' => 'Add occasion',
			'rows_per_page' => 20,
			'sub_fields' => array(
				array(
					'key' => 'field_64e613134b685',
					'label' => 'Name',
					'name' => 'name',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '25',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'parent_repeater' => 'field_64e613134b684',
				),
				array(
					'key' => 'field_64e613134b686',
					'label' => 'ID',
					'name' => 'id',
					'aria-label' => '',
					'type' => 'text',
					'instructions' => '',
					'required' => 1,
					'conditional_logic' => 0,
					'wrapper' => array(
						'width' => '',
						'class' => '',
						'id' => '',
					),
					'show_in_graphql' => 1,
					'default_value' => '',
					'maxlength' => '',
					'placeholder' => '',
					'prepend' => '',
					'append' => '',
					'parent_repeater' => 'field_64e613134b684',
				),
			),
		),
		array(
			'key' => 'field_60e416874da46',
			'label' => 'Menus - default ID',
			'name' => 'zonalevents_default_menu',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60e416d54da47',
			'label' => 'Areas - default ID',
			'name' => 'zonalevents_default_area',
			'aria-label' => '',
			'type' => 'text',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'placeholder' => '',
			'prepend' => '',
			'append' => '',
			'maxlength' => '',
		),
		array(
			'key' => 'field_60e416e54da48',
			'label' => 'Menus - show in booking widget',
			'name' => 'zonalevents_default_menu_show',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '34',
				'class' => '',
				'id' => '',
			),
			'message' => '',
			'default_value' => 1,
			'ui' => 1,
			'ui_on_text' => '',
			'ui_off_text' => '',
		),
		array(
			'key' => 'field_60e417264da49',
			'label' => 'Areas - show in booking widget',
			'name' => 'zonalevents_default_area_show',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'message' => '',
			'default_value' => 1,
			'ui' => 1,
			'ui_on_text' => '',
			'ui_off_text' => '',
		),
		array(
			'key' => 'field_60e81cd578104',
			'label' => 'Upsells - show in booking widget',
			'name' => 'zonalevents_default_upsell_show',
			'aria-label' => '',
			'type' => 'true_false',
			'instructions' => '',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '33',
				'class' => '',
				'id' => '',
			),
			'message' => '',
			'default_value' => 1,
			'ui' => 1,
			'ui_on_text' => '',
			'ui_off_text' => '',
		),
		array(
			'key' => 'field_60faf87fe7a0c',
			'label' => 'Booking confirmation info',
			'name' => 'zonalevents_confirmation_info',
			'aria-label' => '',
			'type' => 'wysiwyg',
			'instructions' => '(this message to be displayed after successful booking confirmation)
		Priority: Pub first, then WB master site, if none set then default app message.',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'tabs' => 'all',
			'toolbar' => 'full',
			'media_upload' => 0,
			'delay' => 0,
		),
		array(
			'key' => 'field_6388937163d3d',
			'label' => 'Enquiry page info',
			'name' => 'zonalevents_enquiry_info',
			'aria-label' => '',
			'type' => 'wysiwyg',
			'instructions' => '(this message to be displayed at Enquiry page)
 Priority: Pub first, then WB master site, if none set then default app message.',
			'required' => 0,
			'conditional_logic' => 0,
			'wrapper' => array(
				'width' => '50',
				'class' => '',
				'id' => '',
			),
			'default_value' => '',
			'tabs' => 'all',
			'toolbar' => 'full',
			'media_upload' => 0,
			'delay' => 0,
		),
	),
	'location' => array(
		array(
			array(
				'param' => 'options_page',
				'operator' => '==',
				'value' => 'heartwood-settings',
			),
		),
	),
	'menu_order' => 1,
	'position' => 'normal',
	'style' => 'default',
	'label_placement' => 'top',
	'instruction_placement' => 'label',
	'hide_on_screen' => '',
	'active' => true,
	'description' => '',
	'show_in_rest' => 0,
	'show_in_graphql' => 1,
	'graphql_field_name' => 'optBooking',
	'map_graphql_types_from_location_rules' => 0,
	'graphql_types' => '',
) );
} );
