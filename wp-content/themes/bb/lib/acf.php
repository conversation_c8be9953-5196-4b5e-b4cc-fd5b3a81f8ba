<?php
/**
 * Register Site Options page
 */
add_action( 'acf/init', function(){
	if( function_exists('acf_add_options_page') ) {

		acf_add_options_page(array(
			'page_title' 	=> 'BB Settings',
			'menu_title'	=> 'BB Settings',
			'menu_slug' 	=> 'heartwood-settings',
			'capability'	=> 'edit_theme_settings',
			'redirect'		=> false,
			'show_in_graphql' => true,
		));

	}
} );

add_action( 'acf/init', function() {
	acf_add_options_page( array(
		'page_title' => 'BB SEO',
		'menu_slug' => 'heartwood-seo',
		'parent_slug' => 'wpseo_dashboard',
		'position' => '',
		'redirect' => false,
		'capability' => 'wpseo_manage_options',
	) );
} );

/**
 * Pre-fill some of the ACF fields
 * WP admin only
 */
if( is_admin() ) {
	add_filter('acf/load_value/name=room-btn-rooms', function ( $value, $post_id, $field ) {
		global $post;
		$post_id = $post->ID;
		$default_val = get_field('guestline_room_id', $post_id);
		if( empty($value) && $default_val ) $value = $default_val;
		return $value;
	}, 10, 3);
}

add_action('after_setup_theme', function(){

});

if( !is_admin() ) {
	add_filter( 'acf/load_value/key=field_67b872c2ab925',function ($value, $post_id, $field) {
		$source_html = $value ? ' origin="'.$value.'"' : '';
		return do_shortcode('[newsletter_form type="standalone"'.$source_html.' layout="short" disable_booking]');
	}, 10, 3 );
}