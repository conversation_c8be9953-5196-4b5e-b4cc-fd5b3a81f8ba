// Custom list of sites; Default is too long
#wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  width: min-content;
  gap: 0;
  z-index: unset;
  padding: 0 0 6px;

  // reset stack
  li {
    z-index: unset;
  }

  // visibility
  .hover {
    background: #7B8D96;
  }

  // nice up
  .ab-sub-wrapper {
    width: 100%;
    box-shadow: none;

    // visibility
    ul {
      background: #7B8D96;
    }
  }

  // declutter
  .wp-admin-bar-arrow,
  .blavatar {
    display: none;
  }
}

// black theme styles for reworked sites dropdown
.admin-color-fresh {
    #wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .hover,
    #wpadminbar .quicklinks .menupop ul#wp-admin-bar-my-sites-list .ab-sub-wrapper ul{
        background: #1F2327;
    }
}

.acf-fields {
  .acf-field-message.heading {
    background-color: #e9e8e8;

    .acf-label {
      margin-bottom: 0;
    }
  }
}