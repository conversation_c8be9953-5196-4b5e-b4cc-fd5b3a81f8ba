{"key": "group_645cb26c4359c", "title": "Page options", "fields": [{"key": "field_645cb26c55bd7", "label": "Hide title", "name": "hide_title", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "allow_in_bindings": 1, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_64b66a1da1c96", "label": "Show signup CTA", "name": "show_signup", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "25", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "allow_in_bindings": 1, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_645cb2a355bd8", "label": "Custom CSS class", "name": "css_class", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_676023974a58a", "label": "Custom Floating CTA", "name": "custom_floating_cta", "aria-label": "", "type": "group", "instructions": "Leave empty fields to use default values from Theme Settings", "required": 0, "conditional_logic": [[{"field": "field_64b66a1da1c96", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "show_in_graphql": 1, "layout": "block", "sub_fields": [{"key": "field_676024b34a58b", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_676024bf4a58c", "label": "Content", "name": "content", "aria-label": "", "type": "textarea", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "rows": 4, "placeholder": "", "new_lines": "br"}, {"key": "field_676025374a58d", "label": "Link", "name": "link", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "40", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_6760256e4a58e", "label": "Label", "name": "label", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "40", "class": "", "id": ""}, "show_in_graphql": 1, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "", "prepend": "", "append": ""}, {"key": "field_676025814a58f", "label": "New Tab?", "name": "new_tab", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "20", "class": "", "id": ""}, "show_in_graphql": 1, "message": "", "default_value": 0, "allow_in_bindings": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}]}], "location": [[{"param": "post_type", "operator": "==", "value": "page"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "show_in_graphql": 1, "graphql_field_name": "pageOptions", "map_graphql_types_from_location_rules": 0, "graphql_types": "", "modified": 1744195945}