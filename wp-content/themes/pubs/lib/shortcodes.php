<?php
add_action('init', function(){

    /**
     * Cookiebot declaration shortcode: [cookiebot_declaration]
     */
    add_shortcode( 'cookiebot_declaration', function(){
        return '<div data-cookiebot-declaration="true">&nbsp;</div>';
    });

    /**
     * Animated Animals shortcode: [animated_animal]
     */
    add_shortcode( 'animated_animal', function($atts, $content){
        extract( shortcode_atts( array(
            'type' => 'bird',
            'text' => 'Default animated text goes here...',
            'class' => ''
        ), $atts ) );
        $html = '<div class="'.$class.'" data-animated-animal="true" data-type="'.$type.'" data-text="'.$text.'">&nbsp;</div>';

        return $html;
    } );

    /**
     * Contact details shortcode [contact]
     */
    add_shortcode( 'contact', function($atts, $content){
        extract( shortcode_atts( array(
            'btn' => 'outline-dusk',
        ), $atts ) );

        $html = '';
        $address = get_field('opt_address_multiline', 'option') ?: false;
        $directions = get_field('opt_directions', 'option') ?: false;
        $phone = get_field('opt_phone', 'option') ?: false;
        $email = get_field('opt_email', 'option') ?: false;
        $show_phone = get_field('contact_show_phone') ?: false;
        $show_email = get_field('contact_show_email') ?: false;

        $html .= '<div class="shortcode-contact">';
        if( $address )$html .= '<p class="text-center text-lg-start font-family-headings fs-4">'.$address.'</p>';
            if( $directions ) $html .= '<a href="'.$directions.'" target="_blank" class="btn btn-'.$btn.' mb-50" rel="noopener">Directions</a>';
            $html .= '<p class="text-center text-lg-start font-family-headings fs-4">';
            if( $show_phone && $phone ) $html .= 'Tel: <a href="tel:'.$phone.'">'.$phone.'</a><br>';
            if( $show_email && $email ) $html .= 'Email: <a href="mailto:'.$email.'">'.$email.'</a>';
            $html .= '</p>';
        $html .= '</div>';

        return $html;
    } );

    /**
     * Opening times shortcode [opening_times]
     */
    add_shortcode( 'opening_times_old', function($atts, $content){
        $groups = get_field('opt_times', 'option') ?: false;
        if( !$groups ) return;

        // $html .= '<h3 class="mt-lg-0">Opening times</h3>';
        $html .= '<div class="opening-times">';
            foreach( $groups as $group ){
                $name = $group['name'];
                $entries = $group['entries'];
                $html .= '<div class="opening-times-group">';
                $html .= '<h6 class="mt-30"><strong>'.$name.'</strong></h6>';
                $html .= '<ul class="list-unstyled mt-0">';
                foreach( $entries as $entry ){
                    $html .= '<li class="d-flex flex-row flex-nowrap">';
                    if( $entry["closed"] ) {
                        $html .= '<span class="day">'.$entry["label"].'</span> <span class="time time-from">Closed</span>';
                    }else {
                        $html .= '<span class="day">'.$entry["label"].'</span> <span class="time"><span class="time-from">'.$entry["from"].'</span> <span class="separator">-</span> <span class="time-to">'.$entry["to"].'</span> ';
                        if( $entry["note"] ) $html .= '<small class="note">('.$entry["note"].')</small>';
                    }
                    $html .= '</li>';
                }
                $html .= '</ul>';
                $html .= '</div>';
            }
        $html .= '</div>';
        return $html;
    } );

    /**
     * Opening times shortcode [opening_times_new]
     */
    add_shortcode( 'opening_times', function($atts, $content){
        extract( shortcode_atts( array(
            'ids' => '',
            'heading_style' => ''
        ), $atts ) );
        $groups = get_field('opt_times_new', 'option') ?: false;
        if( !$groups ) return;
        // $html .= '<h3 class="mt-lg-0">Opening times</h3>';
        $html .= '<div data-opening-times="true" data-ids="'.$ids.'" data-style="'.$heading_style.'"></div>';
        return $html;
    } );

    /*
    * Newsletter signup shortcodes: [newsletter_form]
    */
    add_shortcode('newsletter_form', 'saint_newsletter_form');
    function saint_newsletter_form( $atts, $content = null ) {

    global $post;

    extract( shortcode_atts( array(
        'type' => '',
        'origin' => false,
        'ignore' => false,
        'layout' => 'default',
        'submit_label' => 'Subscribe'
    ), $atts ) );

    // var_dump($atts);

    $html = '';
    $sites = get_published_pubs_sorted();
    $atreemo = class_exists('AtreemoAPI') ? new AtreemoAPI() : false;
    $locations = $atreemo->getLocationsFormated();
    $is_master_site = get_current_blog_id() == 3 ? true : false;
    $disable_booking = in_array('disable_booking', $atts);

    // --- Get starting year for DOB (we dont want people under 18yo)
    $start_year = 1900;
    $end_year = date('Y') - 18;

    // === If STANDALONE and on single restaurant make the location auto-prefilled and hide dropdown
    $local_disable_signup = get_field('disable_signup','option') ?: false;
    $local_atreemo_pub_id = get_field( 'atreemo_pub_id', 'option' ) ?: false;

    $venue_options = '';
    $venue_default = null;
    foreach ( $sites as $site ) :
        switch_to_blog( $site->blog_id );
        // var_dump($site);
        // if( in_array( $site->blog_id, array(22,23) ) ) : // only for Blach Horse pubs
        $disable_signup = get_field('disable_signup','option') ?: false;
        $disable_signup_master = get_field('disable_signup_master','option') ?: false;
        // if master site and disable_signup_master is true, then disable signup
        if( $is_master_site && $disable_signup_master ) $disable_signup = true;

        $atreemo_pub_id = ( get_field( 'atreemo_pub_id', 'option' ) != '' ) ? get_field( 'atreemo_pub_id', 'option' ) : 0;
        $atreemo_suspended = $atreemo ? $atreemo->isSuspended( $atreemo_pub_id ) : false;
        $zonal_disable_email = ( get_field('zonal_disable_email', 'option') ) ? 'yes' : 'no';
        $city = ', ' . get_field( 'opt_town', 'option' );
        $name = get_bloginfo( "name" ) . $city;
        // check
        if( (!$local_disable_signup || $ignore) && $local_atreemo_pub_id && $atreemo_pub_id == $local_atreemo_pub_id ) $venue_default = 'value="' . $atreemo_pub_id . '-' . $zonal_disable_email .'"';

        if( $atreemo_pub_id && !$atreemo_suspended && (!$disable_signup || $ignore) ) {
            $venue_options .= '<option data-ignore="'.$ignore.'" value="'. $atreemo_pub_id . '-' . $zonal_disable_email .'" > '. $name .'</option>';
        }
        restore_current_blog();
    endforeach;

    if( $type == 'standalone' ){
    $html .= '<div class="shortcode-newsletter" data-shortcode-newsletter="true" data-ignore="'.$ignore.'">';
        $html .= '<div class="post--cta post--cta--newsletter">';
    }

        $html .= '<form action="/" class="form form--newsletter js-newsletter-form" method="POST">';
            $html .= '<div class="form-inner">';
                $html .= '<div class="form-row '.(!$is_master_site?"d-none":"").'">';
                $html .= '<label for="venueRef" class="form-label text-center text-lg-start">Your inn *</label>';
                    $html .= '<div class="js-select-replace" >';
                        $html .= '<select name="venueRef" id="venueRef" class="form-control" '.$venue_default.' required>';
                            $html .= '<option value="0">Please select</option>';
                            $html .= $venue_options;
                        $html .= '</select></div>';
                    $html .= '<span class="error-tip">Please select your local brasserie.</span>';
                $html .= '</div>';
                $html .= '<div class="'.($layout == 'short' ? "row" : "").'">';
                    $html .= '<p class="form-row'.($layout == 'short' ? " col-md-6" : "").'">';
                        $html .= '<label for="firstname" class="form-label text-center text-lg-start">First Name *</label>';
                        $html .= '<input type="text" name="firstname" id="firstname" class="form-control" required>';
                        $html .= '<span class="error-tip">This field is required.</span>';
                    $html .= '</p>';
                    $html .= '<p class="form-row'.($layout == 'short' ? " col-md-6" : "").'">';
                        $html .= '<label for="surname" class="form-label text-center text-lg-start" >Last Name *</label>';
                        $html .= '<input type="text" name="surname" id="surname" class="form-control" required>';
                        $html .= '<span class="error-tip">This field is required.</span>';
                    $html .= '</p>';
                $html .= '</div>';
                $html .= '<p class="form-row">';
                    $html .= '<label for="email" class="form-label text-center text-lg-start">Email *</label>';
                    $html .= '<input type="email" name="email" id="email" class="form-control" required>';
                    $html .= '<span class="error-tip">This field is required.</span>';
                $html .= '</p>';

                if( in_array($layout, ['business_membership']) ):
                    $html .= '<p class="form-row">';
                        $html .= '<label for="phone" class="form-label text-center text-lg-start">Phone *</label>';
                        $html .= '<input type="text" name="phone" id="phone" class="form-control" required>';
                    $html .= '</p>';
                endif;

                if( in_array($layout, ['business_membership']) ):
                    $html .= '<p class="form-row">';
                        $html .= '<label for="company" class="form-label text-center text-lg-start">Company name *</label>';
                        $html .= '<input type="text" name="company" id="company" class="form-control" required>';
                    $html .= '</p>';
                endif;

                // === START:  DOB ===
                if( in_array($layout, ['default']) ):
                $html .= '<div class="form-row form-inline">';

                    $html .= '<label for="day" class="form-label">Date of birth</label>';

                    $html .= '<div class="row">';

                        $html .= '<div class="div col col-xs-4">';
                            $html .= '<div class="js-select-replace"><select id="signup_day" class="form-control third" name="day" >';
                                $html .= '<option value="00">DD</option>';
                                $html .= '<option value="01">1</option><option value="02">2</option><option value="03">3</option><option value="04">4</option><option value="05">5</option><option value="06">6</option><option value="07">7</option><option value="08">8</option><option value="09">9</option><option value="10">10</option><option value="11">11</option><option value="12">12</option><option value="13">13</option><option value="14">14</option><option value="15">15</option><option value="16">16</option><option value="17">17</option><option value="18">18</option><option value="19">19</option><option value="20">20</option><option value="21">21</option><option value="22">22</option><option value="23">23</option><option value="24">24</option><option value="25">25</option><option value="26">26</option><option value="27">27</option><option value="28">28</option><option value="29">29</option><option value="30">30</option><option value="31">31</option>';
                            $html .= '</select></div>';
                        $html .= '</div>';

                        $html .= '<div class="div col col-xs-4">';
                            $html .= '<div class="js-select-replace"><select id="signup_month" class="form-control third" name="month" >';
                                $html .= ' <option value="00" >MM</option>';
                                $html .= '<option value="01">01</option>';
                                $html .= '<option value="02">02</option>';
                                $html .= '<option value="03">03</option>';
                                $html .= '<option value="04">04</option>';
                                $html .= '<option value="05">05</option>';
                                $html .= '<option value="06">06</option>';
                                $html .= '<option value="07">07</option>';
                                $html .= '<option value="08">08</option>';
                                $html .= ' <option value="09">09</option>';
                                $html .= '<option value="10">10</option>';
                                $html .= '<option value="11">11</option>';
                                $html .= '<option value="12">12</option>';
                            $html .= '</select></div>';
                        $html .= '</div>';

                        $html .= '<div class="div col col-xs-4">';
                            $html .= '<div class="js-select-replace"><select id="signup_year" class="form-control third" name="year" >';
                                $html .= ' <option value="00">YYYY</option>';
                                $html .= populate_year_dropdown($start_year, $end_year);
                            $html .= '</select></div>';
                        $html .= '</div>';

                    $html .= '</div>';

                    // $html .= '<span class="error-tip">This field is required.</span>';

                $html .= '</div>';
                endif;
                // === END:  DOB ===

                // --- Consent
                $html .= '<div class="checkbox-input-magic form-row">';
                    $html .= '<p>';
                    $html .= '<span><input type="checkbox" class="form-check-input" id="signup_consent" name="consent" required></span>';
                    // $html .= '<label for="signup_consent" class="text-start">I consent to my personal data being used so that information about Heartwood Inns and any subsequent offers can be sent to me. I confirm I am over 18 years old and understand that my personal data will be handled in accordance with Heartwood Inns '. sprintf('<a data-app-link="true" href="%1$s">%2$s</a>', '/legal/privacy-policy/', __('privacy policy') ) .'.</label>';
                    $html .= '<label for="signup_consent" class="text-start">I consent to my personal data being used so that information about my chosen pub, any other Heartwood Inn and Brasserie Blanc and any subsequent offers, news and events can be sent to me via email. <strong>I confirm that I am over 18 years old and understand any personal data will be handled in accordance with Heartwood Inns '. sprintf('<a data-app-link="true" href="%1$s">%2$s</a>', '/legal/privacy-policy/', __('Privacy Policy') ) .'.</strong></label>';
                    $html .= '</p>';
                    $html .= '<span class="error-tip" style="margin-top: -1.5rem; margin-bottom: 1rem;">Please check the box to consent.</span>';
                $html .= '</div>';

                if( in_array($layout, ['default']) ):
                $html .= '<p class="small">Please note that from time to time, we may also include information from trusted complementary third party brands with whom we are running events or offers but we will never share your details with them.</p>';
                endif;
                $html .= '<p class="small">You are able to unsubscribe from communications at any time.</p>';

                $html .= '<p class="message dob-error d-none"></p>';
                $html .= '<p class="message error d-none"></p>';

                switch($layout) {
                    case 'business_membership':
                        $submit_label = 'Join the club';
                        break;
                    default:
                        // $submit_label = $submit_label;
                        break;
                }
                $html .= '<p class="form__actions">';
                    $html .= '<button type="submit" name="subscribe" class="btn btn-outline-dusk">'.$submit_label.'</button>';
                $html .= '</p>';

            $html .= '</div>'; // .form-inner
            $html .= '<p class="message success d-none"></p>';

            // --- hidden fields
            if( $origin ) $html .= '<input type="hidden" name="origin" value="'.$origin.'">';
            if( $disable_booking ) $html .= '<input type="hidden" name="disable_booking" value="1">';

        $html .= '</form>';


    if( $type == 'standalone' ){
        $html .= '</div>';
    $html .= '</div>';
    }

    return $html;

    }

    /**
     * Locations Map: segmented by brand [BB, WBC]
     */
    add_shortcode( 'locations_map', function(){

    // --- Get Locations data
    $locations = get_all_pubs_sorted();
    $markers=null;
    foreach ( $locations as $site ) :
        switch_to_blog( $site->blog_id );
        $comingsoon = get_field('opt_coming_soon','option') ?: false;
        if( !$site->public && !$comingsoon ) continue;

        $brand = get_field('opt_brand', 'option');
        $disable_rooms = get_field('disable_rooms', 'option') ?: false;
        $opt_hotel_id = get_field('opt_hotel_id', 'option') ?: false;
        $opt_cutom_button_label = get_field('opt_cutom_button_label', 'option') ?: false;
        $opt_cutom_button_link = get_field('opt_cutom_button_link', 'option') ?: false;
        $opt_cutom_button_newtab = get_field('opt_cutom_button_newtab', 'option') ?: false;
        if( $brand !== 'hc' ) {
            $headless_config = get_option('headless_config');
            $geo_lat = get_field('opt_geo_lat', 'option') ?: 0;
            $geo_lng = get_field('opt_geo_lng', 'option') ?: 0;

            $markers[] = array(
                "brand" => $brand,
                "name" => get_bloginfo( "name" ),
                "address" => get_field('opt_address_multiline', 'option'),
                "tel" => get_field( 'opt_phone', 'option' ),
                "email" => get_field('opt_email', 'option'),
                "directions" => get_field('opt_directions', 'option'),
                "website" => $headless_config['frontend_url'],
                "bookTable" => $headless_config['frontend_url']."book-a-table/",
                "bookStay" => $headless_config['frontend_url']."rooms/",
                "image" => "",
                "position" => [$geo_lat, $geo_lng],
                "comingsoon" => $comingsoon,
                "rooms_enabled" => !$disable_rooms && $opt_hotel_id ? true : false,
                "custom_button" => [
                    'label'=>$opt_cutom_button_label,
                    'link'=>$opt_cutom_button_link,
                    'newtab'=> $opt_cutom_button_newtab
                ]
            );
        }

        restore_current_blog();
    endforeach;
    restore_current_blog();

        $options = json_encode(array(
            "centerPos" => [52.412951,-0.8642876],
            "zoom" => 7,
            "showFilters" => true,
            "showPopups" => true
        ));

        $markers_json = json_encode($markers);

        return '<div data-locations-map="true" data-options="'. htmlspecialchars($options, ENT_QUOTES, 'UTF-8') .'" data-markers="'. htmlspecialchars($markers_json, ENT_QUOTES, 'UTF-8') .'">&nbsp;</div>';
    } );

    /**
     * Booking (Guestline) button
     * params: hotel id, room id, promo code, arrival date
     */
    add_shortcode( 'guestline_button', function($atts, $content){
        global $post;
        extract( shortcode_atts( array(
            'post_id' => $post->ID,
            'label' => "Book a Stay", // Button label
            'hotel' => false, // Hotel ID
            'rooms' => false, // Room ID
            'arrival' => false, // Arrival date
            'departure' => false, // Departure date
            'nights' => false,
            'adults' => false, // Adults
            'promo' => false, // Promo Code
            'class' => 'my-15',
            'id' => '',
            'btn_style' => 'outline-dusk'
        ), $atts ) );
        // --- Check if Guestline is Disabled
        $disableRooms = get_field('disable_rooms', 'option') ?: false;
        // --- Hotel ID, if we're on Inn site then use local Hotel ID
        $default_hotel = get_field('opt_hotel_id', 'option');
        if( !$hotel && $default_hotel ) $hotel = $default_hotel;
        if( ($disableRooms || !$hotel) && !is_inns_master_site() ) return;
        // --- Room ID, if we're on Single Room view, then use local Room ID
        $default_room = get_field('guestline_room_id', $post_id);
        if( !$rooms && $default_room ) $rooms = $default_room;
        $rooms_html = $rooms ? ' data-rooms="'.$rooms.'"' : '';
        // --- PromoCode
        $promo_html = $promo ? ' data-promoCode="'.esc_attr( $promo ).'"' : '';
        // --- Arrival date
        $arrival_html = $arrival ? ' data-arrival="'.esc_attr( date_format(date_create($arrival), "Y-m-d") ).'"' : '';
        // --- Departure Date
        $departure_html = $departure ? ' data-departure="'.esc_attr( date_format(date_create($departure), "Y-m-d") ).'"' : '';
        // --- Nights
        $nights_html = $nights ? ' data-nights="'.esc_attr( intval($nights) ).'"' : '';
        // --- Adults
        $adults_html = $adults ? ' data-adults="'.esc_attr( intval($adults) ).'"' : '';

        // === Render HTML

        // --- If Master site (dropdown)
        if( is_inns_master_site() ){
            $sites = get_all_pubs_sorted();
            $site_found=0;
            $html = '<div data-bs-dropdown="true" class="guestline-dropdown dropdown my-15">';
                $html .= '<button data-bs-dropdown-toggle="true" data-bs-dropdown-toggle-variant="'.$btn_style.'">';
                $html .= $label.' <svg class="icon-arrow-down " fill="currentColor" viewBox="0 0 12 18"><use href="/images/icon.svg#arrow-down"></use></svg>';
                $html .= '</button>';
                $html .= '<ul data-bs-dropdown-menu="true" class="guestline-dropdown-menu dropdown-menu px-0">';
                    foreach($sites as $site){
                        switch_to_blog( $site->blog_id );
                        $city = ', ' . get_field( 'opt_town', 'option' );
                        $location_name = get_bloginfo( "name" ) . $city;
                        $opt_disable_rooms = get_field('disable_rooms', 'option') ?: false;
                        $opt_hotel_id = get_field('opt_hotel_id', 'option') ?: false;
                        $comingsoon = get_field('opt_coming_soon','option') ?: false;
                        $headless_config = get_option('headless_config');
                        // --- If we book specific Room ID, then check if current Inn has any
                        if( $rooms ) {
                            $site_rooms = get_posts(array(
                                'post_type' => 'room',
                                'posts_per_page' => -1,
                                'meta_key' => 'guestline_room_id',
                                'meta_value' => $rooms
                            ));
                        }
                        restore_current_blog();
                        if( $rooms && !$site_rooms ) continue;
                        if( $opt_disable_rooms || !$opt_hotel_id || $comingsoon ) continue;
                        $html .= '<li><a href="#" class="dropdown-item" data-guestline-redirect="true" data-hotel-id="'.$opt_hotel_id.'" '.$rooms_html.$promo_html.$arrival_html.$departure_html.$nights_html.$adults_html.'>'.$location_name.'</a></li>';
                        // $html .= '<li><a href="'.$headless_config['frontend_url'].'rooms" >'.$location_name.'</a></li>';
                        $site_found++;
                    }
                $html .= '</ul>';
            $html .= '</div>';
            restore_current_blog();
            if( !$site_found ) return '';
        // --- Inn site (default button)
        }else{
            $html = '<p class="'.$class.'"><button class="guestline-button btn btn-'.$btn_style.' button" data-guestline-redirect="true" data-hotel-id="'.$hotel.'" '.$rooms_html.$promo_html.$arrival_html.$departure_html.$nights_html.$adults_html.'>'.$label.'</button></p>';
        }
        restore_current_blog();
        return $html;
    } );

    add_shortcode( 'room_features', function($atts, $content) {
        // === get Features taxonomy
        global $post;
        $post_id = $post->ID;
        $features = get_the_terms( $post_id, 'feature' );
        if( !$features ) return;
        $html = '<h3 class="mt-20 mb-20">Features</h3>';
        $html .= '<ul class="d-flex flex-wrap listing p-0 m-0 mb-30 justify-content-center justify-content-lg-start">';
            foreach( $features as $feature ):
                $html .= '<li class="mx-20">'.$feature->name.'</li>';
            endforeach;
        $html .= '</ul>';
        return $html;
    } );

    /**
     * Vidoe shortcodes to serve dual source (our solution for cross browser transparent video)
     */
    add_shortcode( 'video_transparent', function($atts, $content) {
        extract( shortcode_atts( array(
            'mp4' => false,
            'webm' => false,
            'autoplay' => '',
            'loop' => '',
            'muted' => '',
            'playsinline' => '',
            'controls' => '',
            'nolazy' => false
        ), $atts ) );

        $assets_timestamp = multisite_acf_option(1, 'opt_assets_timestamp') ?: false;
        $assets_timestamp_param = $assets_timestamp ? '?v='.$assets_timestamp : '';

        $html = '';

        if( in_array('autoplay', $atts) ) $autoplay = 'autoplay ';
        if( in_array('loop', $atts) ) $loop = 'loop ';
        if( in_array('muted', $atts) ) $muted = 'muted ';
        if( in_array('playsinline', $atts) ) $playsinline = 'playsinline ';
        if( in_array('controls', $atts) ) $controls = 'controls ';
        if( in_array('nolazy', $atts) ) $nolazy = true;

        $video_class = $nolazy ? '' : 'class="lazy"';
        $source_attr = $nolazy ? 'src' : 'data-src';

        if( $mp4 || $webm ) {
            $html .= '<div class="wp-block-video"><video '.$video_class.' '.$autoplay.$loop.$muted.$playsinline.$controls.'>';
            // Provide the Safari video
            if( $mp4 ) $html .= '<source '.$source_attr.'="'.$mp4.$assets_timestamp_param.'" type="video/mp4; codecs=hvc1">';
            // .. and the Chrome video
            if( $webm ) $html .= '<source '.$source_attr.'="'.$webm.$assets_timestamp_param.'" type="video/webm">';
            $html .= '</video></div>';
        }

        return $html;
    } );

    /**
     * Awards shortcode (list awards associated with current blog ID)
     */
    add_shortcode( 'awards_feed', function($atts, $content) {
        extract( shortcode_atts( array(
            'count' => false,
        ), $atts ) );

        $html = '';
        $found = 0;
        $current_blog_id = get_current_blog_id();
        // --- get Awards from Collection site
        switch_to_blog( 1 );
        $awards = get_field('opt_awards', 'option') ?: false;
        if($awards) {
            $html .= '<div class="shortcode-awards">';
                $html .= '<div class="row justify-content-center align-items-center">';
                    foreach( $awards as $award ) {
                        if( $count && $found >= $count ) break;
                        $site_selection = $award['site_selection'];
                        if( in_array($current_blog_id, array_values($site_selection)) ) {
                            $found++;
                        }else {
                            continue;
                        }
                        $img = $award['image'];
                        $img_alt = $img['alt'] ?: $img['title'];
                        $link  = $award['link'];
                        $new_tab = $award['new_tab'] ? ' target="_blank"' : '';

                        $html .= '<div class="col-12 col-md-3 px-50 px-md-15 mb-30">';
                        $html .= '<figure>';
                        if( $link ) $html .= '<a href="'.$link.'" '.$new_tab.'>';
                        $html .= '<img src="'.$img['url'].'" class="img-fluid" alt="'.$img_alt.'">';
                        if( $link ) $html .= '</a>';
                        $html .= '</figure>';
                        $html .= '</div>';
                    }
                $html .= '</div>';
            $html .= '</div>';
        }

        restore_current_blog();
        return $html;
    } );

    add_shortcode( 'location_dropdown', function(){
        $html = '<p>';
        $html .= '<label class="form-label text-center text-lg-start" for="form-location">Location</label>';
        $html .= '<span class="wpcf7-form-control-wrap" data-name="location">';
        $html .= '<select class="wpcf7-form-control wpcf7-select wpcf7-validates-as-required form-control js-change-form-recipient" name="recipient-location" id="form-location" aria-required="true" aria-invalid="false" required>';
            // --- get locations
            $locations = get_published_pubs_sorted();
            if( $locations ) {
                foreach($locations as $site) {
                    switch_to_blog( $site->blog_id );
                    $name = get_bloginfo( "name" );
                    $email = get_field('opt_email', 'option');
                    $email_forms = get_field('opt_email_forms', 'option') ?: $email;
                    $city = ', ' . get_field( 'opt_town', 'option' );
                    $html .= '<option value="'.$site->blog_id.'|'.$name.$city.'|'.$email_forms.'">'.$name.$city.'</option>';
                }
                restore_current_blog();
            }
        $html .= '</select>';
        $html .= '</span>';
        $html .= '</p>';
        return $html;
    } );

    /**
     * Feratures Carousel
     */
    add_shortcode( 'features_carousel', function(){
        $features = get_field('opt_features_carousel', 'option') ?: false;
        if( !$features ) return;
        $title = $features["title"] ?: "What's on?";
        $html = '';
        $html .= '<div data-features-carousel="true" class="bg-sap py-20">';
            $html .= '<div className="features-carousel text-white">';
                $html .= '<div class="alert alert-info text-center">';
                    $html .= '<h4 class="alert-heading">'.$title.'</h4><hr>';
                    $html .= '<p>This is just a placeholder.</p>';
                $html .= '</div>';
            $html .= '</div>';
        $html .= '</div>';

        return $html;
    } );

    /**
     * Custom data provider for Contact Form 7
     * Field tag: [select* your-space include_blank id:your-space data:your_space]
     */
    add_filter('wpcf7_form_tag_data_option', function($n, $options, $args){
        // special data provider tag found ?
        if (in_array('your_space', $options)){
            $args = array(
                'post_type' => 'area',
                'posts_per_page' => -1,
                'post_status' => 'publish',
            );
            $query = new WP_Query($args);
            if ($query->have_posts()) {
                $data = array('Any');
                while ($query->have_posts()) {
                    $query->the_post();
                    $title = get_the_title();
                    $data[] = esc_attr($title);
                }
            } else {
                $data = array('No Areas Found');
            }

            return $data;
        }
        // default - do not apply any changes within the options
        return null;
    }, 10, 3);

// [area_titles_dropdown]

    /**
     * Loyalty App Store button
     */
    add_shortcode('loyalty_app_store', function(){
        $app_store = get_field('opt_loyalty_ios', 'option') ?: false;
        if(!$app_store) return;
        $html = '<a class="badge-app-store" href="'.$app_store.'" target="_blank" rel="noopener">';
        $html .= '<i data-icon="download-on-app-store"></i>';
        $html .= '</a>';
        return $html;
    });

    /**
     * Loyalty Play Store button
     */
    add_shortcode('loyalty_play_store', function(){
        $play_store = get_field('opt_loyalty_android', 'option') ?: false;
        if(!$play_store) return;
        $html = '<a class="badge-play-store" href="'.$play_store.'" target="_blank" rel="noopener">';
        $html .= '<i data-icon="get-it-on-google-play"></i>';
        $html .= '</a>';
        return $html;
    });

},10);