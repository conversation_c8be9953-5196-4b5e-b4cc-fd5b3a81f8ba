<?php
/**
 * Rooms: Guestline booking button - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */

// === Create id attribute allowing for custom "anchor" value.
$id = 'room-btn-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-room-btn'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}

// === Get Button params
$style = get_field('room-btn-style') ?: 'outline-dusk';
$btn_style = ' btn_style="'.$style.'"';
$label = get_field('room-btn-label') ?: "Book a Stay"; // Button label
$rooms = get_field('room-btn-rooms') ?: false; // Room ID
$rooms_html = $rooms ? ' rooms="'.$rooms.'"' : '';
$arrival = get_field('room-btn-arrival') ?: false; // Arrival date
$arrival_html = $arrival ? ' arrival="'.esc_attr( date_format(date_create($arrival), "Y-m-d") ).'"' : '';
$departure = get_field('room-btn-departure') ?: false; // Departure date
$departure_html = $departure ? ' departure="'.esc_attr( date_format(date_create($departure), "Y-m-d") ).'"' : '';
$nights = get_field('room-btn-nights') ?: false;
$nights_html = $nights ? ' nights="'.esc_attr( intval($nights) ).'"' : '';
$adults = get_field('room-btn-adults') ?: false; // Adults
$adults_html = $adults ? ' adults="'.esc_attr( intval($adults) ).'"' : '';
$promo = get_field('room-btn-promo') ?: false; // Promo Code
$promo_html = $promo ? ' promo="'.esc_attr( $promo ).'"' : '';

if( !is_admin() ):
?>
    <?= do_shortcode('[guestline_button class="'.esc_attr($className).'" label="'.$label.'" '.$rooms_html.$promo_html.$arrival_html.$departure_html.$nights_html.$adults_html.$btn_style.' id="'.$id.'"]') ?>
<!-- .block-room-btn -->
<?php else: // we show only bummy button in editor ?>
    <p class="<?= esc_attr($className) ?>"><button class="btn btn-<?= $style ?>" ><?= $label ?></button></p>
    <!-- .block-room-btn -->
<?php endif; ?>