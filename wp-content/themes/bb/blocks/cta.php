<?php
/**
 * Call To Action (CTA) - Block Template.
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'cta-' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-cta bg-light'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
$title = get_field('cta_title') ?: false;
$content = get_field('cta_content');
$buttons = get_field('cta_buttons') ?: false;
$cta_animation = get_field('cta_animation') ?: false;
$cta_anim_text = get_field('cta_anim_text'); // free text
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?> <?= $image_align ?>">

    <div class="cta-content-col m-0 py-0 bg-sap text-linen edge-2-edge">
        <div class="cta-content full-width-boxed d-flex flex-column justify-content-center py-10 text-center text-lg-start">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-lg-6 d-flex align-items-center">
                        <?php if( $cta_animation ):
                        echo do_shortcode('[animated_animal type="bee" text="'.$cta_anim_text.'" class="half-width"]');
                        endif; ?>
                    </div>
                    <div class="col-12 col-lg-6 py-50">
                    <?php if( $title ): ?><h2 class="cta-title mt-0"><?= $title ?></h2><?php endif; ?>
                        <div class="cta-text mb-30">
                            <?= $content ?>
                        </div>
                        <?php if($buttons): ?>
                            <div class="cta-buttons d-flex flex-column align-items-center align-items-lg-start">
                                <?php foreach( $buttons as $btn ): ?>
                                    <a href="<?= $btn['url'] ?>" class="btn btn-outline-linen mb-15"><?= $btn['label'] ?></a>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div><!-- .block-cta -->