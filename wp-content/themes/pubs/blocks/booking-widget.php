<?php
/**
 * Booking widget (Zonal Events) - Block Template.
 *
 * Zonal Events widget
 * Type: Standard Restaurant Booking
 * Main Pubs master site ID: 3
 *
 * @param   array $block The block settings and attributes.
 * @param   string $content The block inner HTML (empty).
 * @param   bool $is_preview True during AJAX preview.
 * @param   (int|string) $post_id The post ID this block is saved to.
 */
// === Create id attribute allowing for custom "anchor" value.
$id = 'booking-widget' . $block['id'];
if( !empty($block['anchor']) ) {
    $id = $block['anchor'];
}
// === Create class attribute allowing for custom "className" and "align" values.
$className = 'block-booking'; // ***** Unique classname for CSS styling *****
if( !empty($block['className']) ) {
    $className .= ' ' . $block['className'];
}
if( !empty($block['align']) ) {
    $className .= ' align' . $block['align'];
}
$pubs_mastersite_id = 3;
$locations = get_published_pubs_sorted();
$restBookingType = get_field('booking_type') ?: 'standard';
$default_zonalevents_id = get_field('zonalevents_rest_id', 'option') ?: false;
// === Confirmation info - Local
$zonalevents_confirmation_info = get_field('zonalevents_confirmation_info', 'option') ?: false;
$zonalevents_enquiry_info = get_field('zonalevents_enquiry_info', 'option') ?: false;
// --- if none, then try to get master site value
if( !$zonalevents_confirmation_info ) $zonalevents_confirmation_info = multisite_acf_option($pubs_mastersite_id, 'zonalevents_confirmation_info') ?: false;
if( !$zonalevents_enquiry_info ) $zonalevents_enquiry_info = multisite_acf_option($pubs_mastersite_id, 'zonalevents_enquiry_info') ?: false;

// --- Get excluded occasions
$excluded_occasions = false;
$excluded_occasions_array = multisite_acf_option($pubs_mastersite_id, 'zonalevents_excluded_occasion') ?: false;
if( $excluded_occasions_array ) {
    foreach( $excluded_occasions_array as $item ) {
        $excluded_occasions[] = $item['id'];
    }
}

$zonalevents_enquiry_info_content = null;
if( $zonalevents_enquiry_info ):
    $zonalevents_enquiry_info_content = apply_filters('the_content', $zonalevents_enquiry_info);
endif;

$zonalevents_confirmation_content = null;
if( $zonalevents_confirmation_info ):
    $zonalevents_confirmation_content = apply_filters('the_content', $zonalevents_confirmation_info);
endif;

// --- Get Locations data
$count = 1; $options=null;
foreach ( $locations as $site ) :
    switch_to_blog( $site->blog_id );

    $disable_booking = get_field('disable_booking', 'option') ?: false;

    $city = ', ' . get_field( 'opt_town', 'option' );
    $location_name = get_bloginfo( "name" ) . $city;
    $location_phone = get_field( 'opt_phone', 'option' ) ?: false;
    $location_email = get_field('opt_email', 'option') ?: false;
    $atreemo_id = get_field( 'atreemo_pub_id', 'option' ) ?: false;

    $zonalevents_id = get_field('zonalevents_rest_id', 'option') ?: false;
    $default_occasion = get_field('zonalevents_default_occasion', 'option') ?: false;
    $default_occasion_rooms = get_field('zonalevents_default_occasion_rooms', 'option') ?: false;
    $default_menu = get_field('zonalevents_default_menu', 'option') ?: false;
    // $default_menu = false;
    $menu_show = get_field('zonalevents_default_menu_show', 'option') ? 1 : 0;
    $default_area = get_field('zonalevents_default_area', 'option') ?: false;
    $area_show = get_field('zonalevents_default_area_show', 'option') ? 1 : 0;
    $upsell_show = get_field('zonalevents_default_upsell_show', 'option') ? 1 : 0;
    $use_promocodes = get_field('use_promocodes', 'option') ? 1 : 0;
    $min_date = get_field('zonalevents_min_date', 'option') ?: '';

    $headless_config = get_option('headless_config');

    // --- Guestline
    $opt_hotel_id = get_field('opt_hotel_id', 'option') ?: false;
    $glApiUrl = get_field('gl_api_url', 'option') ?: false;
    $glApiPass = get_field('gl_api_pass', 'option') ?: false;
    $glApiOperator = get_field('gl_api_operator', 'option') ?: false;
    $glApiInterface = get_field('gl_api_interface', 'option') ?: false;

    if( $zonalevents_id && !$disable_booking ):
        $options[] = array(
            'id' => $site->blog_id,
            'name' => $location_name,
            'phone' => $location_phone,
            'email' => $location_email,
            'url' => $headless_config['frontend_url'],
            'atreemoid' => $atreemo_id,
            'occasionid' => $default_occasion,
            'roomsoccasionid' => $default_occasion_rooms,
            'menuid' => $default_menu,
            'menushow' => $menu_show,
            'areaid' => $default_area,
            'areashow' => $area_show,
            'upsellshow' => $upsell_show,
            'zonalId' => $zonalevents_id,
            'usePromocodes' => $use_promocodes,
            'enquiryInfo' => $zonalevents_enquiry_info_content,
            'confirmationInfo' => $zonalevents_confirmation_content,
            'hotelid' => $opt_hotel_id,
            'glapiurl' => $glApiUrl,
            'glapipass' => $glApiPass,
            'glapioperator' => $glApiOperator,
            'glapiinterface' => $glApiInterface,
            'mindate' => $min_date,
        );
    endif;
    $count++;
    restore_current_blog();
endforeach;

// --- get Atreemo SourceID origin
$origin = get_field('booking_atreemo_origin') ?: false;
$booking_date = get_field('booking_date') ?: false;
$booking_areaId = get_field('booking_areaId') ?: false;
$booking_menuIds = get_field('booking_menuIds') ?: false;
$booking_time = get_field('booking_time') ?: false;

$settings_json = json_encode(array(
    'defaultLocation' => get_current_blog_id() != $pubs_mastersite_id ? $default_zonalevents_id : 0,
    'excludedOccasions' => $excluded_occasions,
    'restApi' => esc_html(get_site_url()).'/wp-json',
    'mastersiteId' => 3,
    'locationOptions' => $options,
    'restBookingType' => $restBookingType,
    'atreemoOrigin' => $origin,
    'booking_date' => $booking_date,
    'booking_time' => $booking_time,
    'booking_areaId' => $booking_areaId,
    'booking_menuIds' => $booking_menuIds,
));

if( !is_admin() ):
?>
<div id="<?= esc_attr($id); ?>" class="<?= esc_attr($className); ?>">
    <div data-booking-widget="true" data-settings="<?= htmlspecialchars($settings_json, ENT_QUOTES, 'UTF-8') ?>" ></div>
</div><!-- .block-contact -->
<?php
else: ?>
    <!-- Backend placeholder -->
    <div class="alert alert-info text-center">
        <h4 class="alert-heading"><?= $restBookingType == 'events' ? 'Enquiry' : 'Booking' ?> widget</h4>
        <hr>
        <p>This is just a placeholder.</p>
    </div>
<?php endif; ?>
