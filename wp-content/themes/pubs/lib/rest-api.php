<?php
/**
 * Register REST API routes
 */
add_action( 'rest_api_init',  'saint_register_custom_rest_route');
function saint_register_custom_rest_route() {
    // === Atreemo
    register_rest_route( 'atreemo/v1', '/add', array(
     'methods'          => 'POST',
     'callback'         => 'rest_atreemo_add_contact',
     'permission_callback' => '__return_true',
    ) );
    // === Postcode finder
    register_rest_route( 'locations/v1', '/search', array(
      'methods'          => 'POST',
      'callback'         => 'rest_locations_search',
      'permission_callback' => '__return_true',
     ) );
}

/**
 * /atreemo/v1/add
 * Callback function
 *
 * Atreemo Start
 *
 */
function rest_atreemo_add_contact(WP_REST_Request $request) {

    // --- The individual sets of params are also available, if needed:
    // $params = $request->get_url_params();
    // $params = $request->get_query_params();
    $params = $request->get_body_params();
    // $params = $request->get_json_params();
    // $params = $request->get_default_params();

    // --- Auth connection
    if( !isset($params['api_secret']) || $params['api_secret'] != PREVIEW_SECRET_TOKEN ) {
      return rest_ensure_response( array(
        'code' => 401,
        'message' => 'Authorization needed.'
      ) );
    }

    // --- Debug only
    // return rest_ensure_response(
    //   array(
    //     'code' => 200,
    //     'message' => 'TESTING',
    //     'data' => $request->get_params()
    //   )
    // );

    $atreemo_data = $request_data = null;
    $get_result = $add_result = null;

    $venue_data = explode('-', $params['venueRef']);
    $venue_ref = $venue_data[0];

    // --- get Site specific details
    $sites = get_sites([
      'site__not_in' => [ 1 ],
      'public' => 1
    ]);
    foreach ( $sites as $site ) :
      switch_to_blog( $site->blog_id );
      if( get_field( 'atreemo_pub_id', 'option' ) == $venue_ref ) {
        $booking_url = $site->domain;
        $api_method = get_field('atreemo_api', 'option') ?: 'old';
        restore_current_blog();
        break;
      }
      restore_current_blog();
    endforeach;

    // === basic data table to send
    $contact_data = array(
      "SourceID"      => "SAINT WBC",
      "FirstName"     => $params['firstname'],
      "LastName"      => $params['surname'],
      "FullName"      => $params['firstname'] . ' ' . $params['surname'],
      "Email"         => $params['email'],
      "ProcessMydata" => true,
      "HomeSiteID"    => $venue_ref
    );

    $contact_data['SupInfo'] = array();

    $year = isset($params['year']) && $params['year'] != '00' ? $params['year'] : 1900;
    $month = isset($params['month']) && $params['month'] != '00' ? $params['month'] : false;
    $day = isset($params['day']) && $params['day'] != '00' ? $params['day'] : false;
    $dob = $month && $day ? date('Y-m-d', strtotime( $year.'-'.$month.'-'.$day ) ) : false;
    if ( $dob ) $contact_data["BirthDate"] = $dob;

    $phone = isset($params['phone']) && !empty($params['phone']) ? $params['phone'] : false;
    if ( $phone ) $contact_data['Phone'] = $phone;

    // --- Company details
    $contact_data['Company'] = array();
    $postcode = isset($params['postcode']) && !empty($params['postcode']) ? $params['postcode'] : false;
    $company = isset($params['company']) && !empty($params['company']) ? $params['company'] : false;
    $company_size = isset($params['company_size']) && !empty($params['company_size']) ? $params['company_size'] : false;

    if ( $postcode ) $contact_data['Company']['PostCode'] = $postcode;
    if ( $company ) $contact_data['Company']['Name'] = $company;
    if ( $company_size ) $contact_data['Company']['SupInfo'][] = array( 'FieldName' => 'Company size', 'FieldContent' => $company_size );

    // === custom origin - ACF block custom field
    /**
     * $is_fbh : check if this is the hidden variant of signup
     * $custom_origin : this is the custom value added in the wp block allowing you to set different origins; this is added to the source
     */
    $is_fbh = isset($params['origin']) && !empty($params['origin']) ?: false;
    $custom_origin = $is_fbh ? trim($params['origin']) : '';

    // === Extra UTM campaigns data
    if( isset($params['MobilPhone']) ) $contact_data['MobilPhone'] = str_replace(' ','',trim($params['MobilPhone']));

    /*
     * this applies to the Source ID
     * if there is a booking ID, label the origin as booking, otherwise label as sign up
     * if there is a transaction id, label the origin guestline
     * if there is a utm campaign source, label it as utm campaign source
     * if there is no utm campaign source, label it as other
     */

    $origin = isset( $params['bookingId']) && !empty( $params['bookingId']) ? 'booking' : 'sign up';
    if( isset($params['transaction_id']) && !empty($params['transaction_id']) ) $origin = 'GL';
    if( isset($params['utmSource']) && !empty($params['utmSource']) ) {
      $utmSource = $params['utmSource'];
    }else {
      $utmSource = 'other';
    }

    /*
     * read utm parameters
     */

    if( isset($params['utmMedium']) && !empty($params['utmMedium']) ) { $utmMedium = $params['utmMedium']; }
    if( isset($params['utmCampaign']) && !empty($params['utmCampaign']) ) { $utmCampaign = $params['utmCampaign']; }
    if( isset($params['utmTerm']) && !empty($params['utmTerm']) ) { $utmTerm = $params['utmTerm']; }
    if( isset($params['utmContent']) && !empty($params['utmContent']) ) { $utmContent = $params['utmContent']; }
    if( isset($params['promocode']) && !empty($params['promocode']) ) { $promocode = $params['promocode']; }

    /*
     * make the source id using the recipe above
     */

    if( $is_fbh ) {
      $contact_data['SourceID'] .= ' - ' . $custom_origin . ' - ' .  $origin;
    }else {
      $contact_data['SourceID'] .= ' - ' . $utmSource . ' - ' .  $origin;
    }

    $contact_data['SupInfo'][] = array('FieldName'=>'Brand', 'FieldContent'=>'WBC');

    /*
     * add booking parameters to supinfo if they exist, based on avaialable booking info
     */

    if( isset($params['bookingId']) ) $contact_data['SupInfo'][] = array( 'FieldName' => 'Booking reference', 'FieldContent' => $params['bookingId'] );
    if( isset($params['restName']) ) $contact_data['SupInfo'][] = array( 'FieldName' => 'Booking Site', 'FieldContent' => 'WBC '.$params['restName'] );
    if( isset($params['bookingType']) ) $contact_data['SupInfo'][] = array( 'FieldName' => 'Booking Type', 'FieldContent' => $params['bookingType'] );
    if( isset($params['bookingOccassion']) ) $contact_data['SupInfo'][] = array( 'FieldName' => 'Booking Occassion', 'FieldContent' => $params['bookingOccassion'] );
    if( isset($params['bookingArea']) ) $contact_data['SupInfo'][] = array( 'FieldName' => 'Booking Area', 'FieldContent' => $params['bookingArea'] );
    if( isset($params['bookingMenu']) ) $contact_data['SupInfo'][] = array( 'FieldName' => 'Booking Menu', 'FieldContent' => $params['bookingMenu'] );
    // --- Guestline booking ID
    if( isset($params['transaction_id']) ) $contact_data['SupInfo'][] = array( 'FieldName' => 'Guestline Booking reference', 'FieldContent'=> $params['transaction_id']);

    if( isset($params['bookingDate']) && isset($params['bookingTime']) ) {
      $contact_data['SupInfo'][] = array( 'FieldName' => 'Booking Date', 'FieldContent' => $params['bookingDate'] . ' ' . $params['bookingTime'] );
    }

    /*
     * add utm parameters to supinfo if they exist, based on avaialable utm campaign link
     */

    if( $utmSource )    $contact_data['SupInfo'][] = array( 'FieldName' => 'UTM Source', 'FieldContent' => $utmSource );
    if( $utmMedium )    $contact_data['SupInfo'][] = array( 'FieldName' => 'UTM Medium', 'FieldContent' => $utmMedium );
    if( $utmCampaign )  $contact_data['SupInfo'][] = array( 'FieldName' => 'UTM Name', 'FieldContent' => $utmCampaign );
    if( $utmTerm )      $contact_data['SupInfo'][] = array( 'FieldName' => 'UTM Term', 'FieldContent' => $utmTerm );
    if( $utmContent )   $contact_data['SupInfo'][] = array( 'FieldName' => 'UTM Content', 'FieldContent' => $utmContent );
    if( $promocode )    $contact_data['SupInfo'][] = array( 'FieldName' => 'Promocode', 'FieldContent' => $promocode );

    if( isset($params['bookingId']) ) { // --- Liveres booking signup
      $default_prefs = array(
        "EmailOptin"  => filter_var($params['EmailOptIn'], FILTER_VALIDATE_BOOLEAN) ?: null,
        "SmsOptin" => filter_var($params['SmsOptIn'], FILTER_VALIDATE_BOOLEAN) ?: null
      );
    }else {
      $default_prefs = array(
        "EmailOptin"  => true
      );
    }
    $data_prefs = $default_prefs;

     // ====== START: /api/Customer/Post data =====
     $customer_data = array(
      "PersonalInfo" => array(
        "FirstName"     => $params['firstname'],
        "LastName"      => $params['surname'],
        "Email"         => $params['email'],
      ),
      "ExternalIdentifier" => array(
        // "ExternalID" => $contact_data["SourceID"]
        "ExternalSource" => $contact_data["SourceID"]
      ),
      "SelectedLocationID" => $venue_ref,
      "PreferredLocationID" => $venue_ref,
      "MarketingOptin" => $default_prefs,
      "SupInfo" => $contact_data["SupInfo"]
      // "BrandID" => $venue_ref,
      // "ProcessMydata" => true,
    );
    if ( $dob ) $customer_data["PersonalInfo"]["BirthDate"] = $dob;
    if ( $phone ) $customer_data["PersonalInfo"]["Phone"] = $phone;
    if( isset($params['MobilPhone']) ) $customer_data["PersonalInfo"]['MobilePhone'] = str_replace(' ','',trim($params['MobilPhone']));
    if ( $postcode ) $customer_data["PersonalInfo"]["CustomerAddress"]["PostCode"] = $postcode;
    if ( $company ) $customer_data['CorporateDetails']['CorporateName'] = $company;
    // ====== END: /api/Customer/Post data =====


    // ========== APi call ==========
    // --- get API instance
    $atreemo = class_exists('AtreemoAPI') ? new AtreemoAPI() : false;
    if( !$atreemo ) {
      return rest_ensure_response(array(
        'message' => 'Sign up service unavailable at the moment, please try again.',
        'code' => '503'
      ));
    }

    // --- switch API method and set request_data
    switch ($api_method) {
      case 'new':
        $request_data = $customer_data;
        $add_result = $atreemo->addCustomer($request_data); // NEW methos
        break;
      default: // OLD method
        $request_data = $contact_data;
        $add_result = $atreemo->addContact($request_data); // OLD method
        break;
    }

    // --- check API response
    if(
      ( $api_method == 'old' && $add_result ) || // OLD method
      ( $api_method == 'new' && $add_result['ResponseStatus'] ) ) // NEW method
    { // --- create new contact in DB
      $CtcID = $api_method == 'new' ? $add_result['ResponseData']['CtcID'] : $add_result['CtcID'];
      $atreemo_data['code'] = 201;
      $atreemo_data['message'] = "Thank you for signing up to our online community. We will be in touch with news and exclusive offers in the near future. Watch your inbox!";

      // === If user created and OLD API call, then update his communication Preferences
      if( $api_method == 'old' && $add_result ) {
        $data_prefs["CustomerID"] = $add_result['CtcID'];
        $data_prefs["SourceID" ] = $contact_data['SourceID'];
        $prefs_result = $atreemo->updatePreferences($data_prefs);
      }

      $atreemo_data['final_user'] = $atreemo->getContactByID( $CtcID );

      // --- add "Book Table" button to message where available
      if( $venue_ref ) {
        $disable_booking = isset($params['disable_booking']);
        if( !$disable_booking && $booking_url ) $atreemo_data['message'] .= '<span class="d-block"></span><a href="/book-a-table/" class="btn btn-outline-dusk" style="margin-top:1rem;">Book a Table</a><span>';
      }
    }else {
      $atreemo_data['message'] = 'Something went wrong, please try again.';
    }

    // --- get final user created, save to response

    // --- save data to response
    $atreemo_data['api_method'] = $api_method;
    $atreemo_data['request'] = $request_data;
    $atreemo_data['add_result'] = $add_result;
    $atreemo_data['user_id'] = $CtcID;
    $atreemo_data['data_prefs'] = $data_prefs;
    $atreemo_data['prefs_result'] = $prefs_result;
    $atreemo_data['action'] = $params['action'];

    return rest_ensure_response($atreemo_data);

  }

  /**
 * /locations/v1/search
 * Callback function
 */
function rest_locations_search(WP_REST_Request $request) {

  $params = $request->get_body_params();
  $code = 200;
  $message = 'All good';

  // --- Auth connection
  if( !isset($params['api_secret']) || $params['api_secret'] != PREVIEW_SECRET_TOKEN ) {
    return rest_ensure_response( array(
      'code' => 401,
      'message' => 'Authorization needed.'
    ) );
  }

  // --- Debug only
  // return rest_ensure_response(
  //   array(
  //     'code' => 200,
  //     'message' => 'TESTING',
  //     'data' => $params
  //   )
  // );

  $destination = $params['destination'];
  $results = array();
  list($options, $restaurants) = getLocationPreviews();
  $origin = '';
  $dist_found = 0;

  if( !$restaurants ) {
    return rest_ensure_response(
        array(
          'code' => 404,
          'message' => 'TESTING',
          'data' => $params
        )
      );
  }

  foreach( $restaurants as $rest ) {

    $rest_postcode = $rest['address_short'];
    if( $rest_postcode ) {
      $geo = GetDrivingDistance( $destination, $rest_postcode );
      if( isset($geo['distance']) ) {
        $rest['dist'] = $geo['distance'];
        $rest['dist_value'] = $geo['value'];
        if( $geo['origin'] ) $origin = $geo['origin'];
        $dist_found++;
      }
      $results[] = $rest;
    }

  }

  // === If not distances found return normal restaurnts array
  if( $dist_found == 0 ) {
    $code = 404;
    $message = "Sorry, we couldn't find your location, please double check and submit again.";
  }

  // === sort array by distance
  usort($results, function($a, $b)
  {
      return $a['dist_value'] > $b['dist_value'];
  });

  return rest_ensure_response(array(
    'code'=> $code,
    'message' => $message,
    'markers' => $results,
    'origin' => $origin,
    'response' => $geo['response']
  ));
}