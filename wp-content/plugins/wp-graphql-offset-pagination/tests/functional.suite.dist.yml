actor: FunctionalTester
modules:
    enabled:
        - REST
        - WPDb
        - WPBrowser
        - Asserts
        - \Helper\Functional
    disabled:
        - WPLoader

    config:
        REST:
            depends: WPBrowser
            url: "http://%WPTT_SITE_HOST%"
        WPBrowser:
            url: "http://%WPTT_SITE_HOST%"
            adminUsername: "%WPTT_SITE_ADMIN_USERNAME%"
            adminPassword: "%WPTT_SITE_ADMIN_PASSWORD%"
            adminPath: "/wp-admin"
        WPDb:
            dsn: "mysql:host=%WPTT_DB_HOST%;dbname=%WPTT_DB_NAME%"
            user: %WPTT_DB_USER%
            password: %WPTT_DB_PASSWORD%
            dump: %WPTT_INSTALL_DIR%/dump.sql
            populate: true
            cleanup: true
            url: "http://%WPTT_SITE_HOST%"
            tablePrefix: wp_
