name: Run tests

on: [push, pull_request]

jobs:
    test:
        services:
            mariadb:
                image: mariadb
                ports:
                    - 3306:3306
                env:
                    MYSQL_ROOT_PASSWORD: root
                options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v1

            - uses: actions/cache@v1
              id: cache-composer
              with:
                  path: ~/.composer/cache
                  key: ${{ runner.os }}-composer

            - uses: actions/cache@v1
              id: cache-wp-cli
              with:
                  path: ~/.wp-cli/cache
                  key: ${{ runner.os }}-wp-cli

            - name: Run tests
              run: .github/run-tests
